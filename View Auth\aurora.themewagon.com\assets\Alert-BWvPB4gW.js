import{cj as d,j as e,r as D,cb as q,en as n,aV as g,c5 as G,n as J,eo as K,aF as x,c6 as Q,ep as X,P as Y,cd as _,cg as M,cf as I,ce as v,bp as P}from"./index-CP4gzJXp.js";const tt=d(e.jsx("path",{d:"M20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4C12.76,4 13.5,4.11 14.2, 4.31L15.77,2.74C14.61,2.26 13.34,2 12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0, 0 22,12M7.91,10.08L6.5,11.5L11,16L21,6L19.59,4.58L11,13.17L7.91,10.08Z"})),ot=d(e.jsx("path",{d:"M12 5.99L19.53 19H4.47L12 5.99M12 2L1 21h22L12 2zm1 14h-2v2h2v-2zm0-6h-2v4h2v-4z"})),et=d(e.jsx("path",{d:"M11 15h2v2h-2zm0-8h2v6h-2zm.99-5C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8z"})),st=d(e.jsx("path",{d:"M11,9H13V7H11M12,20C7.59,20 4,16.41 4,12C4,7.59 7.59,4 12,4C16.41,4 20,7.59 20, 12C20,16.41 16.41,20 12,20M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10, 10 0 0,0 12,2M11,17H13V11H11V17Z"})),at=t=>{const{variant:s,color:a,severity:o,classes:i}=t,u={root:["root",`color${x(a||o)}`,`${s}${x(a||o)}`,`${s}`],icon:["icon"],message:["message"],action:["action"]};return Q(u,X,i)},lt=g(Y,{name:"MuiAlert",slot:"Root",overridesResolver:(t,s)=>{const{ownerState:a}=t;return[s.root,s[a.variant],s[`${a.variant}${x(a.color||a.severity)}`]]}})(_(({theme:t})=>{const s=t.palette.mode==="light"?M:I,a=t.palette.mode==="light"?I:M;return{...t.typography.body2,backgroundColor:"transparent",display:"flex",padding:"6px 16px",variants:[...Object.entries(t.palette).filter(v(["light"])).map(([o])=>({props:{colorSeverity:o,variant:"standard"},style:{color:t.vars?t.vars.palette.Alert[`${o}Color`]:s(t.palette[o].light,.6),backgroundColor:t.vars?t.vars.palette.Alert[`${o}StandardBg`]:a(t.palette[o].light,.9),[`& .${P.icon}`]:t.vars?{color:t.vars.palette.Alert[`${o}IconColor`]}:{color:t.palette[o].main}}})),...Object.entries(t.palette).filter(v(["light"])).map(([o])=>({props:{colorSeverity:o,variant:"outlined"},style:{color:t.vars?t.vars.palette.Alert[`${o}Color`]:s(t.palette[o].light,.6),border:`1px solid ${(t.vars||t).palette[o].light}`,[`& .${P.icon}`]:t.vars?{color:t.vars.palette.Alert[`${o}IconColor`]}:{color:t.palette[o].main}}})),...Object.entries(t.palette).filter(v(["dark"])).map(([o])=>({props:{colorSeverity:o,variant:"filled"},style:{fontWeight:t.typography.fontWeightMedium,...t.vars?{color:t.vars.palette.Alert[`${o}FilledColor`],backgroundColor:t.vars.palette.Alert[`${o}FilledBg`]}:{backgroundColor:t.palette.mode==="dark"?t.palette[o].dark:t.palette[o].main,color:t.palette.getContrastText(t.palette[o].main)}}}))]}})),rt=g("div",{name:"MuiAlert",slot:"Icon"})({marginRight:12,padding:"7px 0",display:"flex",fontSize:22,opacity:.9}),nt=g("div",{name:"MuiAlert",slot:"Message"})({padding:"8px 0",minWidth:0,overflow:"auto"}),it=g("div",{name:"MuiAlert",slot:"Action"})({display:"flex",alignItems:"flex-start",padding:"4px 0 0 16px",marginLeft:"auto",marginRight:-8}),$={success:e.jsx(tt,{fontSize:"inherit"}),warning:e.jsx(ot,{fontSize:"inherit"}),error:e.jsx(et,{fontSize:"inherit"}),info:e.jsx(st,{fontSize:"inherit"})},pt=D.forwardRef(function(s,a){const o=q({props:s,name:"MuiAlert"}),{action:i,children:u,className:z,closeText:A="Close",color:C,components:f={},componentsProps:b={},icon:S,iconMapping:h=$,onClose:y,role:L="alert",severity:c="success",slotProps:k={},slots:B={},variant:R="standard",...T}=o,l={...o,color:C,severity:c,variant:R,colorSeverity:C||c},p=at(l),r={slots:{closeButton:f.CloseButton,closeIcon:f.CloseIcon,...B},slotProps:{...b,...k}},[w,F]=n("root",{ref:a,shouldForwardComponentProp:!0,className:G(p.root,z),elementType:lt,externalForwardedProps:{...r,...T},ownerState:l,additionalProps:{role:L,elevation:0}}),[O,H]=n("icon",{className:p.icon,elementType:rt,externalForwardedProps:r,ownerState:l}),[N,V]=n("message",{className:p.message,elementType:nt,externalForwardedProps:r,ownerState:l}),[m,j]=n("action",{className:p.action,elementType:it,externalForwardedProps:r,ownerState:l}),[E,W]=n("closeButton",{elementType:J,externalForwardedProps:r,ownerState:l}),[U,Z]=n("closeIcon",{elementType:K,externalForwardedProps:r,ownerState:l});return e.jsxs(w,{...F,children:[S!==!1?e.jsx(O,{...H,children:S||h[c]||$[c]}):null,e.jsx(N,{...V,children:u}),i!=null?e.jsx(m,{...j,children:i}):null,i==null&&y?e.jsx(m,{...j,children:e.jsx(E,{size:"small","aria-label":A,title:A,color:"inherit",onClick:y,...W,children:e.jsx(U,{fontSize:"small",...Z})})}):null]})});export{pt as A};
