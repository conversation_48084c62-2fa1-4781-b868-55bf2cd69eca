import{bb as e,j as i}from"./index-CP4gzJXp.js";import{F as m}from"./ForgotPasswordForm-DMGeYAXM.js";import"./index.esm-Bw9oClnr.js";import"./yup-Bdh5_3QG.js";import"./useCountdown-BEaApQg6.js";import"./index.esm-CVsSWzb0.js";import"./DialogContent-C824DSl1.js";import"./dialogTitleClasses-XEjvqc9B.js";import"./Alert-BWvPB4gW.js";import"./DialogTitle-jz6gg1km.js";import"./DialogContentText-saOMJNKn.js";import"./ViewOnlyAlert-CkXljFy_.js";const h=()=>{const{trigger:r}=e(),t=async o=>await r(o).catch(s=>{throw new Error(s.data.message)});return i.jsx(m,{handleSendResetLink:t})};export{h as default};
