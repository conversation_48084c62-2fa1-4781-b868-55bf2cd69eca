import{r as l,j as s,a0 as d,I as c,n as x,d as i}from"./index-CP4gzJXp.js";const u=({ref:o,...e})=>{const[t,n]=l.useState(!1),r=a=>{a.preventDefault(),n(!t)};return s.jsx(d,{type:t?"text":"password",ref:o,slotProps:{input:{endAdornment:s.jsx(c,{position:"end",children:s.jsx(x,{onClick:r,children:t?s.jsx(i,{icon:"material-symbols-light:visibility-outline-rounded"}):s.jsx(i,{icon:"material-symbols-light:visibility-off-outline-rounded"})})})}},...e})};export{u as P};
