import{g as u,s as d,d as g}from"./index.esm-Bw9oClnr.js";const m=(r,n,t)=>{if(r&&"reportValidity"in r){const e=u(t,n);r.setCustomValidity(e&&e.message||""),r.reportValidity()}},y=(r,n)=>{for(const t in n.fields){const e=n.fields[t];e&&e.ref&&"reportValidity"in e.ref?m(e.ref,t,r):e&&e.refs&&e.refs.forEach(f=>m(f,t,r))}},V=(r,n)=>{n.shouldUseNativeValidation&&y(r,n);const t={};for(const e in r){const f=u(n.fields,e),i=Object.assign(r[e]||{},{ref:f&&f.ref});if(b(n.names||Object.keys(r),e)){const a=Object.assign({},u(t,e));d(a,"root",i),d(t,e,a)}else d(t,e,i)}return t},b=(r,n)=>{const t=v(n);return r.some(e=>v(e).match(`^${t}\\.\\d+`))};function v(r){return r.replace(/\]|\[/g,"")}function O(r,n,t){return n===void 0&&(n={}),t===void 0&&(t={}),function(e,f,i){try{return Promise.resolve(function(a,l){try{var c=(n.context,Promise.resolve(r[t.mode==="sync"?"validateSync":"validate"](e,Object.assign({abortEarly:!1},n,{context:f}))).then(function(o){return i.shouldUseNativeValidation&&y({},i),{values:t.raw?Object.assign({},e):o,errors:{}}}))}catch(o){return l(o)}return c&&c.then?c.then(void 0,l):c}(0,function(a){if(!a.inner)throw a;return{values:{},errors:V((l=a,c=!i.shouldUseNativeValidation&&i.criteriaMode==="all",(l.inner||[]).reduce(function(o,s){if(o[s.path]||(o[s.path]={message:s.message,type:s.type}),c){var p=o[s.path].types,h=p&&p[s.type];o[s.path]=g(s.path,c,o,s.type,h?[].concat(h,s.message):s.message)}return o},{})),i)};var l,c}))}catch(a){return Promise.reject(a)}}}export{O as o};
