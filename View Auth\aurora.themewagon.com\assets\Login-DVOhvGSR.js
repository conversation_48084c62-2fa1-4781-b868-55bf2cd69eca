import{aG as n,b6 as m,j as p,b8 as d,aB as o,be as u,bf as l,b9 as c}from"./index-CP4gzJXp.js";import{L as f}from"./LoginForm-0FAfm8GS.js";import"./index.esm-Bw9oClnr.js";import"./yup-Bdh5_3QG.js";import"./index.esm-CVsSWzb0.js";import"./PasswordTextField-Bu7nMFM0.js";import"./Alert-BWvPB4gW.js";import"./ViewOnlyAlert-CkXljFy_.js";import"./SocialAuth-DUKTxlfk.js";const P=()=>{const e=n(),{setSession:i}=m(),r=async a=>{const t=await u(l,a.email,a.password).catch(s=>{console.log({error:s})});if(t){const s=t.user;i({id:s.uid,name:s.displayName,email:s.email,avatar:s.photoURL},s.accessToken),e(c.root)}};return p.jsx(f,{provider:"firebase",handleLogin:r,signUpLink:o.defaultFirebaseSignup,forgotPasswordLink:o.defaultFirebaseForgotPassword,defaultCredential:d})};export{P as default};
