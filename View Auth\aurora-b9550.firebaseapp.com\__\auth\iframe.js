/*! @license Firebase v3.7.5
    Build: 3.7.5-rc.1
    Terms: https://firebase.google.com/terms/ */
var firebase = null; (function() { var aa=function(a){var b=0;return function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}}},l=typeof Object.defineProperties=="function"?Object.defineProperty:function(a,b,c){if(a==Array.prototype||a==Object.prototype)return a;a[b]=c.value;return a},ba=function(a){a=["object"==typeof globalThis&&globalThis,a,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global];for(var b=0;b<a.length;++b){var c=a[b];if(c&&c.Math==Math)return c}throw Error("Cannot find global object");
},n=ba(this),ca=function(a,b){if(b)a:{var c=n;a=a.split(".");for(var d=0;d<a.length-1;d++){var e=a[d];if(!(e in c))break a;c=c[e]}a=a[a.length-1];d=c[a];b=b(d);b!=d&&b!=null&&l(c,a,{configurable:!0,writable:!0,value:b})}};
ca("Symbol",function(a){if(a)return a;var b=function(g,k){this.V=g;l(this,"description",{configurable:!0,writable:!0,value:k})};b.prototype.toString=function(){return this.V};var c="jscomp_symbol_"+(Math.random()*1E9>>>0)+"_",d=0,e=function(g){if(this instanceof e)throw new TypeError("Symbol is not a constructor");return new b(c+(g||"")+"_"+d++,g)};return e});
var p=function(a){var b=typeof Symbol!="undefined"&&Symbol.iterator&&a[Symbol.iterator];if(b)return b.call(a);if(typeof a.length=="number")return{next:aa(a)};throw Error(String(a)+" is not an iterable or ArrayLike");},q=function(a){for(var b,c=[];!(b=a.next()).done;)c.push(b.value);return c},da=function(){for(var a=Number(this),b=[],c=a;c<arguments.length;c++)b[c-a]=arguments[c];return b};/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
var r=this||self,ea=function(a,b){var c=Array.prototype.slice.call(arguments,1);return function(){var d=c.slice();d.push.apply(d,arguments);return a.apply(this,d)}},u=function(a,b){function c(){}c.prototype=b.prototype;a.ha=b.prototype;a.prototype=new c;a.prototype.constructor=a;a.base=function(d,e,g){for(var k=Array(arguments.length-2),f=2;f<arguments.length;f++)k[f-2]=arguments[f];return b.prototype[e].apply(d,k)}};var v=typeof AsyncContext!=="undefined"&&typeof AsyncContext.Snapshot==="function"?function(a){return a&&AsyncContext.Snapshot.wrap(a)}:function(a){return a};function w(a,b){if(Error.captureStackTrace)Error.captureStackTrace(this,w);else{var c=Error().stack;c&&(this.stack=c)}a&&(this.message=String(a));b!==void 0&&(this.cause=b)}u(w,Error);w.prototype.name="CustomError";function x(a,b){a=a.split("%s");for(var c="",d=a.length-1,e=0;e<d;e++)c+=a[e]+(e<b.length?b[e]:"%s");w.call(this,c+a[d])}u(x,w);x.prototype.name="AssertionError";function z(a,b,c,d){var e="Assertion failed";if(c){e+=": "+c;var g=d}else a&&(e+=": "+a,g=b);throw new x(""+e,g||[]);}
var A=function(a,b,c){a||z("",null,b,Array.prototype.slice.call(arguments,2))},fa=function(a,b,c){a==null&&z("Expected to exist: %s.",[a],b,Array.prototype.slice.call(arguments,2));return a},B=function(a,b,c){if(typeof a!=="function"){var d=typeof a;d=d!="object"?d:a?Array.isArray(a)?"array":d:"null";z("Expected function but got %s: %s.",[d,a],b,Array.prototype.slice.call(arguments,2))}};var C=function(a,b){this.aa=100;this.W=a;this.ba=b;this.F=0;this.D=null};C.prototype.get=function(){if(this.F>0){this.F--;var a=this.D;this.D=a.next;a.next=null}else a=this.W();return a};C.prototype.put=function(a){this.ba(a);this.F<this.aa&&(this.F++,a.next=this.D,this.D=a)};function ha(a){r.setTimeout(function(){throw a;},0)};var D=function(){this.G=this.o=null};D.prototype.add=function(a,b){var c=ia.get();c.set(a,b);this.G?this.G.next=c:(A(!this.o),this.o=c);this.G=c};D.prototype.remove=function(){var a=null;this.o&&(a=this.o,this.o=this.o.next,this.o||(this.G=null),a.next=null);return a};var ia=new C(function(){return new E},function(a){return a.reset()}),E=function(){this.next=this.scope=this.fn=null};E.prototype.set=function(a,b){this.fn=a;this.scope=b;this.next=null};
E.prototype.reset=function(){this.next=this.scope=this.fn=null};var F=r.console&&r.console.createTask?r.console.createTask.bind(r.console):void 0,G=F?Symbol("consoleTask"):void 0;
function H(a,b){function c(){var f=da.apply(0,arguments),h=this;return k.run(function(){return a.call.apply(a,[h].concat(f instanceof Array?f:q(p(f))))})}b=b===void 0?"anonymous":b;if(G&&a[G])return a;var d=a,e,g=(e=ja)==null?void 0:e();a=function(){var f=da.apply(0,arguments),h,m=(h=ja)==null?void 0:h();if(g!==m)throw Error(b+" was scheduled in '"+g+"' but called in '"+m+"'.\nMake sure your test awaits all async calls.\n\nTIP: To help investigate, debug the test in Chrome and look at the async portion\nof the call stack to see what originally scheduled the callback.  Then, make the\ntest wait for the relevant asynchronous work to finish.");return d.call.apply(d,
[this].concat(f instanceof Array?f:q(p(f))))};if(!F)return a;var k=F(a.name||b);c[fa(G)]=k;return c}var ja;var I,J=!1,ka=new D,K=function(a,b){a=H(a,"goog.async.run");I||la();J||(I(),J=!0);ka.add(a,b)},la=function(){var a=Promise.resolve(void 0);I=function(){a.then(ma)}};function ma(){for(var a;a=ka.remove();){try{a.fn.call(a.scope)}catch(b){ha(b)}ia.put(a)}J=!1};var L=function(){};var O=function(a,b){this.g=0;this.T=void 0;this.s=this.i=this.m=null;this.B=this.H=!1;if(a!=L)try{var c=this;a.call(b,function(d){M(c,2,d)},function(d){if(!(d instanceof N))try{if(d instanceof Error)throw d;throw Error("Promise rejected.");}catch(e){}M(c,3,d)})}catch(d){M(this,3,d)}},na=function(){this.next=this.context=this.u=this.l=this.child=null;this.v=!1};na.prototype.reset=function(){this.context=this.u=this.l=this.child=null;this.v=!1};
var oa=new C(function(){return new na},function(a){a.reset()}),pa=function(a,b,c){var d=oa.get();d.l=a;d.u=b;d.context=c;return d},ra=function(a,b,c){qa(a,b,c,null)||K(ea(b,a))};O.prototype.then=function(a,b,c){a!=null&&B(a,"opt_onFulfilled should be a function.");b!=null&&B(b,"opt_onRejected should be a function. Did you pass opt_context as the second argument instead of the third?");return sa(this,v(typeof a==="function"?a:null),v(typeof b==="function"?b:null),c)};O.prototype.$goog_Thenable=!0;
var ua=function(a,b,c,d){b!=null&&B(b,"opt_onFulfilled should be a function.");c!=null&&B(c,"opt_onRejected should be a function. Did you pass opt_context as the second argument instead of the third?");ta(a,pa(b||L,c||null,d))};O.prototype.finally=function(a){var b=this;a=v(a);return new Promise(function(c,d){ua(b,function(e){a();c(e)},function(e){a();d(e)})})};O.prototype.U=function(a,b){return sa(this,null,v(a),b)};O.prototype.catch=O.prototype.U;
O.prototype.cancel=function(a){if(this.g==0){var b=new N(a);K(function(){va(this,b)},this)}};
var va=function(a,b){if(a.g==0)if(a.m){var c=a.m;if(c.i){for(var d=0,e=null,g=null,k=c.i;k&&(k.v||(d++,k.child==a&&(e=k),!(e&&d>1)));k=k.next)e||(g=k);e&&(c.g==0&&d==1?va(c,b):(g?(d=g,A(c.i),A(d!=null),d.next==c.s&&(c.s=d),d.next=d.next.next):wa(c),xa(c,e,3,b)))}a.m=null}else M(a,3,b)},ta=function(a,b){a.i||a.g!=2&&a.g!=3||ya(a);A(b.l!=null);a.s?a.s.next=b:a.i=b;a.s=b},sa=function(a,b,c,d){b&&(b=H(b,"goog.Promise.then"));c&&(c=H(c,"goog.Promise.then"));var e=pa(null,null,null);e.child=new O(function(g,
k){e.l=b?function(f){try{var h=b.call(d,f);g(h)}catch(m){k(m)}}:g;e.u=c?function(f){try{var h=c.call(d,f);h===void 0&&f instanceof N?k(f):g(h)}catch(m){k(m)}}:k});e.child.m=a;ta(a,e);return e.child};O.prototype.ea=function(a){A(this.g==1);this.g=0;M(this,2,a)};O.prototype.fa=function(a){A(this.g==1);this.g=0;M(this,3,a)};
var M=function(a,b,c){a.g==0&&(a===c&&(b=3,c=new TypeError("Promise cannot resolve to itself")),a.g=1,qa(c,a.ea,a.fa,a)||(a.T=c,a.g=b,a.m=null,ya(a),b!=3||c instanceof N||za(a,c)))},qa=function(a,b,c,d){if(a instanceof O)return ua(a,b,c,d),!0;if(a)try{var e=!!a.$goog_Thenable}catch(k){e=!1}else e=!1;if(e)return a.then(b,c,d),!0;e=typeof a;if(e=="object"&&a!=null||e=="function")try{var g=a.then;if(typeof g==="function")return Aa(a,g,b,c,d),!0}catch(k){return c.call(d,k),!0}return!1},Aa=function(a,
b,c,d,e){var g=!1,k=function(h){g||(g=!0,c.call(e,h))},f=function(h){g||(g=!0,d.call(e,h))};try{b.call(a,k,f)}catch(h){f(h)}},ya=function(a){a.H||(a.H=!0,K(a.X,a))},wa=function(a){var b=null;a.i&&(b=a.i,a.i=b.next,b.next=null);a.i||(a.s=null);b!=null&&A(b.l!=null);return b};O.prototype.X=function(){for(var a;a=wa(this);)xa(this,a,this.g,this.T);this.H=!1};
var xa=function(a,b,c,d){if(c==3&&b.u&&!b.v)for(;a&&a.B;a=a.m)a.B=!1;if(b.child)b.child.m=null,Ba(b,c,d);else try{b.v?b.l.call(b.context):Ba(b,c,d)}catch(e){Ca.call(null,e)}oa.put(b)},Ba=function(a,b,c){b==2?a.l.call(a.context,c):a.u&&a.u.call(a.context,c)},za=function(a,b){a.B=!0;K(function(){a.B&&Ca.call(null,b)})},Ca=ha,N=function(a){w.call(this,a)};u(N,w);N.prototype.name="cancel";O.all=function(a){return new O(function(b,c){var d=a.length,e=[];if(d)for(var g=function(m,t){d--;e[m]=t;d==0&&b(e)},k=function(m){c(m)},f,h=0;h<a.length;h++)f=a[h],ra(f,ea(g,h),k);else b(e)})};O.resolve=function(a){if(a instanceof O)return a;var b=new O(L);M(b,2,a);return b};O.reject=function(a){return new O(function(b,c){c(a)})};O.prototype["catch"]=O.prototype.U;var P=O;typeof Promise!=="undefined"&&(P=Promise);function Q(a,b){if(!(b instanceof Object))return b;switch(b.constructor){case Date:return new Date(b.getTime());case Object:a===void 0&&(a={});break;case Array:a=[];break;default:return b}for(var c in b)b.hasOwnProperty(c)&&(a[c]=Q(a[c],b[c]));return a};var Da=Error.captureStackTrace,S=function(a,b){this.code=a;this.message=b;if(Da)Da(this,R.prototype.create);else{var c=Error.apply(this,arguments);this.name="FirebaseError";Object.defineProperty(this,"stack",{get:function(){return c.stack}})}};S.prototype.constructor=S;S.prototype.name="FirebaseError";var R=function(a,b,c){this.service=a;this.da=b;this.errors=c;this.pattern=/\{\$([^}]+)}/g};
R.prototype.create=function(a,b){b===void 0&&(b={});var c=this.errors[a];a=this.service+"/"+a;c=c===void 0?"Error":c.replace(this.pattern,function(e,g){e=b[g];return e!==void 0?e.toString():"<"+g+"?>"});c=this.da+": "+c+" ("+a+").";c=new S(a,c);for(var d in b)b.hasOwnProperty(d)&&d.slice(-1)!=="_"&&(c[d]=b[d]);return c};var Ea=P;function Fa(a,b){a=new T(a,b);return a.subscribe.bind(a)}var T=function(a,b){var c=this;this.h=[];this.S=0;this.task=Ea.resolve();this.A=!1;this.J=b;this.task.then(function(){a(c)}).catch(function(d){c.error(d)})};T.prototype.next=function(a){U(this,function(b){b.next(a)})};T.prototype.error=function(a){U(this,function(b){b.error(a)});this.close(a)};T.prototype.complete=function(){U(this,function(a){a.complete()});this.close()};
T.prototype.subscribe=function(a,b,c){var d=this;if(a===void 0&&b===void 0&&c===void 0)throw Error("Missing Observer.");var e=Ga(a)?a:{next:a,error:b,complete:c};e.next===void 0&&(e.next=V);e.error===void 0&&(e.error=V);e.complete===void 0&&(e.complete=V);a=this.ga.bind(this,this.h.length);this.A&&this.task.then(function(){try{d.M?e.error(d.M):e.complete()}catch(g){}});this.h.push(e);return a};
T.prototype.ga=function(a){this.h!==void 0&&this.h[a]!==void 0&&(delete this.h[a],--this.S,this.S===0&&this.J!==void 0&&this.J(this))};var U=function(a,b){if(!a.A)for(var c=0;c<a.h.length;c++)Ha(a,c,b)},Ha=function(a,b,c){a.task.then(function(){if(a.h!==void 0&&a.h[b]!==void 0)try{c(a.h[b])}catch(d){typeof console!=="undefined"&&console.error&&console.error(d)}})};T.prototype.close=function(a){var b=this;this.A||(this.A=!0,a!==void 0&&(this.M=a),this.task.then(function(){b.h=void 0;b.J=void 0}))};
function Ga(a){if(typeof a!=="object"||a===null)return!1;for(var b=p(["next","error","complete"]),c=b.next();!c.done;c=b.next())if(c=c.value,c in a&&typeof a[c]==="function")return!0;return!1}function V(){};var W=P,X=function(a,b,c){var d=this;this.P=c;this.R=!1;this.j={};this.I=b;this.K=Q(void 0,a);a="serviceAccount"in this.K;("credential"in this.K||a)&&typeof console!=="undefined"&&console.log("The '"+(a?"serviceAccount":"credential")+"' property specified in the first argument to initializeApp() is deprecated and will be removed in the next major version. You should instead use the 'firebase-admin' package. See https://firebase.google.com/docs/admin/setup for details on how to get started.");Object.keys(c.INTERNAL.factories).forEach(function(e){var g=
c.INTERNAL.useAsService(d,e);g!==null&&(g=d.Z.bind(d,g),d[e]=g)})};X.prototype.delete=function(){var a=this;return(new W(function(b){Y(a);b()})).then(function(){a.P.INTERNAL.removeApp(a.I);var b=[];Object.keys(a.j).forEach(function(c){Object.keys(a.j[c]).forEach(function(d){b.push(a.j[c][d])})});return W.all(b.filter(function(c){return"INTERNAL"in c}).map(function(c){return c.INTERNAL.delete()}))}).then(function(){a.R=!0;a.j={}})};
X.prototype.Z=function(a,b){Y(this);typeof this.j[a]==="undefined"&&(this.j[a]={});var c=b||"[DEFAULT]";return typeof this.j[a][c]==="undefined"?(b=this.P.INTERNAL.factories[a](this,this.Y.bind(this),b),this.j[a][c]=b):this.j[a][c]};X.prototype.Y=function(a){Q(this,a)};var Y=function(a){a.R&&Z("app-deleted",{name:a.I})};n.Object.defineProperties(X.prototype,{name:{configurable:!0,enumerable:!0,get:function(){Y(this);return this.I}},options:{configurable:!0,enumerable:!0,get:function(){Y(this);return this.K}}});
X.prototype.name&&X.prototype.options||X.prototype.delete||console.log("dc");
function Ia(){function a(f){f=f||"[DEFAULT]";var h=d[f];h===void 0&&Z("no-app",{name:f});return h}function b(f,h){Object.keys(e).forEach(function(m){m=c(f,m);if(m!==null&&g[m])g[m](h,f)})}function c(f,h){if(h==="serverAuth")return null;var m=h;f=f.options;h==="auth"&&(f.serviceAccount||f.credential)&&(m="serverAuth","serverAuth"in e||Z("sa-not-supported"));return m}var d={},e={},g={},k={__esModule:!0,initializeApp:function(f,h){h===void 0?h="[DEFAULT]":(typeof h!=="string"||h==="")&&Z("bad-app-name",
{name:h+""});d[h]!==void 0&&Z("duplicate-app",{name:h});f=new X(f,h,k);d[h]=f;b(f,"create");f.INTERNAL!=void 0&&f.INTERNAL.getToken!=void 0||Q(f,{INTERNAL:{getUid:function(){return null},getToken:function(){return W.resolve(null)},addAuthTokenListener:function(){},removeAuthTokenListener:function(){}}});return f},app:a,apps:null,Promise:W,SDK_VERSION:"0.0.0",INTERNAL:{registerService:function(f,h,m,t,Ja){e[f]&&Z("duplicate-service",{name:f});e[f]=Ja?h:function(y,Ka){return h(y,Ka,"[DEFAULT]")};t&&
(g[f]=t);t=function(y){y===void 0&&(y=a());typeof y[f]!=="function"&&Z("invalid-app-argument",{name:f});return y[f]()};m!==void 0&&Q(t,m);return k[f]=t},createFirebaseNamespace:Ia,extendNamespace:function(f){Q(k,f)},createSubscribe:Fa,ErrorFactory:R,removeApp:function(f){b(d[f],"delete");delete d[f]},factories:e,useAsService:c,Promise:O,deepExtend:Q}};k["default"]=k;Object.defineProperty(k,"apps",{get:function(){return Object.keys(d).map(function(f){return d[f]})}});a.App=X;return k}
function Z(a,b){throw La.create(a,b);}
var La=new R("app","Firebase",{"no-app":"No Firebase App '{$name}' has been created - call Firebase App.initializeApp()","bad-app-name":"Illegal App name: '{$name}","duplicate-app":"Firebase App named '{$name}' already exists","app-deleted":"Firebase App named '{$name}' already deleted","duplicate-service":"Firebase service named '{$name}' already registered","sa-not-supported":"Initializing the Firebase SDK with a service account is only allowed in a Node.js environment. On client devices, you should instead initialize the SDK with an api key and auth domain","invalid-app-argument":"firebase.{$name}() takes either no argument or a Firebase App instance."});typeof firebase!=="undefined"&&(firebase=Ia()); }).call(this);
firebase.SDK_VERSION = "3.7.5";
(function(){var k,aa=function(a){var b=0;return function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}}},ba=typeof Object.defineProperties=="function"?Object.defineProperty:function(a,b,c){if(a==Array.prototype||a==Object.prototype)return a;a[b]=c.value;return a},ca=function(a){a=["object"==typeof globalThis&&globalThis,a,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global];for(var b=0;b<a.length;++b){var c=a[b];if(c&&c.Math==Math)return c}throw Error("Cannot find global object");
},da=ca(this),n=function(a,b){if(b)a:{var c=da;a=a.split(".");for(var d=0;d<a.length-1;d++){var e=a[d];if(!(e in c))break a;c=c[e]}a=a[a.length-1];d=c[a];b=b(d);b!=d&&b!=null&&ba(c,a,{configurable:!0,writable:!0,value:b})}};
n("Symbol",function(a){if(a)return a;var b=function(f,g){this.Xj=f;ba(this,"description",{configurable:!0,writable:!0,value:g})};b.prototype.toString=function(){return this.Xj};var c="jscomp_symbol_"+(Math.random()*1E9>>>0)+"_",d=0,e=function(f){if(this instanceof e)throw new TypeError("Symbol is not a constructor");return new b(c+(f||"")+"_"+d++,f)};return e});
n("Symbol.iterator",function(a){if(a)return a;a=Symbol("Symbol.iterator");for(var b="Array Int8Array Uint8Array Uint8ClampedArray Int16Array Uint16Array Int32Array Uint32Array Float32Array Float64Array".split(" "),c=0;c<b.length;c++){var d=da[b[c]];typeof d==="function"&&typeof d.prototype[a]!="function"&&ba(d.prototype,a,{configurable:!0,writable:!0,value:function(){return ea(aa(this))}})}return a});
var ea=function(a){a={next:a};a[Symbol.iterator]=function(){return this};return a},fa=typeof Object.create=="function"?Object.create:function(a){var b=function(){};b.prototype=a;return new b},ha;if(typeof Object.setPrototypeOf=="function")ha=Object.setPrototypeOf;else{var ia;a:{var ja={a:!0},ka={};try{ka.__proto__=ja;ia=ka.a;break a}catch(a){}ia=!1}ha=ia?function(a,b){a.__proto__=b;if(a.__proto__!==b)throw new TypeError(a+" is not extensible");return a}:null}
var la=ha,p=function(a,b){a.prototype=fa(b.prototype);a.prototype.constructor=a;if(la)la(a,b);else for(var c in b)if(c!="prototype")if(Object.defineProperties){var d=Object.getOwnPropertyDescriptor(b,c);d&&Object.defineProperty(a,c,d)}else a[c]=b[c];a.Id=b.prototype},ma=function(a){var b=typeof Symbol!="undefined"&&Symbol.iterator&&a[Symbol.iterator];if(b)return b.call(a);if(typeof a.length=="number")return{next:aa(a)};throw Error(String(a)+" is not an iterable or ArrayLike");},na=function(a){if(!(a instanceof
Array)){a=ma(a);for(var b,c=[];!(b=a.next()).done;)c.push(b.value);a=c}return a},pa=function(a){return oa(a,a)},oa=function(a,b){a.raw=b;Object.freeze&&(Object.freeze(a),Object.freeze(b));return a},qa=function(){for(var a=Number(this),b=[],c=a;c<arguments.length;c++)b[c-a]=arguments[c];return b};n("globalThis",function(a){return a||da});
n("Promise",function(a){function b(){this.nc=null}function c(g){return g instanceof e?g:new e(function(h){h(g)})}if(a)return a;b.prototype.Nh=function(g){if(this.nc==null){this.nc=[];var h=this;this.Oh(function(){h.yk()})}this.nc.push(g)};var d=da.setTimeout;b.prototype.Oh=function(g){d(g,0)};b.prototype.yk=function(){for(;this.nc&&this.nc.length;){var g=this.nc;this.nc=[];for(var h=0;h<g.length;++h){var l=g[h];g[h]=null;try{l()}catch(m){this.fk(m)}}}this.nc=null};b.prototype.fk=function(g){this.Oh(function(){throw g;
})};var e=function(g){this.Ba=0;this.Ua=void 0;this.wd=[];this.Ji=!1;var h=this.ng();try{g(h.resolve,h.reject)}catch(l){h.reject(l)}};e.prototype.ng=function(){function g(m){return function(q){l||(l=!0,m.call(h,q))}}var h=this,l=!1;return{resolve:g(this.Nl),reject:g(this.mh)}};e.prototype.Nl=function(g){if(g===this)this.mh(new TypeError("A Promise cannot resolve to itself"));else if(g instanceof e)this.dm(g);else{a:switch(typeof g){case "object":var h=g!=null;break a;case "function":h=!0;break a;
default:h=!1}h?this.Ml(g):this.li(g)}};e.prototype.Ml=function(g){var h=void 0;try{h=g.then}catch(l){this.mh(l);return}typeof h=="function"?this.em(h,g):this.li(g)};e.prototype.mh=function(g){this.Fj(2,g)};e.prototype.li=function(g){this.Fj(1,g)};e.prototype.Fj=function(g,h){if(this.Ba!=0)throw Error("Cannot settle("+g+", "+h+"): Promise already settled in state"+this.Ba);this.Ba=g;this.Ua=h;this.Ba===2&&this.Yl();this.Ak()};e.prototype.Yl=function(){var g=this;d(function(){if(g.tl()){var h=da.console;
typeof h!=="undefined"&&h.error(g.Ua)}},1)};e.prototype.tl=function(){if(this.Ji)return!1;var g=da.CustomEvent,h=da.Event,l=da.dispatchEvent;if(typeof l==="undefined")return!0;typeof g==="function"?g=new g("unhandledrejection",{cancelable:!0}):typeof h==="function"?g=new h("unhandledrejection",{cancelable:!0}):(g=da.document.createEvent("CustomEvent"),g.initCustomEvent("unhandledrejection",!1,!0,g));g.promise=this;g.reason=this.Ua;return l(g)};e.prototype.Ak=function(){if(this.wd!=null){for(var g=
0;g<this.wd.length;++g)f.Nh(this.wd[g]);this.wd=null}};var f=new b;e.prototype.dm=function(g){var h=this.ng();g.Me(h.resolve,h.reject)};e.prototype.em=function(g,h){var l=this.ng();try{g.call(h,l.resolve,l.reject)}catch(m){l.reject(m)}};e.prototype.then=function(g,h){function l(A,S){return typeof A=="function"?function(Aa){try{m(A(Aa))}catch(Ac){q(Ac)}}:S}var m,q,x=new e(function(A,S){m=A;q=S});this.Me(l(g,m),l(h,q));return x};e.prototype.catch=function(g){return this.then(void 0,g)};e.prototype.Me=
function(g,h){function l(){switch(m.Ba){case 1:g(m.Ua);break;case 2:h(m.Ua);break;default:throw Error("Unexpected state: "+m.Ba);}}var m=this;this.wd==null?f.Nh(l):this.wd.push(l);this.Ji=!0};e.resolve=c;e.reject=function(g){return new e(function(h,l){l(g)})};e.race=function(g){return new e(function(h,l){for(var m=ma(g),q=m.next();!q.done;q=m.next())c(q.value).Me(h,l)})};e.all=function(g){var h=ma(g),l=h.next();return l.done?c([]):new e(function(m,q){function x(Aa){return function(Ac){A[Aa]=Ac;S--;
S==0&&m(A)}}var A=[],S=0;do A.push(void 0),S++,c(l.value).Me(x(A.length-1),q),l=h.next();while(!l.done)})};return e});var ra=function(a,b,c){if(a==null)throw new TypeError("The 'this' value for String.prototype."+c+" must not be null or undefined");if(b instanceof RegExp)throw new TypeError("First argument to String.prototype."+c+" must not be a regular expression");return a+""};
n("String.prototype.startsWith",function(a){return a?a:function(b,c){var d=ra(this,b,"startsWith");b+="";var e=d.length,f=b.length;c=Math.max(0,Math.min(c|0,d.length));for(var g=0;g<f&&c<e;)if(d[c++]!=b[g++])return!1;return g>=f}});n("String.prototype.endsWith",function(a){return a?a:function(b,c){var d=ra(this,b,"endsWith");b+="";c===void 0&&(c=d.length);c=Math.max(0,Math.min(c|0,d.length));for(var e=b.length;e>0&&c>0;)if(d[--c]!=b[--e])return!1;return e<=0}});
var sa=function(a,b){return Object.prototype.hasOwnProperty.call(a,b)};n("Symbol.dispose",function(a){return a?a:Symbol("Symbol.dispose")});var ta=function(a,b){a instanceof String&&(a+="");var c=0,d=!1,e={next:function(){if(!d&&c<a.length){var f=c++;return{value:b(f,a[f]),done:!1}}d=!0;return{done:!0,value:void 0}}};e[Symbol.iterator]=function(){return e};return e};n("Array.prototype.keys",function(a){return a?a:function(){return ta(this,function(b){return b})}});
n("WeakMap",function(a){function b(){}function c(l){var m=typeof l;return m==="object"&&l!==null||m==="function"}function d(l){if(!sa(l,f)){var m=new b;ba(l,f,{value:m})}}function e(l){var m=Object[l];m&&(Object[l]=function(q){if(q instanceof b)return q;Object.isExtensible(q)&&d(q);return m(q)})}if(function(){if(!a||!Object.seal)return!1;try{var l=Object.seal({}),m=Object.seal({}),q=new a([[l,2],[m,3]]);if(q.get(l)!=2||q.get(m)!=3)return!1;q.delete(l);q.set(m,4);return!q.has(l)&&q.get(m)==4}catch(x){return!1}}())return a;
var f="$jscomp_hidden_"+Math.random();e("freeze");e("preventExtensions");e("seal");var g=0,h=function(l){this.Pa=(g+=Math.random()+1).toString();if(l){l=ma(l);for(var m;!(m=l.next()).done;)m=m.value,this.set(m[0],m[1])}};h.prototype.set=function(l,m){if(!c(l))throw Error("Invalid WeakMap key");d(l);if(!sa(l,f))throw Error("WeakMap key fail: "+l);l[f][this.Pa]=m;return this};h.prototype.get=function(l){return c(l)&&sa(l,f)?l[f][this.Pa]:void 0};h.prototype.has=function(l){return c(l)&&sa(l,f)&&sa(l[f],
this.Pa)};h.prototype.delete=function(l){return c(l)&&sa(l,f)&&sa(l[f],this.Pa)?delete l[f][this.Pa]:!1};return h});
n("Map",function(a){if(function(){if(!a||typeof a!="function"||!a.prototype.entries||typeof Object.seal!="function")return!1;try{var h=Object.seal({x:4}),l=new a(ma([[h,"s"]]));if(l.get(h)!="s"||l.size!=1||l.get({x:4})||l.set({x:4},"t")!=l||l.size!=2)return!1;var m=l.entries(),q=m.next();if(q.done||q.value[0]!=h||q.value[1]!="s")return!1;q=m.next();return q.done||q.value[0].x!=4||q.value[1]!="t"||!m.next().done?!1:!0}catch(x){return!1}}())return a;var b=new WeakMap,c=function(h){this[0]={};this[1]=
f();this.size=0;if(h){h=ma(h);for(var l;!(l=h.next()).done;)l=l.value,this.set(l[0],l[1])}};c.prototype.set=function(h,l){h=h===0?0:h;var m=d(this,h);m.list||(m.list=this[0][m.id]=[]);m.Oa?m.Oa.value=l:(m.Oa={next:this[1],Yb:this[1].Yb,head:this[1],key:h,value:l},m.list.push(m.Oa),this[1].Yb.next=m.Oa,this[1].Yb=m.Oa,this.size++);return this};c.prototype.delete=function(h){h=d(this,h);return h.Oa&&h.list?(h.list.splice(h.index,1),h.list.length||delete this[0][h.id],h.Oa.Yb.next=h.Oa.next,h.Oa.next.Yb=
h.Oa.Yb,h.Oa.head=null,this.size--,!0):!1};c.prototype.clear=function(){this[0]={};this[1]=this[1].Yb=f();this.size=0};c.prototype.has=function(h){return!!d(this,h).Oa};c.prototype.get=function(h){return(h=d(this,h).Oa)&&h.value};c.prototype.entries=function(){return e(this,function(h){return[h.key,h.value]})};c.prototype.keys=function(){return e(this,function(h){return h.key})};c.prototype.values=function(){return e(this,function(h){return h.value})};c.prototype.forEach=function(h,l){for(var m=this.entries(),
q;!(q=m.next()).done;)q=q.value,h.call(l,q[1],q[0],this)};c.prototype[Symbol.iterator]=c.prototype.entries;var d=function(h,l){var m=l&&typeof l;m=="object"||m=="function"?b.has(l)?m=b.get(l):(m=""+ ++g,b.set(l,m)):m="p_"+l;var q=h[0][m];if(q&&sa(h[0],m))for(h=0;h<q.length;h++){var x=q[h];if(l!==l&&x.key!==x.key||l===x.key)return{id:m,list:q,index:h,Oa:x}}return{id:m,list:q,index:-1,Oa:void 0}},e=function(h,l){var m=h[1];return ea(function(){if(m){for(;m.head!=h[1];)m=m.Yb;for(;m.next!=m.head;)return m=
m.next,{done:!1,value:l(m)};m=null}return{done:!0,value:void 0}})},f=function(){var h={};return h.Yb=h.next=h.head=h},g=0;return c});
n("Set",function(a){if(function(){if(!a||typeof a!="function"||!a.prototype.entries||typeof Object.seal!="function")return!1;try{var c=Object.seal({x:4}),d=new a(ma([c]));if(!d.has(c)||d.size!=1||d.add(c)!=d||d.size!=1||d.add({x:4})!=d||d.size!=2)return!1;var e=d.entries(),f=e.next();if(f.done||f.value[0]!=c||f.value[1]!=c)return!1;f=e.next();return f.done||f.value[0]==c||f.value[0].x!=4||f.value[1]!=f.value[0]?!1:e.next().done}catch(g){return!1}}())return a;var b=function(c){this.ab=new Map;if(c){c=
ma(c);for(var d;!(d=c.next()).done;)this.add(d.value)}this.size=this.ab.size};b.prototype.add=function(c){c=c===0?0:c;this.ab.set(c,c);this.size=this.ab.size;return this};b.prototype.delete=function(c){c=this.ab.delete(c);this.size=this.ab.size;return c};b.prototype.clear=function(){this.ab.clear();this.size=0};b.prototype.has=function(c){return this.ab.has(c)};b.prototype.entries=function(){return this.ab.entries()};b.prototype.values=function(){return this.ab.values()};b.prototype.keys=b.prototype.values;
b.prototype[Symbol.iterator]=b.prototype.values;b.prototype.forEach=function(c,d){var e=this;this.ab.forEach(function(f){return c.call(d,f,f,e)})};return b});n("Array.prototype.entries",function(a){return a?a:function(){return ta(this,function(b,c){return[b,c]})}});n("Object.entries",function(a){return a?a:function(b){var c=[],d;for(d in b)sa(b,d)&&c.push([d,b[d]]);return c}});n("Number.isFinite",function(a){return a?a:function(b){return typeof b!=="number"?!1:!isNaN(b)&&b!==Infinity&&b!==-Infinity}});
n("Array.prototype.find",function(a){return a?a:function(b,c){a:{var d=this;d instanceof String&&(d=String(d));for(var e=d.length,f=0;f<e;f++){var g=d[f];if(b.call(c,g,f,d)){b=g;break a}}b=void 0}return b}});
n("Array.from",function(a){return a?a:function(b,c,d){c=c!=null?c:function(h){return h};var e=[],f=typeof Symbol!="undefined"&&Symbol.iterator&&b[Symbol.iterator];if(typeof f=="function"){b=f.call(b);for(var g=0;!(f=b.next()).done;)e.push(c.call(d,f.value,g++))}else for(f=b.length,g=0;g<f;g++)e.push(c.call(d,b[g],g));return e}});n("Object.values",function(a){return a?a:function(b){var c=[],d;for(d in b)sa(b,d)&&c.push(b[d]);return c}});
n("Array.prototype.values",function(a){return a?a:function(){return ta(this,function(b,c){return c})}});n("Object.is",function(a){return a?a:function(b,c){return b===c?b!==0||1/b===1/c:b!==b&&c!==c}});n("Array.prototype.includes",function(a){return a?a:function(b,c){var d=this;d instanceof String&&(d=String(d));var e=d.length;c=c||0;for(c<0&&(c=Math.max(c+e,0));c<e;c++){var f=d[c];if(f===b||Object.is(f,b))return!0}return!1}});
n("String.prototype.includes",function(a){return a?a:function(b,c){return ra(this,b,"includes").indexOf(b,c||0)!==-1}});/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
var ua=ua||{},r=this||self,t=function(a,b){a=a.split(".");for(var c=r,d;a.length&&(d=a.shift());)a.length||b===void 0?c=c[d]&&c[d]!==Object.prototype[d]?c[d]:c[d]={}:c[d]=b},va=function(a){var b=typeof a;return b!="object"?b:a?Array.isArray(a)?"array":b:"null"},wa=function(a){var b=va(a);return b=="array"||b=="object"&&typeof a.length=="number"},u=function(a){var b=typeof a;return b=="object"&&a!=null||b=="function"},xa=function(a,b,c){return a.call.apply(a.bind,arguments)},ya=function(a,b,c){if(!a)throw Error();
if(arguments.length>2){var d=Array.prototype.slice.call(arguments,2);return function(){var e=Array.prototype.slice.call(arguments);Array.prototype.unshift.apply(e,d);return a.apply(b,e)}}return function(){return a.apply(b,arguments)}},v=function(a,b,c){v=Function.prototype.bind&&Function.prototype.bind.toString().indexOf("native code")!=-1?xa:ya;return v.apply(null,arguments)},za=function(a,b){var c=Array.prototype.slice.call(arguments,1);return function(){var d=c.slice();d.push.apply(d,arguments);
return a.apply(this,d)}},w=function(a,b){function c(){}c.prototype=b.prototype;a.Id=b.prototype;a.prototype=new c;a.prototype.constructor=a;a.Pd=function(d,e,f){for(var g=Array(arguments.length-2),h=2;h<arguments.length;h++)g[h-2]=arguments[h];return b.prototype[e].apply(d,g)}};var Ba=function(a){var b=window.___jsl=window.___jsl||{};b.cfg=b.cfg||{};b=b.cfg;if(!a)return b;a=a.split("/");for(var c=0,d=a.length;b&&typeof b==="object"&&c<d;++c)b=b[a[c]];return c===a.length&&b!==void 0?b:void 0};/*

 Copyright Google LLC
 SPDX-License-Identifier: Apache-2.0
*/
var Ca={};function Da(){if(Ca!==Ca)throw Error("Bad secret");};var Ea=pa([""]),Fa=oa(["\x00"],["\\0"]),Ga=oa(["\n"],["\\n"]),Ha=oa(["\x00"],["\\u0000"]),Ia=pa([""]),Ja=oa(["\x00"],["\\0"]),Ka=oa(["\n"],["\\n"]),La=oa(["\x00"],["\\u0000"]);function Ma(a){return Object.isFrozen(a)&&Object.isFrozen(a.raw)}function Na(a){return a.toString().indexOf("`")===-1}var Oa=Na(function(a){return a(Ea)})||Na(function(a){return a(Fa)})||Na(function(a){return a(Ga)})||Na(function(a){return a(Ha)}),Pa=Ma(Ia)&&Ma(Ja)&&Ma(Ka)&&Ma(La);var Qa=globalThis.trustedTypes,Ra;function Sa(){var a=null;if(!Qa)return a;try{var b=function(c){return c};a=Qa.createPolicy("goog#html",{createHTML:b,createScript:b,createScriptURL:b})}catch(c){throw c;}return a}function Ta(){Ra===void 0&&(Ra=Sa());return Ra};var Ua=function(a){Da();this.lj=a};Ua.prototype.toString=function(){return this.lj+""};function Va(a){var b=Ta();return new Ua(b?b.createHTML(a):a)}function Wa(a){if(a instanceof Ua)return a.lj;throw Error("Unexpected type when unwrapping SafeHtml");};var Xa=function(a){Da();this.mj=a};Xa.prototype.toString=function(){return this.mj+""};function Ya(a){var b=Ta();return new Xa(b?b.createScriptURL(a):a)}function Za(a){if(a instanceof Xa)return a.mj;throw Error("Unexpected type when unwrapping TrustedResourceUrl");};var $a=function(a){Da();this.nj=a};$a.prototype.toString=function(){return this.nj};new $a("about:blank");var ab=new $a("about:invalid#zClosurez");function bb(a){if(a instanceof $a)return a.nj;throw Error("Unexpected type when unwrapping SafeUrl, got '"+a+"' of type '"+typeof a+"'");};var cb=function(a){this.kl=a};function db(a){return new cb(function(b){return b.substr(0,a.length+1).toLowerCase()===a+":"})}var eb=[db("data"),db("http"),db("https"),db("mailto"),db("ftp"),new cb(function(a){return/^[^:]*([/?#]|$)/.test(a)})];function fb(a){var b=b===void 0?eb:b;a:if(b=b===void 0?eb:b,a instanceof $a)b=a;else{for(var c=0;c<b.length;++c){var d=b[c];if(d instanceof cb&&d.kl(a)){b=new $a(a);break a}}b=void 0}b===void 0&&gb(a.toString());return b||ab}var hb=/^\s*(?!javascript:)(?:[\w+.-]+:|[^:/?#]*(?:[/?#]|$))/i;
function ib(a){if(a instanceof $a)a=bb(a);else{var b=!hb.test(a);b&&gb(a);a=b?void 0:a}return a}var jb=[],gb=function(){};kb(function(a){console.warn("A URL with content '"+a+"' was sanitized away.")});function kb(a){jb.indexOf(a)===-1&&jb.push(a);gb=function(b){jb.forEach(function(c){c(b)})}};function lb(a){var b=qa.apply(1,arguments),c=b.length;if(!Array.isArray(a)||!Array.isArray(a.raw)||a.length!==a.raw.length||!Oa&&a===a.raw||!(Oa&&!Pa||Ma(a))||c+1!==a.length)throw new TypeError("\n    ############################## ERROR ##############################\n\n    It looks like you are trying to call a template tag function (fn`...`)\n    using the normal function syntax (fn(...)), which is not supported.\n\n    The functions in the safevalues library are not designed to be called\n    like normal functions, and doing so invalidates the security guarantees\n    that safevalues provides.\n\n    If you are stuck and not sure how to proceed, please reach out to us\n    instead through:\n     - go/ise-hardening-yaqs (preferred) // LINE-INTERNAL\n     - g/ise-hardening // LINE-INTERNAL\n     - https://github.com/google/safevalues/issues\n\n    ############################## ERROR ##############################");
if(b.length===0)return Ya(a[0]);c=a[0].toLowerCase();if(/^data:/.test(c))throw Error("Data URLs cannot have expressions in the template literal input.");if(/^https:\/\//.test(c)||/^\/\//.test(c)){var d=c.indexOf("//")+2;var e=c.indexOf("/",d);if(e<=d)throw Error("Can't interpolate data in a url's origin, Please make sure to fully specify the origin, terminated with '/'.");d=c.substring(d,e);if(!/^[0-9a-z.:-]+$/i.test(d))throw Error("The origin contains unsupported characters.");if(!/^[^:]*(:[0-9]+)?$/i.test(d))throw Error("Invalid port number.");
if(!/(^|\.)[a-z][^.]*$/i.test(d))throw Error("The top-level domain must start with a letter.");d=!0}else d=!1;if(!d)if(/^\//.test(c))if(c==="/"||c.length>1&&c[1]!=="/"&&c[1]!=="\\")d=!0;else throw Error("The path start in the url is invalid.");else d=!1;if(!(d=d||RegExp("^[^:\\s\\\\/]+/").test(c)))if(/^about:blank/.test(c)){if(c!=="about:blank"&&!/^about:blank#/.test(c))throw Error("The about url is invalid.");d=!0}else d=!1;if(!d)throw Error("Trying to interpolate expressions in an unsupported url format.");
c=a[0];for(d=0;d<b.length;d++)c+=encodeURIComponent(b[d])+a[d+1];return Ya(c)};function mb(a,b){if(Error.captureStackTrace)Error.captureStackTrace(this,mb);else{var c=Error().stack;c&&(this.stack=c)}a&&(this.message=String(a));b!==void 0&&(this.cause=b)}w(mb,Error);mb.prototype.name="CustomError";var nb;function ob(a,b){a=a.split("%s");for(var c="",d=a.length-1,e=0;e<d;e++)c+=a[e]+(e<b.length?b[e]:"%s");mb.call(this,c+a[d])}w(ob,mb);ob.prototype.name="AssertionError";function pb(a,b,c,d){var e="Assertion failed";if(c){e+=": "+c;var f=d}else a&&(e+=": "+a,f=b);throw new ob(""+e,f||[]);}
var y=function(a,b,c){a||pb("",null,b,Array.prototype.slice.call(arguments,2));return a},qb=function(a,b,c){a==null&&pb("Expected to exist: %s.",[a],b,Array.prototype.slice.call(arguments,2));return a},rb=function(a,b){throw new ob("Failure"+(a?": "+a:""),Array.prototype.slice.call(arguments,1));},sb=function(a,b,c){typeof a!=="number"&&pb("Expected number but got %s: %s.",[va(a),a],b,Array.prototype.slice.call(arguments,2));return a},tb=function(a,b,c){typeof a!=="function"&&pb("Expected function but got %s: %s.",
[va(a),a],b,Array.prototype.slice.call(arguments,2))};var ub=Array.prototype.indexOf?function(a,b){y(a.length!=null);return Array.prototype.indexOf.call(a,b,void 0)}:function(a,b){if(typeof a==="string")return typeof b!=="string"||b.length!=1?-1:a.indexOf(b,0);for(var c=0;c<a.length;c++)if(c in a&&a[c]===b)return c;return-1},z=Array.prototype.forEach?function(a,b){y(a.length!=null);Array.prototype.forEach.call(a,b,void 0)}:function(a,b){for(var c=a.length,d=typeof a==="string"?a.split(""):a,e=0;e<c;e++)e in d&&b.call(void 0,d[e],e,a)};
function vb(a,b){for(var c=typeof a==="string"?a.split(""):a,d=a.length-1;d>=0;--d)d in c&&b.call(void 0,c[d],d,a)}
var wb=Array.prototype.filter?function(a,b){y(a.length!=null);return Array.prototype.filter.call(a,b,void 0)}:function(a,b){for(var c=a.length,d=[],e=0,f=typeof a==="string"?a.split(""):a,g=0;g<c;g++)if(g in f){var h=f[g];b.call(void 0,h,g,a)&&(d[e++]=h)}return d},xb=Array.prototype.map?function(a,b){y(a.length!=null);return Array.prototype.map.call(a,b,void 0)}:function(a,b){for(var c=a.length,d=Array(c),e=typeof a==="string"?a.split(""):a,f=0;f<c;f++)f in e&&(d[f]=b.call(void 0,e[f],f,a));return d},
yb=Array.prototype.some?function(a,b){y(a.length!=null);return Array.prototype.some.call(a,b,void 0)}:function(a,b){for(var c=a.length,d=typeof a==="string"?a.split(""):a,e=0;e<c;e++)if(e in d&&b.call(void 0,d[e],e,a))return!0;return!1};function zb(a,b){return ub(a,b)>=0}function Ab(a,b){b=ub(a,b);var c;(c=b>=0)&&Bb(a,b);return c}function Bb(a,b){y(a.length!=null);return Array.prototype.splice.call(a,b,1).length==1}function Cb(a,b){var c=0;vb(a,function(d,e){b.call(void 0,d,e,a)&&Bb(a,e)&&c++})}
function Db(a){var b=a.length;if(b>0){for(var c=Array(b),d=0;d<b;d++)c[d]=a[d];return c}return[]}function Eb(a,b){for(var c=0,d=a.length,e;c<d;){var f=c+(d-c>>>1);var g=b.call(void 0,a[f],f,a);g>0?c=f+1:(d=f,e=!g)}return e?c:-c-1};function Fb(a,b){for(var c in a)b.call(void 0,a[c],c,a)}function Gb(a,b){for(var c in a)if(b.call(void 0,a[c],c,a))return!0;return!1}function Hb(a){for(var b in a)return!1;return!0}function Ib(a){var b={},c;for(c in a)b[c]=a[c];return b}var Jb="constructor hasOwnProperty isPrototypeOf propertyIsEnumerable toLocaleString toString valueOf".split(" ");
function Kb(a,b){for(var c,d,e=1;e<arguments.length;e++){d=arguments[e];for(c in d)a[c]=d[c];for(var f=0;f<Jb.length;f++)c=Jb[f],Object.prototype.hasOwnProperty.call(d,c)&&(a[c]=d[c])}};var Lb=/&/g,Mb=/</g,Nb=/>/g,Ob=/"/g,Pb=/'/g,Qb=/\x00/g,Rb=/[\x00&<>"']/;function B(a,b){return a.indexOf(b)!=-1};function Sb(a,b,c,d){b=ib(b);return b!==void 0?a.open(b,c,d):null};function Tb(a,b){a.src=Za(b);var c;b=a.ownerDocument;b=b===void 0?document:b;var d;b=(d=(c=b).querySelector)==null?void 0:d.call(c,"script[nonce]");(c=b==null?"":b.nonce||b.getAttribute("nonce")||"")&&a.setAttribute("nonce",c)};var Ub=function(a,b){this.name=a;this.value=b};Ub.prototype.toString=function(){return this.name};var Vb=new Ub("OFF",Infinity),Wb=new Ub("SEVERE",1E3),Xb=new Ub("WARNING",900),Yb=new Ub("CONFIG",700),Zb=new Ub("FINE",500),$b=function(){this.Ne=0;this.clear()},ac;$b.prototype.clear=function(){this.Qh=Array(this.Ne);this.Yh=-1;this.Ii=!1};var bc=function(a,b){this.reset(a||Vb,b,null,void 0,void 0)};bc.prototype.reset=function(){};
var cc=function(a){this.level=null;this.Wk=[];this.parent=(a===void 0?null:a)||null;this.children=[]},dc=function(a){if(a.level)return a.level;if(a.parent)return dc(a.parent);rb("Root logger has no level set.");return Vb},ec=function(a,b){for(;a;)a.Wk.forEach(function(c){c(b)}),a=a.parent},fc=function(){this.entries={};var a=new cc;a.level=Yb;this.entries[""]=a},gc,hc=function(a,b){var c=a.entries[b];if(c)return c;c=hc(a,b.slice(0,Math.max(b.lastIndexOf("."),0)));var d=new cc(c);a.entries[b]=d;c.children.push(d);
return d},ic=function(){gc||(gc=new fc);return gc},jc=function(a,b,c){if(a&&a&&b&&b.value>=(a?dc(hc(ic(),null)):Vb).value){b=b||Vb;a=hc(ic(),null);typeof c==="function"&&(c=c());ac||(ac=new $b);var d=ac;if(d.Ne>0){var e=(d.Yh+1)%d.Ne;d.Yh=e;d.Ii?(d=d.Qh[e],d.reset(b,c,null)):(d.Ii=e==d.Ne-1,d=d.Qh[e]=new bc(b,c))}else d=new bc(b,c);ec(a,d)}},kc=function(a,b){a&&jc(a,Zb,b)};function lc(a){if(typeof a!=="string"||a.trim()==="")throw Error("Calls to uncheckedconversion functions must go through security review. A justification must be provided to capture what security assumptions are being made. See go/unchecked-conversions");};var mc=function(a,b){for(var c=a.split("%s"),d="",e=Array.prototype.slice.call(arguments,1);e.length&&c.length>1;)d+=c.shift()+e.shift();return d+c.join("%s")};var nc,oc;a:{for(var pc=["CLOSURE_FLAGS"],qc=r,rc=0;rc<pc.length;rc++)if(qc=qc[pc[rc]],qc==null){oc=null;break a}oc=qc}var sc=oc&&oc[610401301];nc=sc!=null?sc:!1;function tc(){var a=r.navigator;return a&&(a=a.userAgent)?a:""}var uc,vc=r.navigator;uc=vc?vc.userAgentData||null:null;function wc(a){if(!nc||!uc)return!1;for(var b=0;b<uc.brands.length;b++){var c=uc.brands[b].brand;if(c&&B(c,a))return!0}return!1}function C(a){return B(tc(),a)};function xc(){return nc?!!uc&&uc.brands.length>0:!1};function yc(){return C("iPhone")&&!C("iPod")&&!C("iPad")};var zc=xc()?!1:C("Opera"),Bc=xc()?!1:C("Trident")||C("MSIE"),Cc=C("Edge"),Dc=C("Gecko")&&!(B(tc().toLowerCase(),"webkit")&&!C("Edge"))&&!(C("Trident")||C("MSIE"))&&!C("Edge"),Ec=B(tc().toLowerCase(),"webkit")&&!C("Edge"),Fc=Ec&&C("Mobile"),Gc=function(){var a=r.document;return a?a.documentMode:void 0},Hc;
a:{var Ic="",Jc=function(){var a=tc();if(Dc)return/rv:([^\);]+)(\)|;)/.exec(a);if(Cc)return/Edge\/([\d\.]+)/.exec(a);if(Bc)return/\b(?:MSIE|rv)[: ]([^\);]+)(\)|;)/.exec(a);if(Ec)return/WebKit\/(\S+)/.exec(a);if(zc)return/(?:Version)[ \/]?(\S+)/.exec(a)}();Jc&&(Ic=Jc?Jc[1]:"");if(Bc){var Kc=Gc();if(Kc!=null&&Kc>parseFloat(Ic)){Hc=String(Kc);break a}}Hc=Ic}var Lc=Hc,Mc;if(r.document&&Bc){var Nc=Gc();Mc=Nc?Nc:parseInt(Lc,10)||void 0}else Mc=void 0;var Oc=Mc;var Pc=function(a){var b=document;return typeof a==="string"?b.getElementById(a):a},Rc=function(a,b){Fb(b,function(c,d){d=="style"?a.style.cssText=c:d=="class"?a.className=c:d=="for"?a.htmlFor=c:Qc.hasOwnProperty(d)?a.setAttribute(Qc[d],c):d.lastIndexOf("aria-",0)==0||d.lastIndexOf("data-",0)==0?a.setAttribute(d,c):a[d]=c})},Qc={cellpadding:"cellPadding",cellspacing:"cellSpacing",colspan:"colSpan",frameborder:"frameBorder",height:"height",maxlength:"maxLength",nonce:"nonce",role:"role",rowspan:"rowSpan",
type:"type",usemap:"useMap",valign:"vAlign",width:"width"},Sc=function(a,b,c){function d(h){h&&b.appendChild(typeof h==="string"?a.createTextNode(h):h)}for(var e=1;e<c.length;e++){var f=c[e];if(!wa(f)||u(f)&&f.nodeType>0)d(f);else{a:{if(f&&typeof f.length=="number"){if(u(f)){var g=typeof f.item=="function"||typeof f.item=="string";break a}if(typeof f==="function"){g=typeof f.item=="function";break a}}g=!1}z(g?Db(f):f,d)}}},Tc=function(a,b){b=String(b);a.contentType==="application/xhtml+xml"&&(b=b.toLowerCase());
return a.createElement(b)},Uc=function(a){for(var b;b=a.firstChild;)a.removeChild(b)},Vc=function(a){return a&&a.parentNode?a.parentNode.removeChild(a):null},Wc=function(a){y(a,"Node cannot be null or undefined.");return a.nodeType==9?a:a.ownerDocument||a.document},Xc=function(a){this.Se=a||r.document||document};k=Xc.prototype;k.getElementsByTagName=function(a,b){return(b||this.Se).getElementsByTagName(String(a))};k.createElement=function(a){return Tc(this.Se,a)};k.createTextNode=function(a){return this.Se.createTextNode(String(a))};
k.getWindow=function(){return this.Se.defaultView};k.appendChild=function(a,b){y(a!=null&&b!=null,"goog.dom.appendChild expects non-null arguments");a.appendChild(b)};k.append=function(a,b){Sc(Wc(a),a,arguments)};k.canHaveChildren=function(a){if(a.nodeType!=1)return!1;switch(a.tagName){case "APPLET":case "AREA":case "BASE":case "BR":case "COL":case "COMMAND":case "EMBED":case "FRAME":case "HR":case "IMG":case "INPUT":case "IFRAME":case "ISINDEX":case "KEYGEN":case "LINK":case "NOFRAMES":case "NOSCRIPT":case "META":case "OBJECT":case "PARAM":case "SCRIPT":case "SOURCE":case "STYLE":case "TRACK":case "WBR":return!1}return!0};
k.removeNode=Vc;k.isElement=function(a){return u(a)&&a.nodeType==1};k.contains=function(a,b){if(!a||!b)return!1;if(a.contains&&b.nodeType==1)return a==b||a.contains(b);if(typeof a.compareDocumentPosition!="undefined")return a==b||!!(a.compareDocumentPosition(b)&16);for(;b&&a!=b;)b=b.parentNode;return b==a};/*
 gapi.loader.OBJECT_CREATE_TEST_OVERRIDE &&*/
var D=window,Yc=document,Zc=/\[native code\]/,$c=function(a,b,c){return a[b]=a[b]||c},ad=function(a){return!!a&&typeof a==="object"&&Zc.test(a.push)},bd=function(a){for(var b=0;b<this.length;b++)if(this[b]===a)return b;return-1},dd=function(a,b,c){if(a)if(ad(a)){if(a){E(ad(a),"arrayForEach was called with a non array value");for(var d=0;d<a.length;d++)b.call(c,a[d],d)}}else for(d in E(typeof a==="object","objectForEach was called with a non object value"),c=c||a,a)cd(a,d)&&a[d]!==void 0&&b.call(c,
a[d],d)},ed=/&/g,fd=/</g,gd=/>/g,hd=/"/g,id=/'/g,jd=function(a){return String(a).replace(ed,"&amp;").replace(fd,"&lt;").replace(gd,"&gt;").replace(hd,"&quot;").replace(id,"&#39;")},kd=function(){var a;if((a=Object.create)&&Zc.test(a))a=a(null);else{a={};for(var b in a)a[b]=void 0}return a},cd=function(a,b){return Object.prototype.hasOwnProperty.call(a,b)},ld=function(a,b){a=a||{};for(var c in a)cd(a,c)&&(b[c]=a[c])},E=function(a,b){if(!a)throw Error(b||"");},md=$c(D,"gapi",{});var nd=function(a,b,c){var d=new RegExp("([#].*&|[#])"+b+"=([^&#]*)","g");b=new RegExp("([?#].*&|[?#])"+b+"=([^&#]*)","g");if(a=a&&(d.exec(a)||b.exec(a)))try{c=decodeURIComponent(a[2])}catch(e){}return c},od=new RegExp(/^/.source+/([a-zA-Z][-+.a-zA-Z0-9]*:)?/.source+/(\/\/[^\/?#]*)?/.source+/([^?#]*)?/.source+/(\?([^#]*))?/.source+/(#((#|[^#])*))?/.source+/$/.source),pd=/[\ud800-\udbff][\udc00-\udfff]|[^!-~]/g,qd=new RegExp(/(%([^0-9a-fA-F%]|[0-9a-fA-F]([^0-9a-fA-F%])?)?)*/.source+/%($|[^0-9a-fA-F]|[0-9a-fA-F]($|[^0-9a-fA-F]))/.source,
"g"),rd=/%([a-f]|[0-9a-fA-F][a-f])/g,sd=/^(https?|ftp|file|chrome-extension):$/i,td=function(a){a=String(a);a=a.replace(pd,function(e){try{return encodeURIComponent(e)}catch(f){return encodeURIComponent(e.replace(/^[^%]+$/g,"\ufffd"))}}).replace(qd,function(e){return e.replace(/%/g,"%25")}).replace(rd,function(e){return e.toUpperCase()});a=a.match(od)||[];var b=kd(),c=function(e){return e.replace(/\\/g,"%5C").replace(/\^/g,"%5E").replace(/`/g,"%60").replace(/\{/g,"%7B").replace(/\|/g,"%7C").replace(/\}/g,
"%7D")},d=!!(a[1]||"").match(sd);b.Pd=c((a[1]||"")+(a[2]||"")+(a[3]||(a[2]&&d?"/":"")));d=function(e){return c(e.replace(/\?/g,"%3F").replace(/#/g,"%23"))};b.query=a[5]?[d(a[5])]:[];b.Rb=a[7]?[d(a[7])]:[];return b},ud=function(a){return a.Pd+(a.query.length>0?"?"+a.query.join("&"):"")+(a.Rb.length>0?"#"+a.Rb.join("&"):"")},vd=function(a,b){var c=[];if(a)for(var d in a)if(cd(a,d)&&a[d]!=null){var e=b?b(a[d]):a[d];c.push(encodeURIComponent(d)+"="+encodeURIComponent(e))}return c},wd=new RegExp(/\/?\??#?/.source+
"("+/[\/?#]/i.source+"|"+/[\uD800-\uDBFF]/i.source+"|"+/%[c-f][0-9a-f](%[89ab][0-9a-f]){0,2}(%[89ab]?)?/i.source+"|"+/%[0-9a-f]?/i.source+")$","i"),xd=function(a,b){var c=td(b);b=c.Pd;c.query.length&&(b+="?"+c.query.join(""));c.Rb.length&&(b+="#"+c.Rb.join(""));var d="";b.length>2E3&&(d=b,b=b.substr(0,2E3),b=b.replace(wd,""),d=d.substr(b.length));var e=a.createElement("div");a=a.createElement("a");c=td(b);b=c.Pd;c.query.length&&(b+="?"+c.query.join(""));c.Rb.length&&(b+="#"+c.Rb.join(""));b=b===null?
"null":b===void 0?"undefined":b;if(typeof b!=="string")throw Error("Expected a string");b=new $a(b);b=ib(b);b!==void 0&&(a.href=b);e.appendChild(a);a=e.innerHTML;lc("Assignment to self.");a=Va(a);if(e.nodeType===1&&(b=e.tagName,/^(script|style)$/i.test(b)))throw d=b.toLowerCase()==="script"?"Use setScriptTextContent with a SafeScript.":"Use setStyleTextContent with a SafeStyleSheet.",Error(d);e.innerHTML=Wa(a);b=String(e.firstChild.href);e.parentNode&&e.parentNode.removeChild(e);c=td(b+d);d=c.Pd;
c.query.length&&(d+="?"+c.query.join(""));c.Rb.length&&(d+="#"+c.Rb.join(""));return d},yd=/^https?:\/\/[^\/%\\?#\s]+\/[^\s]*$/i;var zd;var Bd=function(a,b,c){Ad(a,b,c,"add","at")},Ad=function(a,b,c,d,e){if(a[d+"EventListener"])a[d+"EventListener"](b,c,!1);else if(a[e+"tachEvent"])a[e+"tachEvent"]("on"+b,c)},Cd=function(a){for(;a.firstChild;)a.removeChild(a.firstChild)};var Dd=/^https?:\/\/(?:\w|[\-\.])+\.google\.(?:\w|[\-:\.])+(?:\/[^\?#]*)?\/u\/(\d)\//,Ed=/^https?:\/\/(?:\w|[\-\.])+\.google\.(?:\w|[\-:\.])+(?:\/[^\?#]*)?\/b\/(\d{10,21})\//,Fd=function(){var a=Ba("googleapis.config/sessionIndex");"string"===typeof a&&a.length>254&&(a=null);a==null&&(a=window.__X_GOOG_AUTHUSER);"string"===typeof a&&a.length>254&&(a=null);if(a==null){var b=window.google;b&&(a=b.authuser)}"string"===typeof a&&a.length>254&&(a=null);a==null&&(b=window.location.href,a=nd(b,"authuser")||
null,a==null&&(a=(a=b.match(Dd))?a[1]:null));if(a==null)return null;a=String(a);a.length>254&&(a=null);return a},Gd=function(){var a=Ba("googleapis.config/sessionDelegate");"string"===typeof a&&a.length>21&&(a=null);a==null&&(a=(a=window.location.href.match(Ed))?a[1]:null);if(a==null)return null;a=String(a);a.length>21&&(a=null);return a};var Hd={};Hd=$c(D,"___jsl",kd());$c(Hd,"I",0);$c(Hd,"hel",10);var Id,Jd,Kd=void 0,Ld=function(a){try{return r.JSON.parse.call(r.JSON,a)}catch(b){return!1}},Md=function(a){return Object.prototype.toString.call(a)},Nd=Md(0),Od=Md(new Date(0)),Pd=Md(!0),Qd=Md(""),Rd=Md({}),Sd=Md([]),Td=function(a,b){if(b)for(var c=0,d=b.length;c<d;++c)if(a===b[c])throw new TypeError("Converting circular structure to JSON");d=typeof a;if(d!=="undefined"){c=Array.prototype.slice.call(b||[],0);c[c.length]=a;b=[];var e=Md(a);if(a!=null&&typeof a.toJSON==="function"&&(Object.prototype.hasOwnProperty.call(a,
"toJSON")||(e!==Sd||a.constructor!==Array&&a.constructor!==Object)&&(e!==Rd||a.constructor!==Array&&a.constructor!==Object)&&e!==Qd&&e!==Nd&&e!==Pd&&e!==Od))return Td(a.toJSON.call(a),c);if(a==null)b[b.length]="null";else if(e===Nd)a=Number(a),isNaN(a)||isNaN(a-a)?a="null":a===-0&&1/a<0&&(a="-0"),b[b.length]=String(a);else if(e===Pd)b[b.length]=String(!!Number(a));else{if(e===Od)return Td(a.toISOString.call(a),c);if(e===Sd&&Md(a.length)===Nd){b[b.length]="[";var f=0;for(d=Number(a.length)>>0;f<d;++f)f&&
(b[b.length]=","),b[b.length]=Td(a[f],c)||"null";b[b.length]="]"}else if(e==Qd&&Md(a.length)===Nd){b[b.length]='"';f=0;for(c=Number(a.length)>>0;f<c;++f)d=String.prototype.charAt.call(a,f),e=String.prototype.charCodeAt.call(a,f),b[b.length]=d==="\b"?"\\b":d==="\f"?"\\f":d==="\n"?"\\n":d==="\r"?"\\r":d==="\t"?"\\t":d==="\\"||d==='"'?"\\"+d:e<=31?"\\u"+(e+65536).toString(16).substr(1):e>=32&&e<=65535?d:"\ufffd";b[b.length]='"'}else if(d==="object"){b[b.length]="{";d=0;for(f in a)Object.prototype.hasOwnProperty.call(a,
f)&&(e=Td(a[f],c),e!==void 0&&(d++&&(b[b.length]=","),b[b.length]=Td(f),b[b.length]=":",b[b.length]=e));b[b.length]="}"}else return}return b.join("")}},Ud=/[\0-\x07\x0b\x0e-\x1f]/,Vd=/^([^"]*"([^\\"]|\\.)*")*[^"]*"([^"\\]|\\.)*[\0-\x1f]/,Wd=/^([^"]*"([^\\"]|\\.)*")*[^"]*"([^"\\]|\\.)*\\[^\\\/"bfnrtu]/,Xd=/^([^"]*"([^\\"]|\\.)*")*[^"]*"([^"\\]|\\.)*\\u([0-9a-fA-F]{0,3}[^0-9a-fA-F])/,Yd=/"([^\0-\x1f\\"]|\\[\\\/"bfnrt]|\\u[0-9a-fA-F]{4})*"/g,Zd=/-?(0|[1-9][0-9]*)(\.[0-9]+)?([eE][-+]?[0-9]+)?/g,$d=/[ \t\n\r]+/g,
ae=/[^"]:/,be=/""/g,ce=/true|false|null/g,de=/00/,ee=/[\{]([^0\}]|0[^:])/,fe=/(^|\[)[,:]|[,:](\]|\}|[,:]|$)/,ge=/[^\[,:][\[\{]/,he=/^(\{|\}|\[|\]|,|:|0)+/,ie=/\u2028/g,je=/\u2029/g,le=function(a){a=String(a);if(Ud.test(a)||Vd.test(a)||Wd.test(a)||Xd.test(a))return!1;var b=a.replace(Yd,'""');b=b.replace(Zd,"0");b=b.replace($d,"");if(ae.test(b))return!1;b=b.replace(be,"0");b=b.replace(ce,"0");if(de.test(b)||ee.test(b)||fe.test(b)||ge.test(b)||!b||(b=b.replace(he,"")))return!1;a=a.replace(ie,"\\u2028").replace(je,
"\\u2029");b=void 0;try{b=Kd?[Ld(a)]:eval("(function (var_args) {\n  return Array.prototype.slice.call(arguments, 0);\n})(\n"+a+"\n)")}catch(c){return!1}return b&&b.length===1?b[0]:!1},me=function(){var a=((r.document||{}).scripts||[]).length;if((Id===void 0||Kd===void 0||Jd!==a)&&Jd!==-1){Id=Kd=!1;Jd=-1;try{try{Kd=!!r.JSON&&r.JSON.stringify.call(r.JSON,{a:[3,!0,new Date(0)],c:function(){}})==='{"a":[3,true,"1970-01-01T00:00:00.000Z"]}'&&Ld("true")===!0&&Ld('[{"a":3}]')[0].a===3}catch(b){}Id=Kd&&
!Ld("[00]")&&!Ld('"\u0007"')&&!Ld('"\\0"')&&!Ld('"\\v"')}finally{Jd=a}}},ne=function(a){if(Jd===-1)return!1;me();return(Id?Ld:le)(a)},oe=function(a){if(Jd!==-1)return me(),Kd?r.JSON.stringify.call(r.JSON,a):Td(a)},pe=!Date.prototype.toISOString||typeof Date.prototype.toISOString!=="function"||(new Date(0)).toISOString()!=="1970-01-01T00:00:00.000Z",qe=function(){var a=Date.prototype.getUTCFullYear.call(this);return[a<0?"-"+String(1E6-a).substr(1):a<=9999?String(1E4+a).substr(1):"+"+String(1E6+a).substr(1),
"-",String(101+Date.prototype.getUTCMonth.call(this)).substr(1),"-",String(100+Date.prototype.getUTCDate.call(this)).substr(1),"T",String(100+Date.prototype.getUTCHours.call(this)).substr(1),":",String(100+Date.prototype.getUTCMinutes.call(this)).substr(1),":",String(100+Date.prototype.getUTCSeconds.call(this)).substr(1),".",String(1E3+Date.prototype.getUTCMilliseconds.call(this)).substr(1),"Z"].join("")};Date.prototype.toISOString=pe?qe:Date.prototype.toISOString;var re=function(){if(!r.addEventListener||!Object.defineProperty)return!1;var a=!1,b=Object.defineProperty({},"passive",{get:function(){a=!0}});try{var c=function(){};r.addEventListener("test",c,b);r.removeEventListener("test",c,b)}catch(d){}return a}();function se(){this.blockSize=-1};function te(){this.blockSize=-1;this.blockSize=64;this.va=[];this.hg=[];this.Yj=[];this.yf=[];this.yf[0]=128;for(var a=1;a<this.blockSize;++a)this.yf[a]=0;this.bc=this.rd=0;this.reset()}w(te,se);te.prototype.reset=function(){this.va[0]=1732584193;this.va[1]=4023233417;this.va[2]=2562383102;this.va[3]=271733878;this.va[4]=3285377520;this.bc=this.rd=0};
var ue=function(a,b,c){c||(c=0);var d=a.Yj;if(typeof b==="string")for(var e=0;e<16;e++)d[e]=b.charCodeAt(c)<<24|b.charCodeAt(c+1)<<16|b.charCodeAt(c+2)<<8|b.charCodeAt(c+3),c+=4;else for(e=0;e<16;e++)d[e]=b[c]<<24|b[c+1]<<16|b[c+2]<<8|b[c+3],c+=4;for(b=16;b<80;b++)c=d[b-3]^d[b-8]^d[b-14]^d[b-16],d[b]=(c<<1|c>>>31)&4294967295;b=a.va[0];c=a.va[1];e=a.va[2];for(var f=a.va[3],g=a.va[4],h,l,m=0;m<80;m++)m<40?m<20?(h=f^c&(e^f),l=1518500249):(h=c^e^f,l=1859775393):m<60?(h=c&e|f&(c|e),l=2400959708):(h=c^
e^f,l=3395469782),h=(b<<5|b>>>27)+h+g+l+d[m]&4294967295,g=f,f=e,e=(c<<30|c>>>2)&4294967295,c=b,b=h;a.va[0]=a.va[0]+b&4294967295;a.va[1]=a.va[1]+c&4294967295;a.va[2]=a.va[2]+e&4294967295;a.va[3]=a.va[3]+f&4294967295;a.va[4]=a.va[4]+g&4294967295};
te.prototype.update=function(a,b){if(a!=null){b===void 0&&(b=a.length);for(var c=b-this.blockSize,d=0,e=this.hg,f=this.rd;d<b;){if(f==0)for(;d<=c;)ue(this,a,d),d+=this.blockSize;if(typeof a==="string")for(;d<b;){if(e[f]=a.charCodeAt(d),++f,++d,f==this.blockSize){ue(this,e);f=0;break}}else for(;d<b;)if(e[f]=a[d],++f,++d,f==this.blockSize){ue(this,e);f=0;break}}this.rd=f;this.bc+=b}};
te.prototype.digest=function(){var a=[],b=this.bc*8;this.rd<56?this.update(this.yf,56-this.rd):this.update(this.yf,this.blockSize-(this.rd-56));for(var c=this.blockSize-1;c>=56;c--)this.hg[c]=b&255,b/=256;ue(this,this.hg);for(c=b=0;c<5;c++)for(var d=24;d>=0;d-=8)a[b]=this.va[c]>>d&255,++b;return a};var ve=function(){this.Ah=new te};k=ve.prototype;k.reset=function(){this.Ah.reset()};k.updateByteArray=function(a){this.Ah.update(a)};k.digestByteArray=function(){return this.Ah.digest()};k.updateString=function(a){a=unescape(encodeURIComponent(a));for(var b=[],c=a.length,d=0;d<c;++d)b.push(a.charCodeAt(d));this.updateByteArray(b)};
k.digestString=function(){for(var a=this.digestByteArray(),b="",c=0;c<a.length;c++)b+="0123456789ABCDEF".charAt(Math.floor(a[c]/16))+"0123456789ABCDEF".charAt(a[c]%16);return b};var we=D.crypto,xe=!1,ye=0,ze=0,Ae=1,Be=0,Ce="",De=function(a){a=a||D.event;var b=a.screenX+a.clientX<<16;b+=a.screenY+a.clientY;b*=(new Date).getTime()%1E6;Ae=Ae*b%Be;ye>0&&++ze==ye&&Ad(D,"mousemove",De,"remove","de")},Ee=function(a){var b=new ve;b.updateString(a);return b.digestString()};xe=!!we&&typeof we.getRandomValues=="function";
xe||(Be=(screen.width*screen.width+screen.height)*1E6,Ce=Ee(Yc.cookie+"|"+Yc.location+"|"+(new Date).getTime()+"|"+Math.random()),ye=Ba("random/maxObserveMousemove")||0,ye!=0&&Bd(D,"mousemove",De));var Fe=function(){var a=Hd.onl;if(!a){a=kd();Hd.onl=a;var b=kd();a.e=function(c){var d=b[c];d&&(delete b[c],d())};a.a=function(c,d){b[c]=d};a.r=function(c){delete b[c]}}return a},Ge=function(a,b){b=b.onload;return typeof b==="function"?(Fe().a(a,b),b):null},He=function(a){E(/^\w+$/.test(a),"Unsupported id - "+a);return'onload="window.___jsl.onl.e(&#34;'+a+'&#34;)"'},Ie=function(a){Fe().r(a)};var Je={allowtransparency:"true",frameborder:"0",hspace:"0",marginheight:"0",marginwidth:"0",scrolling:"no",style:"",tabindex:"0",vspace:"0",width:"100%"},Ke={allowtransparency:!0,onload:!0},Le=0,Me=function(a,b){var c=0;do var d=b.id||["I",Le++,"_",(new Date).getTime()].join("");while(a.getElementById(d)&&++c<5);E(c<5,"Error creating iframe id");return d},Ne=function(a,b){return a?b+"/"+a:""},Oe=function(a,b,c,d){var e={},f={};a.documentMode&&a.documentMode<9&&(e.hostiemode=a.documentMode);ld(d.queryParams||
{},e);ld(d.fragmentParams||{},f);var g=d.pfname;var h=kd();Ba("iframes/dropLegacyIdParam")||(h.id=c);h._gfid=c;h.parent=a.location.protocol+"//"+a.location.host;c=nd(a.location.href,"parent");g=g||"";!g&&c&&(g=nd(a.location.href,"_gfid","")||nd(a.location.href,"id",""),g=Ne(g,nd(a.location.href,"pfname","")));g||(c=ne(nd(a.location.href,"jcp","")))&&typeof c=="object"&&(g=Ne(c.id,c.pfname));h.pfname=g;d.connectWithJsonParam&&(g={},g.jcp=oe(h),h=g);g=nd(b,"rpctoken")||e.rpctoken||f.rpctoken;if(!g){if(!(g=
d.rpctoken)){g=String;c=Math;var l=c.round;if(xe){var m=new D.Uint32Array(1);we.getRandomValues(m);m=Number("0."+m[0])}else m=Ae,m+=parseInt(Ce.substr(0,20),16),Ce=Ee(Ce),m/=Be+1.2089258196146292E24;g=g(l.call(c,m*1E8))}h.rpctoken=g}d.rpctoken=g;ld(h,d.connectWithQueryParams?e:f);h=a.location.href;a=kd();(g=nd(h,"_bsh",Hd.bsh))&&(a._bsh=g);(h=Hd.dpo?Hd.h:nd(h,"jsh",Hd.h))&&(a.jsh=h);d.hintInFragment?ld(a,f):ld(a,e);d=d.paramsSerializer;b=td(b);b.query.push.apply(b.query,vd(e,d));b.Rb.push.apply(b.Rb,
vd(f,d));return ud(b)},Pe=function(a){E(!a||yd.test(a),"Illegal url for new iframe - "+a)},Qe=function(a,b,c,d,e){Pe(c.src);var f,g=Ge(d,c),h=g?He(d):"";try{document.all&&(f=a.createElement('<iframe frameborder="'+jd(String(c.frameborder))+'" scrolling="'+jd(String(c.scrolling))+'" '+h+' name="'+jd(String(c.name))+'"/>'))}catch(m){}finally{f||(f=(a?new Xc(Wc(a)):nb||(nb=new Xc)).createElement("IFRAME"),g&&(f.onload=function(){f.onload=null;g.call(this)},Ie(d)))}f.setAttribute("ng-non-bindable","");
for(var l in c)a=c[l],l==="style"&&typeof a==="object"?ld(a,f.style):Ke[l]||f.setAttribute(l,String(a));(l=e&&e.beforeNode||null)||e&&e.dontclear||Cd(b);b.insertBefore(f,l);f=l?l.previousSibling:b.lastChild;c.allowtransparency&&(f.allowTransparency=!0);return f};var Re=/^:[\w]+$/,Se=/:([a-zA-Z_]+):/g,Te=function(a,b){a=Fd()||"0";var c=Gd();var d=Fd()||a;var e=Gd(),f="";d&&(f+="u/"+encodeURIComponent(String(d))+"/");e&&(f+="b/"+encodeURIComponent(String(e))+"/");d=f||null;(f=(e=Ba("isLoggedIn")===!1)?"_/im/":"")&&(d="");var g=Ba("iframes/:socialhost:"),h=Ba("iframes/:im_socialhost:");zd={socialhost:g,ctx_socialhost:e?h:g,session_index:a,session_delegate:c,session_prefix:d,im_prefix:f};return zd[b]||""},Ue=function(a){var b=a;Re.test(a)&&(b=Ba("iframes/"+b.substring(1)+
"/url"),E(!!b,"Unknown iframe url config for - "+a));return xd(Yc,b.replace(Se,Te))},Ve=function(a,b,c){c=c||{};var d=c.attributes||{};E(!(c.allowPost||c.forcePost)||!d.onload,"onload is not supported by post iframe (allowPost or forcePost)");a=Ue(a);d=b.ownerDocument||Yc;var e=Me(d,c);a=Oe(d,a,e,c);var f=c,g=kd();ld(Je,g);ld(f.attributes,g);g.name=g.id=e;g.src=a;c.eurl=a;c=(f=c)||{};var h=!!c.allowPost;if(c.forcePost||h&&a.length>2E3){c=td(a);g.src="";f.dropDataPostorigin||(g["data-postorigin"]=
a);a=Qe(d,b,g,e);if(navigator.userAgent.indexOf("WebKit")!=-1){var l=a.contentWindow.document;l.open();g=l.createElement("div");h={};var m=e+"_inner";h.name=m;h.src="";h.style="display:none";Qe(d,g,h,m,f)}g=(f=c.query[0])?f.split("&"):[];f=[];for(h=0;h<g.length;h++)m=g[h].split("=",2),f.push([decodeURIComponent(m[0]),decodeURIComponent(m[1])]);c.query=[];g=ud(c);E(yd.test(g),"Invalid URL: "+g);c=d.createElement("form");c.method="POST";c.target=e;c.style.display="none";e=ib(g);e!==void 0&&(c.action=
e);for(e=0;e<f.length;e++)g=d.createElement("input"),g.type="hidden",g.name=f[e][0],g.value=f[e][1],c.appendChild(g);b.appendChild(c);c.submit();c.parentNode.removeChild(c);l&&l.close();b=a}else b=Qe(d,b,g,e,f);return b};window.osapi=window.osapi||{};window.___jsl=window.___jsl||{};
(window.___jsl.cd=window.___jsl.cd||[]).push({gwidget:{parsetags:"explicit"},appsapi:{plus_one_service:"/plus/v1"},csi:{rate:.01},poshare:{hangoutContactPickerServer:"https://plus.google.com"},gappsutil:{required_scopes:["https://www.googleapis.com/auth/plus.me","https://www.googleapis.com/auth/plus.people.recommended"],display_on_page_ready:!1},appsutil:{required_scopes:["https://www.googleapis.com/auth/plus.me","https://www.googleapis.com/auth/plus.people.recommended"],display_on_page_ready:!1},
"oauth-flow":{authUrl:"https://accounts.google.com/o/oauth2/auth",proxyUrl:"https://accounts.google.com/o/oauth2/postmessageRelay",redirectUri:"postmessage"},iframes:{sharebox:{params:{json:"&"},url:":socialhost:/:session_prefix:_/sharebox/dialog"},plus:{url:":socialhost:/:session_prefix:_/widget/render/badge?usegapi=1"},":socialhost:":"https://apis.google.com",":im_socialhost:":"https://plus.googleapis.com",domains_suggest:{url:"https://domains.google.com/suggest/flow"},card:{params:{s:"#",userid:"&"},
url:":socialhost:/:session_prefix:_/hovercard/internalcard"},":signuphost:":"https://plus.google.com",":gplus_url:":"https://plus.google.com",plusone:{url:":socialhost:/:session_prefix:_/+1/fastbutton?usegapi=1"},plus_share:{url:":socialhost:/:session_prefix:_/+1/sharebutton?plusShare=true&usegapi=1"},plus_circle:{url:":socialhost:/:session_prefix:_/widget/plus/circle?usegapi=1"},plus_followers:{url:":socialhost:/_/im/_/widget/render/plus/followers?usegapi=1"},configurator:{url:":socialhost:/:session_prefix:_/plusbuttonconfigurator?usegapi=1"},
appcirclepicker:{url:":socialhost:/:session_prefix:_/widget/render/appcirclepicker"},page:{url:":socialhost:/:session_prefix:_/widget/render/page?usegapi=1"},person:{url:":socialhost:/:session_prefix:_/widget/render/person?usegapi=1"},community:{url:":ctx_socialhost:/:session_prefix::im_prefix:_/widget/render/community?usegapi=1"},follow:{url:":socialhost:/:session_prefix:_/widget/render/follow?usegapi=1"},commentcount:{url:":socialhost:/:session_prefix:_/widget/render/commentcount?usegapi=1"},comments:{url:":socialhost:/:session_prefix:_/widget/render/comments?usegapi=1"},
youtube:{url:":socialhost:/:session_prefix:_/widget/render/youtube?usegapi=1"},reportabuse:{url:":socialhost:/:session_prefix:_/widget/render/reportabuse?usegapi=1"},additnow:{url:":socialhost:/additnow/additnow.html"},appfinder:{url:"https://workspace.google.com/:session_prefix:marketplace/appfinder?usegapi=1"},":source:":"1p"},poclient:{update_session:"google.updateSessionCallback"},"googleapis.config":{rpc:"/rpc",root:"https://content.googleapis.com","root-1p":"https://clients6.google.com",useGapiForXd3:!0,
xd3:"/static/proxy.html",auth:{useInterimAuth:!1}},report:{apis:["iframes\\..*","gadgets\\..*","gapi\\.appcirclepicker\\..*","gapi\\.client\\..*"],rate:1E-4},client:{perApiBatch:!0}});var We=function(a){var b=window.___jsl=window.___jsl||{};b[a]=b[a]||[];return b[a]},Xe=function(a){var b=window.___jsl=window.___jsl||{};b.cfg=!a&&b.cfg||{};return b.cfg},Ye=function(a){return typeof a==="object"&&/\[native code\]/.test(a.push)},Ze=function(a,b,c){if(b&&typeof b==="object")for(var d in b)!Object.prototype.hasOwnProperty.call(b,d)||c&&d==="___goc"&&typeof b[d]==="undefined"||(a[d]&&b[d]&&typeof a[d]==="object"&&typeof b[d]==="object"&&!Ye(a[d])&&!Ye(b[d])?Ze(a[d],b[d]):b[d]&&typeof b[d]===
"object"?(a[d]=Ye(b[d])?[]:{},Ze(a[d],b[d])):a[d]=b[d])},bf=function(a,b){if(a&&!/^\s+$/.test(a)){for(;a.charCodeAt(a.length-1)==0;)a=a.substring(0,a.length-1);var c=""+a,d=We("dm");d.push(20);try{var e=window.JSON.parse(a)}catch(g){}if(typeof e==="object")return d.push(21),e;try{e=window.JSON.parse("{"+a+"}")}catch(g){}if(typeof e==="object")return d.push(22),e;a=a.replace(RegExp("([^\"',{}\\s]+?)\\s*:\\s*(.*?)[,}\\s]","g"),function(g,h,l){l=l.startsWith('"')?"%DOUBLE_QUOTE%"+l.substring(1):l;l=
l.endsWith('"')?l.slice(0,-1)+"%DOUBLE_QUOTE%":l;return"%DOUBLE_QUOTE%"+h+"%DOUBLE_QUOTE%:"+l});a=a.replace(/\\'/g,"%SINGLE_QUOTE%");a=a.replace(/"/g,'\\"');a=a.replace(/'/g,'"');a=a.replace(/%SINGLE_QUOTE%/g,"'");a=a.replace(/%DOUBLE_QUOTE%/g,'"');try{e=window.JSON.parse(a)}catch(g){}if(typeof e==="object")return d.push(23),e;try{e=window.JSON.parse("{"+a+"}")}catch(g){}if(typeof e==="object")return d.push(24),e;a=document.getElementsByTagName("script")||[];var f;a.length>0&&(f=a[0].nonce||a[0].getAttribute("nonce"));
if(f&&f===b||!f&&$e())if(e=af(c),d.push(25),typeof e==="object")return e;return{}}},$e=function(){var a=window.location.hostname;return a?/(^|\.)(2mdn|ampproject|android|appspot|blogger|blogspot|chrome|chromium|doubleclick|gcpnode|ggpht|gmail|google|google-analytics|googleadservices|googleapis|googleapis-cn|googleoptimize|googlers|googlesource|googlesyndication|googletagmanager|googletagservices|googleusercontent|googlevideo|gstatic|tiltbrush|waze|withgoogle|youtube|ytimg)(\.com?|\.net|\.org)?(\.[a-z][a-z]|\.cat)?$/.test(a):
!1},af=function(a){try{var b=(new Function("return ("+a+"\n)"))()}catch(c){}if(typeof b==="object")return b;try{b=(new Function("return ({"+a+"\n})"))()}catch(c){}return b},cf=function(a,b){var c={___goc:void 0};a.length&&a[a.length-1]&&Object.hasOwnProperty.call(a[a.length-1],"___goc")&&typeof a[a.length-1].___goc==="undefined"&&(c=a.pop());Ze(c,b);a.push(c)},df=function(a){Xe(!0);var b=window.___gcfg,c=We("cu"),d=window.___gu;b&&b!==d&&(cf(c,b),window.___gu=b);b=We("cu");var e=document.getElementsByTagName("script")||
[];d=[];var f=[];f.push.apply(f,We("us"));for(var g=0;g<e.length;++g)for(var h=e[g],l=0;l<f.length;++l)h.src&&h.src.indexOf(f[l])==0&&d.push(h);d.length==0&&e.length>0&&e[e.length-1].src&&d.push(e[e.length-1]);for(e=0;e<d.length;++e)d[e].getAttribute("gapi_processed")||(d[e].setAttribute("gapi_processed",!0),(f=d[e])?(g=f.nodeType,f=g==3||g==4?f.nodeValue:f.textContent||""):f=void 0,g=d[e].nonce||d[e].getAttribute("nonce"),(f=bf(f,g))&&b.push(f));a&&cf(c,a);d=We("cd");a=0;for(b=d.length;a<b;++a)Ze(Xe(),
d[a],!0);d=We("ci");a=0;for(b=d.length;a<b;++a)Ze(Xe(),d[a],!0);a=0;for(b=c.length;a<b;++a)Ze(Xe(),c[a],!0)};var ef=function(){var a=window.__GOOGLEAPIS;a&&(a.googleapis&&!a["googleapis.config"]&&(a["googleapis.config"]=a.googleapis),$c(Hd,"ci",[]).push(a),window.__GOOGLEAPIS=void 0)};ef&&ef();df();t("gapi.config.get",function(a,b){var c=Xe();if(!a)return c;a=a.split("/");for(var d=0,e=a.length;c&&typeof c==="object"&&d<e;++d)c=c[a[d]];return d===a.length&&c!==void 0?c:b});t("gapi.config.update",function(a,b){var c;if(typeof a==="string"){var d=c={};a=a.split("/");for(var e=0,f=a.length;e<f-1;++e){var g={};d=d[a[e]]=g}d[a[e]]=b}else c=a;df(c)});var ff=function(a){if(!a)return"";if(/^about:(?:blank|srcdoc)$/.test(a))return window.origin||"";a.indexOf("blob:")===0&&(a=a.substring(5));a=a.split("#")[0].split("?")[0];a=a.toLowerCase();a.indexOf("//")==0&&(a=window.location.protocol+a);/^[\w\-]*:\/\//.test(a)||(a=window.location.href);var b=a.substring(a.indexOf("://")+3),c=b.indexOf("/");c!=-1&&(b=b.substring(0,c));c=a.substring(0,a.indexOf("://"));if(!c)throw Error("URI is missing protocol: "+a);if(c!=="http"&&c!=="https"&&c!=="chrome-extension"&&
c!=="moz-extension"&&c!=="file"&&c!=="android-app"&&c!=="chrome-search"&&c!=="chrome-untrusted"&&c!=="chrome"&&c!=="app"&&c!=="devtools")throw Error("Invalid URI scheme in origin: "+c);a="";var d=b.indexOf(":");if(d!=-1){var e=b.substring(d+1);b=b.substring(0,d);if(c==="http"&&e!=="80"||c==="https"&&e!=="443")a=":"+e}return c+"://"+b+a};var jf=function(a){this.lb=a;this.Context=gf(a);this.Iframe=hf(a)};k=jf.prototype;k.CROSS_ORIGIN_IFRAMES_FILTER=function(a){return this.lb().CROSS_ORIGIN_IFRAMES_FILTER(a)};k.SAME_ORIGIN_IFRAMES_FILTER=function(a){return this.lb().SAME_ORIGIN_IFRAMES_FILTER(a)};k.create=function(a,b,c){return this.lb().create(a,b,c)};k.getBeforeOpenStyle=function(a){return this.lb().getBeforeOpenStyle(a)};k.getContext=function(){return this.lb().getContext()};k.getStyle=function(a){return this.lb().getStyle(a)};
k.makeWhiteListIframesFilter=function(a){return this.lb().makeWhiteListIframesFilter(a)};k.registerBeforeOpenStyle=function(a,b){return this.lb().registerBeforeOpenStyle(a,b)};k.registerIframesApi=function(a,b,c){return this.lb().registerIframesApi(a,b,c)};k.registerIframesApiHandler=function(a,b,c){return this.lb().registerIframesApiHandler(a,b,c)};k.registerStyle=function(a,b){return this.lb().registerStyle(a,b)};
function gf(a){var b=function(c){return new (a().Context)(c)};b.prototype.addOnConnectHandler=function(c,d,e,f){return a().Context.prototype.addOnConnectHandler.apply(this,[c,d,e,f])};b.prototype.addOnOpenerHandler=function(c,d,e){return a().Context.prototype.addOnOpenerHandler.apply(this,[c,d,e])};b.prototype.closeSelf=function(c,d,e){return a().Context.prototype.closeSelf.apply(this,[c,d,e])};b.prototype.connectIframes=function(c,d){a().Context.prototype.connectIframes.apply(this,[c,d])};b.prototype.getFrameName=
function(){return a().Context.prototype.getFrameName.apply(this)};b.prototype.getGlobalParam=function(c){a().Context.prototype.getGlobalParam.apply(this,[c])};b.prototype.getParentIframe=function(){return a().Context.prototype.getParentIframe.apply(this)};b.prototype.getWindow=function(){return a().Context.prototype.getWindow.apply(this)};b.prototype.isDisposed=function(){return a().Context.prototype.isDisposed.apply(this)};b.prototype.open=function(c,d){return a().Context.prototype.open.apply(this,
[c,d])};b.prototype.openChild=function(c){return a().Context.prototype.openChild.apply(this,[c])};b.prototype.ready=function(c,d,e,f){a().Context.prototype.ready.apply(this,[c,d,e,f])};b.prototype.removeOnConnectHandler=function(c){a().Context.prototype.removeOnConnectHandler.apply(this,[c])};b.prototype.restyleSelf=function(c,d,e){return a().Context.prototype.restyleSelf.apply(this,[c,d,e])};b.prototype.setCloseSelfFilter=function(c){a().Context.prototype.setCloseSelfFilter.apply(this,[c])};b.prototype.setGlobalParam=
function(c,d){a().Context.prototype.setGlobalParam.apply(this,[c,d])};b.prototype.setRestyleSelfFilter=function(c){a().Context.prototype.setRestyleSelfFilter.apply(this,[c])};return b}
function hf(a){var b=function(c,d,e,f){return new (a().Iframe)(c,d,e,f)};b.prototype.applyIframesApi=function(c){a().Iframe.prototype.applyIframesApi(c)};b.prototype.close=function(c,d){return a().Iframe.prototype.close.apply(this,[c,d])};b.prototype.getContext=function(){return a().Iframe.prototype.getContext.apply(this,[])};b.prototype.getFrameName=function(){return a().Iframe.prototype.getFrameName.apply(this,[])};b.prototype.getId=function(){return a().Iframe.prototype.getId.apply(this,[])};b.prototype.getIframeEl=
function(){return a().Iframe.prototype.getIframeEl.apply(this,[])};b.prototype.getOrigin=function(){return a().Iframe.prototype.getOrigin.apply(this,[])};b.prototype.getParam=function(c){a().Iframe.prototype.getParam.apply(this,[c])};b.prototype.getSiteEl=function(){return a().Iframe.prototype.getSiteEl.apply(this,[])};b.prototype.getWindow=function(){return a().Iframe.prototype.getWindow.apply(this,[])};b.prototype.isDisposed=function(){return a().Iframe.prototype.isDisposed.apply(this,[])};b.prototype.ping=
function(c,d){return a().Iframe.prototype.ping.apply(this,[c,d])};b.prototype.register=function(c,d,e){a().Iframe.prototype.register.apply(this,[c,d,e])};b.prototype.registerWasClosed=function(c,d){a().Iframe.prototype.registerWasClosed.apply(this,[c,d])};b.prototype.registerWasRestyled=function(c,d){a().Iframe.prototype.registerWasRestyled.apply(this,[c,d])};b.prototype.restyle=function(c,d){return a().Iframe.prototype.restyle.apply(this,[c,d])};b.prototype.send=function(c,d,e,f){return a().Iframe.prototype.send.apply(this,
[c,d,e,f])};b.prototype.setParam=function(c,d){a().Iframe.prototype.setParam.apply(this,[c,d])};b.prototype.setSiteEl=function(c){a().Iframe.prototype.setSiteEl.apply(this,[c])};b.prototype.unregister=function(c,d){a().Iframe.prototype.unregister.apply(this,[c,d])};return b};function kf(a,b){b=b===void 0?new Set:b;if(b.has(a))return"(Recursive reference)";switch(typeof a){case "object":if(a){var c=Object.getPrototypeOf(a);switch(c){case Map.prototype:case Set.prototype:case Array.prototype:b.add(a);var d="["+Array.from(a,function(e){return kf(e,b)}).join(", ")+"]";b.delete(a);c!==Array.prototype&&(d=lf(c.constructor)+"("+d+")");return d;case Object.prototype:return b.add(a),c="{"+Object.entries(a).map(function(e){var f=ma(e);e=f.next().value;f=f.next().value;return e+
": "+kf(f,b)}).join(", ")+"}",b.delete(a),c;default:return d="Object",c&&c.constructor&&(d=lf(c.constructor)),typeof a.toString==="function"&&a.toString!==Object.prototype.toString?d+"("+String(a)+")":"(object "+d+")"}}break;case "function":return"function "+lf(a);case "number":if(!Number.isFinite(a))return String(a);break;case "bigint":return a.toString(10)+"n";case "symbol":return a.toString()}return JSON.stringify(a)}
function lf(a){var b=a.displayName;return b&&typeof b==="string"||(b=a.name)&&typeof b==="string"?b:(a=/function\s+([^\(]+)/m.exec(String(a)))?a[1]:"(Anonymous)"};function mf(a){var b=nf,c=of,d=[];pf(b,a,d)||qf.apply(null,[void 0,c,"Guard "+b.wi().trim()+" failed:"].concat(na(d.reverse())))}function pf(a,b,c){var d=a(b,c);d||rf(c,function(){var e="";e.length>0&&(e+=": ");return e+"Expected "+a.wi().trim()+", got "+kf(b)});return d}function rf(a,b){a==null||a.push((typeof b==="function"?b():b).trim())}var of=void 0;function sf(a){return typeof a==="function"?a():a}
function qf(){throw Error(qa.apply(0,arguments).map(sf).filter(Boolean).join("\n").trim().replace(/:$/,""));}var nf=function(a,b){a.Hm=!0;a.wi=typeof b==="function"?b:function(){return b};return a}(function(a){return a!==null&&a!==void 0},"exists");var tf=function(){this.Bb=[];this.ei=this.ik=this.dk=!1};tf.prototype.lb=function(a){this.ei=!0;return this.Bb.length?uf(this,this.Bb[0],a):void 0};var uf=function(a,b,c){c=c===void 0?function(d){return new d}:c;if(!b.Re)return b.instance;c=c(b.Re);a.ik&&(delete b.Re,b.instance=c);return c},vf=function(){tf.apply(this,arguments)};p(vf,tf);
var xf=function(a){var b=wf.di;y(b.dk||!b.ei,"Cannot register new delegates after instantiation.");var c=a.priority,d=~Eb(b.Bb,function(f){return f.priority<c?-1:1}),e=d>0?b.Bb[d-1]:null;e&&e.priority<=c&&y(!1,"two delegates registered with same priority (%s): %s and %s",c,e.Re||e.instance,a.Re||a.instance);b.Bb.splice(d,0,a)};var wf=new function(){var a=this;this.di=new vf;this.instance=new jf(function(){var b=a.di.lb();mf(b);return b()})};xf({instance:function(){var a=window.gapi;mf(a);a=a.iframes;mf(a);return a},priority:1});var yf={height:!0,width:!0},zf=/^(?!-*(?:expression|(?:moz-)?binding))(?:[.#]?-?(?:[_a-z0-9-]+)(?:-[_a-z0-9-]+)*-?|-?(?:[0-9]+(?:\.[0-9]*)?|\.[0-9]+)(?:[a-z]{1,2}|%)?|!important|)$/i,Af=function(a){typeof a==="number"&&(a=String(a)+"px");return a};var Bf=typeof AsyncContext!=="undefined"&&typeof AsyncContext.Snapshot==="function"?function(a){return a&&AsyncContext.Snapshot.wrap(a)}:function(a){return a};var Cf=function(a,b){this.nl=100;this.qk=a;this.Ll=b;this.wf=0;this.af=null};Cf.prototype.get=function(){if(this.wf>0){this.wf--;var a=this.af;this.af=a.next;a.next=null}else a=this.qk();return a};Cf.prototype.put=function(a){this.Ll(a);this.wf<this.nl&&(this.wf++,a.next=this.af,this.af=a)};function Df(a){r.setTimeout(function(){throw a;},0)};var Ef=function(){this.Uf=this.Xc=null};Ef.prototype.add=function(a,b){var c=Ff.get();c.set(a,b);this.Uf?this.Uf.next=c:(y(!this.Xc),this.Xc=c);this.Uf=c};Ef.prototype.remove=function(){var a=null;this.Xc&&(a=this.Xc,this.Xc=this.Xc.next,this.Xc||(this.Uf=null),a.next=null);return a};var Ff=new Cf(function(){return new Gf},function(a){return a.reset()}),Gf=function(){this.next=this.scope=this.Vd=null};Gf.prototype.set=function(a,b){this.Vd=a;this.scope=b;this.next=null};
Gf.prototype.reset=function(){this.next=this.scope=this.Vd=null};var Hf=r.console&&r.console.createTask?r.console.createTask.bind(r.console):void 0,If=Hf?Symbol("consoleTask"):void 0;
function Jf(a,b){function c(){var h=qa.apply(0,arguments),l=this;return g.run(function(){return a.call.apply(a,[l].concat(na(h)))})}b=b===void 0?"anonymous":b;if(If&&a[If])return a;var d=a,e,f=(e=Kf)==null?void 0:e();a=function(){var h=qa.apply(0,arguments),l,m=(l=Kf)==null?void 0:l();if(f!==m)throw Error(b+" was scheduled in '"+f+"' but called in '"+m+"'.\nMake sure your test awaits all async calls.\n\nTIP: To help investigate, debug the test in Chrome and look at the async portion\nof the call stack to see what originally scheduled the callback.  Then, make the\ntest wait for the relevant asynchronous work to finish.");return d.call.apply(d,
[this].concat(na(h)))};if(!Hf)return a;var g=Hf(a.name||b);c[qb(If)]=g;return c}var Kf;var Lf,Mf=!1,Nf=new Ef,Pf=function(a,b){a=Jf(a,"goog.async.run");Lf||Of();Mf||(Lf(),Mf=!0);Nf.add(a,b)},Of=function(){var a=Promise.resolve(void 0);Lf=function(){a.then(Qf)}};function Qf(){for(var a;a=Nf.remove();){try{a.Vd.call(a.scope)}catch(b){Df(b)}Ff.put(a)}Mf=!1};var Rf=function(){},Sf=function(a){return typeof a==="function"};var Tf=function(a){if(!a)return!1;try{return!!a.$goog_Thenable}catch(b){return!1}};var F=function(a,b){this.Ba=0;this.Ua=void 0;this.Zc=this.Wb=this.Ha=null;this.Xe=this.xg=!1;if(a!=Rf)try{var c=this;a.call(b,function(d){Uf(c,2,d)},function(d){if(!(d instanceof Vf))try{if(d instanceof Error)throw d;throw Error("Promise rejected.");}catch(e){}Uf(c,3,d)})}catch(d){Uf(this,3,d)}},Wf=function(){this.next=this.context=this.vd=this.Pc=this.child=null;this.Od=!1};Wf.prototype.reset=function(){this.context=this.vd=this.Pc=this.child=null;this.Od=!1};
var Xf=new Cf(function(){return new Wf},function(a){a.reset()}),Yf=function(a,b,c){var d=Xf.get();d.Pc=a;d.vd=b;d.context=c;return d},G=function(a){if(a instanceof F)return a;var b=new F(Rf);Uf(b,2,a);return b},H=function(a){return new F(function(b,c){c(a)})},$f=function(a,b,c){Zf(a,b,c,null)||Pf(za(b,a))},ag=function(a){return new F(function(b,c){var d=a.length,e=[];if(d)for(var f=function(m,q){d--;e[m]=q;d==0&&b(e)},g=function(m){c(m)},h,l=0;l<a.length;l++)h=a[l],$f(h,za(f,l),g);else b(e)})},bg=
function(a){return new F(function(b){var c=a.length,d=[];if(c)for(var e=function(h,l,m){c--;d[h]=l?{mi:!0,value:m}:{mi:!1,reason:m};c==0&&b(d)},f,g=0;g<a.length;g++)f=a[g],$f(f,za(e,g,!0),za(e,g,!1));else b(d)})};
F.prototype.then=function(a,b,c){a!=null&&tb(a,"opt_onFulfilled should be a function.");b!=null&&tb(b,"opt_onRejected should be a function. Did you pass opt_context as the second argument instead of the third?");return cg(this,Bf(typeof a==="function"?a:null),Bf(typeof b==="function"?b:null),c)};F.prototype.$goog_Thenable=!0;
var eg=function(a,b,c,d){b!=null&&tb(b,"opt_onFulfilled should be a function.");c!=null&&tb(c,"opt_onRejected should be a function. Did you pass opt_context as the second argument instead of the third?");dg(a,Yf(b||Rf,c||null,d))};k=F.prototype;k.Ac=function(a,b){a=Bf(a);b=Yf(a,a,b);b.Od=!0;dg(this,b);return this};k.finally=function(a){var b=this;a=Bf(a);return new Promise(function(c,d){eg(b,function(e){a();c(e)},function(e){a();d(e)})})};k.l=function(a,b){return cg(this,null,Bf(a),b)};k.catch=F.prototype.l;
k.cancel=function(a){if(this.Ba==0){var b=new Vf(a);Pf(function(){fg(this,b)},this)}};
var fg=function(a,b){if(a.Ba==0)if(a.Ha){var c=a.Ha;if(c.Wb){for(var d=0,e=null,f=null,g=c.Wb;g&&(g.Od||(d++,g.child==a&&(e=g),!(e&&d>1)));g=g.next)e||(f=g);e&&(c.Ba==0&&d==1?fg(c,b):(f?(d=f,y(c.Wb),y(d!=null),d.next==c.Zc&&(c.Zc=d),d.next=d.next.next):gg(c),hg(c,e,3,b)))}a.Ha=null}else Uf(a,3,b)},dg=function(a,b){a.Wb||a.Ba!=2&&a.Ba!=3||ig(a);y(b.Pc!=null);a.Zc?a.Zc.next=b:a.Wb=b;a.Zc=b},cg=function(a,b,c,d){b&&(b=Jf(b,"goog.Promise.then"));c&&(c=Jf(c,"goog.Promise.then"));var e=Yf(null,null,null);
e.child=new F(function(f,g){e.Pc=b?function(h){try{var l=b.call(d,h);f(l)}catch(m){g(m)}}:f;e.vd=c?function(h){try{var l=c.call(d,h);l===void 0&&h instanceof Vf?g(h):f(l)}catch(m){g(m)}}:g});e.child.Ha=a;dg(a,e);return e.child};F.prototype.jm=function(a){y(this.Ba==1);this.Ba=0;Uf(this,2,a)};F.prototype.km=function(a){y(this.Ba==1);this.Ba=0;Uf(this,3,a)};
var Uf=function(a,b,c){a.Ba==0&&(a===c&&(b=3,c=new TypeError("Promise cannot resolve to itself")),a.Ba=1,Zf(c,a.jm,a.km,a)||(a.Ua=c,a.Ba=b,a.Ha=null,ig(a),b!=3||c instanceof Vf||jg(a,c)))},Zf=function(a,b,c,d){if(a instanceof F)return eg(a,b,c,d),!0;if(Tf(a))return a.then(b,c,d),!0;if(u(a))try{var e=a.then;if(typeof e==="function")return kg(a,e,b,c,d),!0}catch(f){return c.call(d,f),!0}return!1},kg=function(a,b,c,d,e){var f=!1,g=function(l){f||(f=!0,c.call(e,l))},h=function(l){f||(f=!0,d.call(e,l))};
try{b.call(a,g,h)}catch(l){h(l)}},ig=function(a){a.xg||(a.xg=!0,Pf(a.zk,a))},gg=function(a){var b=null;a.Wb&&(b=a.Wb,a.Wb=b.next,b.next=null);a.Wb||(a.Zc=null);b!=null&&y(b.Pc!=null);return b};F.prototype.zk=function(){for(var a;a=gg(this);)hg(this,a,this.Ba,this.Ua);this.xg=!1};
var hg=function(a,b,c,d){if(c==3&&b.vd&&!b.Od)for(;a&&a.Xe;a=a.Ha)a.Xe=!1;if(b.child)b.child.Ha=null,lg(b,c,d);else try{b.Od?b.Pc.call(b.context):lg(b,c,d)}catch(e){mg.call(null,e)}Xf.put(b)},lg=function(a,b,c){b==2?a.Pc.call(a.context,c):a.vd&&a.vd.call(a.context,c)},jg=function(a,b){a.Xe=!0;Pf(function(){a.Xe&&mg.call(null,b)})},mg=Df,Vf=function(a){mb.call(this,a)};w(Vf,mb);Vf.prototype.name="cancel";var ng,og,pg,qg=/^[\w\.\-]*$/,rg=function(a){return a.getOrigin()===a.getContext().getOrigin()},sg=function(){return!0},tg=function(a){for(var b=kd(),c=0;c<a.length;c++)b[a[c]]=!0;return function(d){return!!b[d.Ib]}},vg=function(a,b,c){return function(d){if(!b.isDisposed()){var e=this.origin,f=b.getOrigin();E(e===f,"Wrong origin "+e+" != "+f);e=this.callback;d=ug(a,d,b);!c&&d.length>0&&ag(d).then(e)}}},ug=function(a,b,c){a=ng[a];if(!a)return[];for(var d=[],e=0;e<a.length;e++)d.push(G(a[e].call(c,
b,c)));return d},wg=function(a,b,c){E(a!="_default","Cannot update default api");og[a]={map:b,filter:c}},xg=function(a,b,c){E(a!="_default","Cannot update default api");$c(og,a,{map:{},filter:rg}).map[b]=c},yg=function(a,b){$c(og,"_default",{map:{},filter:sg}).map[a]=b;dd(pg.kb,function(c){c.register(a,b,sg)})},zg=function(){return pg},Ag=/^https?:\/\/[^\/%\\?#\s]+$/i,Bg={longdesc:!0,name:!0,src:!0,frameborder:!0,marginwidth:!0,marginheight:!0,scrolling:!0,align:!0,height:!0,width:!0,id:!0,"class":!0,
title:!0,tabindex:!0,hspace:!0,vspace:!0,allowtransparency:!0};var Cg=function(a){this.i=a||{}};Cg.prototype.value=function(){return this.i};Cg.prototype.getIframe=function(){return this.i.iframe};var Dg=function(a,b){a.i.role=b;return a},Eg=function(a,b){a.i.data=b;return a};Cg.prototype.ac=function(a){this.i.setRpcReady=a;return this};var Fg=function(a){return a.i.setRpcReady};Cg.prototype.Tc=function(a){this.i.rpctoken=a;return this};var Gg=function(a){return a.i.rpctoken},Hg=function(a){a.i.selfConnect=!0;return a};var Ig=function(a){this.i=a};Ig.prototype.value=function(){return this.i};Ig.prototype.Dj=function(a){this.i.style=a};Ig.prototype.getStyle=function(){return this.i.style};var Jg=function(a,b){a.i.onload=b};function Kg(a){this.i=a||{}}Kg.prototype.value=function(){return this.i};var Mg=function(a){var b=new Lg;b.i.role=a;return b},Ng=function(a,b){a.i.handler=b;return a},Og=function(a,b){a.i.filter=b;return a};Kg.prototype.xe=function(a){this.i.apis=a;return this};var Pg=function(a){this.i=a||{}};k=Pg.prototype;k.value=function(){return this.i};k.setUrl=function(a){this.i.url=a;return this};k.getUrl=function(){return this.i.url};k.Dj=function(a){this.i.style=a};k.getStyle=function(){return this.i.style};k.getId=function(){return this.i.id};k.Tc=function(a){this.i.rpctoken=a;return this};var Qg=function(a,b){a.i.messageHandlers=b;return a},Rg=function(a,b){a.i.messageHandlersFilter=b;return a};Pg.prototype.xe=function(a){this.i.apis=a;return this};
var Sg=function(a,b){a.i.onClose=b};Pg.prototype.getContext=function(){return this.i.context};var Tg=function(a){a.i.attributes=a.i.attributes||{};return new Ig(a.i.attributes)},Ug=function(a,b){a.i.controllerData=b},Vg=function(a){return(a=a.i.timeout)?a:null};var Wg=function(){Cg.apply(this,arguments)};p(Wg,Cg);var Lg=function(){Kg.apply(this,arguments)};p(Lg,Kg);var Xg=function(){Pg.apply(this,arguments)};p(Xg,Pg);var I=function(a){Xg.call(this,a)};p(I,Xg);var Yg=function(a,b){a.i.frameName=b;return a};I.prototype.getFrameName=function(){return this.i.frameName};var Zg=function(a,b){a.i.rpcAddr=b;return a};I.prototype.ob=function(){return this.i.rpcAddr};var $g=function(a,b){a.i.retAddr=b;return a};I.prototype.Fb=function(){return this.i.retAddr};var ah=function(a,b){a.i.origin=b;return a};I.prototype.getOrigin=function(){return this.i.origin};I.prototype.ac=function(a){this.i.setRpcReady=a;return this};
var bh=function(a,b){a.i.context=b},ch=function(a,b){a.i._rpcReadyFn=b};I.prototype.getIframeEl=function(){return this.i.iframeEl};var dh=function(a,b,c){var d=a.ob(),e=b.Fb();$g(Zg(c,a.Fb()+"/"+b.ob()),e+"/"+d);ah(Yg(c,b.getFrameName()),b.getOrigin())};var fh=function(a){this.resolve=this.reject=null;this.promise=new F(v(function(b,c){this.resolve=b;this.reject=c},this));a&&(this.promise=eh(this.promise,a))},eh=function(a,b){return a.then(function(c){try{b(c)}catch(d){}return c})};var gh=function(){this.vb=window.console};gh.prototype.log=function(a){this.vb&&this.vb.log&&this.vb.log(a)};gh.prototype.error=function(a){this.vb&&(this.vb.error?this.vb.error(a):this.vb.log&&this.vb.log(a))};gh.prototype.warn=function(a){this.vb&&(this.vb.warn?this.vb.warn(a):this.vb.log&&this.vb.log(a))};gh.prototype.debug=function(){};var hh=new gh;var ph=function(){this.De={tj:ih?"../"+ih:null,tk:jh,vi:kh,Im:lh,getToken:mh,Jm:nh};this.ub=D;this.kj=this.sk;this.Dk=/MSIE\s*[0-8](\D|$)/.test(window.navigator.userAgent);if(this.De.tj){this.ub=this.De.vi(this.ub,this.De.tj);var a=this.ub.document,b=a.createElement("script");b.setAttribute("type","text/javascript");b.text="window.doPostMsg=function(w,s,o) {window.setTimeout(function(){w.postMessage(s,o);},0);};";a.body.appendChild(b);this.kj=this.ub.doPostMsg}this.Bh={};this.Kh={};a=v(this.Nk,this);
Bd(this.ub,"message",a);$c(Hd,"RPMQ",[]).push(a);this.ub!=this.ub.parent&&oh(this,this.ub.parent,this.Pi(this.ub.name),"*")};ph.prototype.Pi=function(a){return'{"h":"'+escape(a)+'"}'};var qh=function(a){var b=null;a.indexOf('{"h":"')===0&&a.indexOf('"}')===a.length-2&&(b=unescape(a.substring(6,a.length-2)));return b},rh=function(a){if(!/^\s*{/.test(a))return!1;a=ne(a);return a!==null&&typeof a==="object"&&!!a.g};
ph.prototype.Nk=function(a){var b=String(a.data);hh.debug("gapix.rpc.receive("+lh+"): "+(!b||b.length<=512?b:b.substr(0,512)+"... ("+b.length+" bytes)"));var c=b.indexOf("!_")!==0;c||(b=b.substring(2));var d=rh(b);if(!c&&!d){if(!d&&(c=qh(b))){if(this.Bh[c])this.Bh[c]();else this.Kh[c]=1;return}var e=a.origin,f=this.De.tk;this.Dk?D.setTimeout(function(){f(b,e)},0):f(b,e)}};ph.prototype.setup=function(a,b){a===".."||this.Kh[a]?(b(),delete this.Kh[a]):this.Bh[a]=b};
var oh=function(a,b,c,d){var e=rh(c)?"":"!_";hh.debug("gapix.rpc.send("+lh+"): "+(!c||c.length<=512?c:c.substr(0,512)+"... ("+c.length+" bytes)"));a.kj(b,e+c,d)};ph.prototype.sk=function(a,b,c){a.postMessage(b,c)};ph.prototype.send=function(a,b,c){(a=this.De.vi(this.ub,a))&&!a.closed&&oh(this,a,b,c)};var sh=0,th=[],uh={},vh={},wh=D.location.href,xh=nd(wh,"rpctoken"),yh=nd(wh,"parent")||Yc.referrer,ih=nd(wh,"rly"),lh=ih||(D!==D.top||D.opener)&&D.name||"..",zh=null,Ah={},Bh=function(){},Ch={send:Bh,setup:Bh,Pi:Bh},kh=function(a,b){var c=a;b.charAt(0)=="/"&&(b=b.substring(1),c=D.top);if(b.length===0)return c;for(b=b.split("/");b.length;){a=b.shift();a.charAt(0)=="{"&&a.charAt(a.length-1)=="}"&&(a=a.substring(1,a.length-1));var d=a;if(d==="..")c=c==c.parent?c.opener:c.parent;else if(d!==".."&&c.frames[d]){var e=
c;a=d;c=c.frames[d];if(!("postMessage"in c))if(c instanceof HTMLIFrameElement&&"contentWindow"in c)c=c.contentWindow!=null&&"postMessage"in c.contentWindow?c.contentWindow:null;else{d=null;e=ma(e.document.getElementsByTagName("iframe"));for(var f=e.next();!f.done;f=e.next())if(f=f.value,f.getAttribute("id")==a||f.getAttribute("name")==a)d=f;if(d&&"contentWindow"in d)c=d.contentWindow!=null?d.contentWindow:null;else throw Error(c+"is not a window for part "+a);}}else return null}return c},mh=function(a){return(a=
uh[a])&&a.token},Dh=function(a){if(a.f in{})return!1;var b=a.t,c=uh[a.r];a=a.origin;return c&&(c.token===b||!c.token&&!b)&&(a===c.origin||c.origin==="*")},Eh=function(a){var b=a.id.split("/"),c=b[b.length-1],d=a.origin;return function(e){var f=e.origin;return e.f==c&&(d==f||d=="*")}},Hh=function(a,b,c){a=Fh(a);vh[a.name]={Vd:b,he:a.he,Ca:c||Dh};Gh()},Ih={},Jh=function(a,b){(a=Ih["_"+a])&&a[1](this)&&a[0].call(this,b)},Lh=function(a){var b=a.c;if(!b)return Bh;var c=a.r,d=a.g?"legacy__":"";return function(){var e=
[].slice.call(arguments,0);e.unshift(c,d+"__cb",null,b);Kh.apply(null,e)}},nh=function(a){zh=a},Nh=function(a){Ah[a]||(Ah[a]=D.setTimeout(function(){Ah[a]=!1;Mh(a)},0))},Mh=function(a){var b=uh[a];if(b&&b.ready){var c=b.ih;for(b.ih=[];c.length;)Ch.send(a,oe(c.shift()),b.origin)}},Fh=function(a){return a.indexOf("legacy__")===0?{name:a.substring(8),he:!0}:{name:a,he:!1}},Gh=function(){for(var a=Ba("rpc/residenceSec")||60,b=(new Date).getTime()/1E3,c,d=0;c=th[d];++d){var e=c.rpc;if(!e||a>0&&b-c.timestamp>
a)th.splice(d,1),--d;else{var f=e.s,g=vh[f]||vh["*"];if(g)if(th.splice(d,1),--d,e.origin=c.origin,c=Lh(e),e.callback=c,g.Ca(e)){if(f!=="__cb"&&!!g.he!=!!e.g)break;e=g.Vd.apply(e,e.a);e!==void 0&&c(e)}else hh.debug("gapix.rpc.rejected("+lh+"): "+f)}}},Oh=function(a,b,c){th.push({rpc:a,origin:b,timestamp:(new Date).getTime()/1E3});c||Gh()},jh=function(a,b){a=ne(a);Oh(a,b,!1)},Ph=function(a){for(;a.length;)Oh(a.shift(),this.origin,!0);Gh()},Qh=function(a){var b=!1;a=a.split("|");var c=a[0];c.indexOf("/")>=
0&&(b=!0);return{id:c,origin:a[1]||"*",Lg:b}},Rh=function(a,b,c,d){var e=Qh(a);d&&(D.frames[e.id]=D.frames[e.id]||d);a=e.id;if(!uh.hasOwnProperty(a)){c=c||null;d=e.origin;if(a==="..")d=ff(yh),c=c||xh;else if(!e.Lg){var f=Yc.getElementById(a);f&&(f=f.src,d=ff(f),c=c||nd(f,"rpctoken"))}e.origin==="*"&&d||(d=e.origin);uh[a]={token:c,ih:[],origin:d,Tl:b,qj:function(){var g=a;uh[g].ready=1;Mh(g)}};Ch.setup(a,uh[a].qj)}return uh[a].qj},Kh=function(a,b,c,d){a=a||"..";Rh(a);a=a.split("|",1)[0];var e=b,f=
a,g=[].slice.call(arguments,3),h=c,l=lh,m=xh,q=uh[f],x=l,A=Qh(f);if(q&&f!==".."){if(A.Lg){if(!(m=uh[f].Tl)){m=zh?zh.substring(1).split("/"):[lh];x=m.length-1;for(f=D.parent;f!==D.top;){var S=f.parent;if(!x--){for(var Aa=null,Ac=S.frames.length,ke=0;ke<Ac;++ke)S.frames[ke]==f&&(Aa=ke);m.unshift("{"+Aa+"}")}f=S}m="/"+m.join("/")}x=m}else x=l="..";m=q.token}h&&A?(q=Dh,A.Lg&&(q=Eh(A)),Ih["_"+ ++sh]=[h,q],h=sh):h=null;g={s:e,f:l,r:x,t:m,c:h,a:g};e=Fh(e);g.s=e.name;g.g=e.he;uh[a].ih.push(g);Nh(a)};
if(typeof D.postMessage==="function"||typeof D.postMessage==="object")Ch=new ph,Hh("__cb",Jh,function(){return!0}),Hh("_processBatch",Ph,function(){return!0}),Rh("..");var Sh=C("Safari")&&!((xc()?wc("Chromium"):(C("Chrome")||C("CriOS"))&&(xc()||!C("Edge"))||C("Silk"))||(xc()?0:C("Coast"))||(xc()?0:C("Opera"))||(xc()?0:C("Edge"))||(xc()?wc("Microsoft Edge"):C("Edg/"))||(xc()?wc("Opera"):C("OPR"))||C("Firefox")||C("FxiOS")||C("Silk")||C("Android"))&&!(yc()||C("iPad")||C("iPod"));var Uh=function(a,b,c){a.setTimeout(function(){b.closed||c==5?Th(b):(b.close(),c++,Uh(a,b,c))},1E3)},Th=function(a){if(!a.closed&&a.document&&a.document.body)if(a=a.document.body,y(a!=null,"goog.dom.setTextContent expects a non-null value for node"),"textContent"in a)a.textContent="Please close this window.";else if(a.nodeType==3)a.data="Please close this window.";else if(a.firstChild&&a.firstChild.nodeType==3){for(;a.lastChild!=a.firstChild;)a.removeChild(y(a.lastChild));a.firstChild.data="Please close this window."}else{Uc(a);
var b=Wc(a);a.appendChild(b.createTextNode("Please close this window."))}};var J=function(a,b,c,d){this.Cb=!1;this.Qe=a;this.oh=b;this.nd=c;this.Sa=d;this.uj=this.Sa.Fb();this.Ib=this.Sa.getOrigin();this.Yk=this.Sa.getIframeEl();this.Ij=this.Sa.i.where;this.Bb=[];this.applyIframesApi("_default");a=this.Sa.i.apis||[];for(b=0;b<a.length;b++)this.applyIframesApi(a[b]);this.Qe.kb[c]=this};k=J.prototype;k.isDisposed=function(){return this.Cb};
k.dispose=function(){if(!this.isDisposed()){for(var a=0;a<this.Bb.length;a++)this.unregister(this.Bb[a]);delete pg.kb[this.getFrameName()];this.Cb=!0}};k.getContext=function(){return this.Qe};k.getOptions=function(){return this.Sa};k.ob=function(){return this.oh};k.Fb=function(){return this.uj};k.getFrameName=function(){return this.nd};k.getIframeEl=function(){return this.Yk};k.getSiteEl=function(){return this.Ij};k.setSiteEl=function(a){this.Ij=a};k.ac=function(){(0,this.Sa.i._rpcReadyFn)()};
k.setParam=function(a,b){this.Sa.value()[a]=b};k.getParam=function(a){return this.Sa.value()[a]};k.ri=function(){return this.Sa.value()};k.getId=function(){return this.Sa.getId()};k.getOrigin=function(){return this.Ib};var Vh=function(a,b){var c=a.nd;a=a.Qe.getFrameName();return c+":"+a+":"+b};k=J.prototype;
k.register=function(a,b,c){E(!this.isDisposed(),"Cannot register handler on disposed iframe "+a);E((c||rg)(this),"Rejecting untrusted message "+a);c=Vh(this,a);$c(ng,c,[]).push(b)==1&&(this.Bb.push(a),Hh(c,vg(c,this,a==="_g_wasClosed")))};k.unregister=function(a,b){var c=Vh(this,a),d=ng[c];d&&(b?(b=bd.call(d,b),b>=0&&d.splice(b,1)):d.splice(0,d.length),d.length==0&&(b=bd.call(this.Bb,a),b>=0&&this.Bb.splice(b,1),delete vh[Fh(c).name]))};k.Ik=function(){return this.Bb};
k.applyIframesApi=function(a){this.Wf=this.Wf||[];if(!(bd.call(this.Wf,a)>=0)){this.Wf.push(a);a=og[a]||{map:{}};for(var b in a.map)cd(a.map,b)&&this.register(b,a.map[b],a.filter)}};k.getWindow=function(){if(!rg(this))return null;var a=this.Sa.i._popupWindow;if(a)return a;var b=this.oh.split("/");a=this.getContext().getWindow();for(var c=0;c<b.length&&a;c++){var d=b[c];a=".."===d?a==a.parent?a.opener:a.parent:a.frames[d]}return a};
var Wh=function(a){var b={};if(a)for(var c in a)cd(a,c)&&cd(yf,c)&&zf.test(a[c])&&(b[c]=a[c]);return b};k=J.prototype;k.close=function(a,b){return Xh(this,"_g_close",a,b)};k.restyle=function(a,b){return Xh(this,"_g_restyle",a,b)};k.Pl=function(a,b){return Xh(this,"_g_restyleDone",a,b)};k.jk=function(a){return this.getContext().closeSelf(a,void 0,this)};k.Rl=function(a){if(a&&typeof a==="object")return this.getContext().restyleSelf(a,void 0,this)};
k.Sl=function(a){var b=this.Sa.i.onRestyle;b&&b.call(this,a,this);a=a&&typeof a==="object"?Wh(a):{};(b=this.getIframeEl())&&a&&typeof a==="object"&&(cd(a,"height")&&(a.height=Af(a.height)),cd(a,"width")&&(a.width=Af(a.width)),ld(a,b.style))};
k.kk=function(a){var b=this.Sa.i.onClose;b&&b.call(this,a,this);if(b=this.getOptions().i._popupWindow){var c=this.getContext().getWindow().document.getElementById(this.getId());c&&c.parentNode&&c.parentNode.removeChild(c);c=this.getContext().getWindow();Fc&&Sh&&c?(c.focus(),Uh(c,b,0)):(b.close(),Th(b))}b||(b=this.getIframeEl())&&b.parentNode&&b.parentNode.removeChild(b);if(b=this.Sa.i.controller)c={},c.frameName=this.getFrameName(),Xh(b,"_g_disposeControl",c);b=Vh(this,"_g_wasClosed");ug(b,a,this)};
k.registerWasRestyled=function(a,b){this.register("_g_wasRestyled",a,b)};k.registerWasClosed=function(a,b){this.register("_g_wasClosed",a,b)};k.sm=function(){delete this.getContext().kb[this.getFrameName()];this.getContext().getWindow().setTimeout(v(function(){this.dispose()},this),0)};
k.send=function(a,b,c,d){E(!this.isDisposed(),"Cannot send message to disposed iframe - "+a);E((d||rg)(this),"Wrong target for message "+a);c=new fh(c);a=this.Qe.getFrameName()+":"+this.nd+":"+a;Kh(this.oh,a,c.resolve,b);return c.promise};var Xh=function(a,b,c,d){return a.send(b,c,d,sg)};k=J.prototype;k.Fl=function(a){return a};k.ping=function(a,b){return Xh(this,"_g_ping",b,a)};
k.nk=function(a){a=a&&typeof a==="object"?a:{};for(var b=a.rpcAddr,c=(this.ob()+"/"+b).split("/"),d=this.getContext().getWindow(),e;(e=c.shift())&&d;)d=e==".."?d.parent:d.frames[e];E(!!d,"Bad rpc address "+b);a._window=d;a._parentRpcAddr=this.ob();a._parentRetAddr=this.Fb();this.getContext();b=new K(a);this.wl&&this.wl(b,a.controllerData);this.mg=this.mg||[];this.mg.push(b,a.controllerData)};
k.uk=function(a){a=(a||{}).frameName;for(var b=this.mg||[],c=0;c<b.length;c++)if(b[c].getFrameName()===a){a=b.splice(c,1)[0];a.dispose();this.xl&&this.xl(a);return}E(!1,"Unknown contolled iframe to dispose - "+a)};
k.lk=function(a){var b=new I(a);a=new Wg(b.value());if(a.i.selfConnect)var c=this;else(E(Ag.test(b.getOrigin()),"Illegal origin for connected iframe - "+b.getOrigin()),c=this.getContext().kb[b.getFrameName()],c)?Fg(b)&&(c.ac(),Xh(c,"_g_rpcReady")):(b=Yg(ah($g(Zg(new I,b.ob()),b.Fb()),b.getOrigin()),b.getFrameName()).ac(Fg(b)).Tc(Gg(b)),c=Yh(this.getContext(),b.value()));b=this.getContext();var d=a.i.role;a=a.i.data;Zh(b);d=d||"";$c(b.lg,d,[]).push({ff:c,data:a});$h(c,a,b.Sg[d])};
k.Cj=function(a,b){(new I(b)).i._relayedDepth||(b={},Hg(Dg(new Wg(b),"_opener")),Xh(a,"_g_connect",b))};
k.fj=function(a){var b=this,c=a.i.messageHandlers,d=a.i.messageHandlersFilter,e=a.i.onClose;Sg(Rg(Qg(a,null),null),null);return Xh(this,"_g_open",a.value()).then(function(f){var g=new I(f[0]),h=g.getFrameName();f=new I;var l=b.Fb(),m=g.Fb();$g(Zg(f,b.ob()+"/"+g.ob()),m+"/"+l);Yg(f,h);ah(f,g.getOrigin());f.xe(g.i.apis);f.Tc(Gg(a));Qg(f,c);Rg(f,d);Sg(f,e);(g=b.getContext().kb[h])||(g=Yh(b.getContext(),f.value()));return g})};
k.qh=function(a){var b=a.getUrl();E(!b||yd.test(b),"Illegal url for new iframe - "+b);var c=Tg(a).value();b={};for(var d in c)cd(c,d)&&cd(Bg,d)&&(b[d]=c[d]);cd(c,"style")&&(d=c.style,typeof d==="object"&&(b.style=Wh(d)));a.value().attributes=b};
k.Cl=function(a){a=new I(a);this.qh(a);var b=a.i._relayedDepth||0;a.i._relayedDepth=b+1;a.i.openerIframe=this;var c=Gg(a);a.Tc(null);var d=this;return this.getContext().open(a.value()).then(function(e){var f=(new I(e.ri())).i.apis,g=new I;dh(e,d,g);b==0&&Dg(new Wg(g.value()),"_opener");g.ac(!0);g.Tc(c);Xh(e,"_g_connect",g.value());g=new I;ah(Yg($g(Zg(g,e.ob()),e.uj),e.getFrameName()),e.getOrigin()).xe(f);return g.value()})};
k.Ql=function(a){this.getContext().addOnOpenerHandler(function(b){b.send("_g_wasRestyled",a,void 0,sg)},null,sg)};var ai=kd(),bi=kd(),ci=function(a,b){ai[a]=b},di=function(a){return ai[a]},ei=function(a,b){md.load("gapi.iframes.style."+a,b)},fi=function(a,b){bi[a]=b},gi=function(a){return bi[a]};var hi=function(){function a(h,l){h=window.getComputedStyle(h,"").getPropertyValue(l).match(/^([0-9]+)/);return parseInt(h[0],10)}for(var b=0,c=[document.body];c.length>0;){var d=c.shift(),e=d.childNodes;if(typeof d.style!=="undefined"){var f=d.style.overflowY;f||(f=(f=document.defaultView.getComputedStyle(d,null))?f.overflowY:null);if(f!="visible"&&f!="inherit"&&(f=d.style.height,f||(f=(f=document.defaultView.getComputedStyle(d,null))?f.height:""),f.length>0&&f!="auto"))continue}for(d=0;d<e.length;d++){f=
e[d];if(typeof f.offsetTop!=="undefined"&&typeof f.offsetHeight!=="undefined"){var g=f.offsetTop+f.offsetHeight+a(f,"margin-bottom");b=Math.max(b,g)}c.push(f)}}return b+a(document.body,"border-bottom")+a(document.body,"margin-bottom")+a(document.body,"padding-bottom")},ii=function(){var a=0;self.innerHeight?a=self.innerHeight:document.documentElement&&document.documentElement.clientHeight?a=document.documentElement.clientHeight:document.body&&(a=document.body.clientHeight);var b=document.body,c=document.documentElement;
if(document.compatMode==="CSS1Compat"&&c.scrollHeight)return c.scrollHeight!==a?c.scrollHeight:c.offsetHeight;if(navigator.userAgent.indexOf("AppleWebKit")>=0)return hi();if(b&&c){var d=c.scrollHeight,e=c.offsetHeight;c.clientHeight!==e&&(d=b.scrollHeight,e=b.offsetHeight);return d>a?d>e?d:e:d<e?d:e}};var K=function(a){a=a||{};this.Cb=!1;this.wa=kd();this.kb=kd();this.ub=a._window||D;this.hb=this.ub.location.href;this.ij=(this.Xg=ji(this.hb,"parent"))?ji(this.hb,"pfname"):"";this.Pa=this.Xg?ji(this.hb,"_gfid")||ji(this.hb,"id"):"";this.nd=Ne(this.Pa,this.ij);this.Ib=ff(this.hb);if(this.Pa){var b=new I;Zg(b,a._parentRpcAddr||"..");$g(b,a._parentRetAddr||this.Pa);ah(b,ff(this.Xg||this.hb));Yg(b,this.ij);this.Ha=Yh(this,b.value())}else this.Ha=null};k=K.prototype;k.isDisposed=function(){return this.Cb};
k.dispose=function(){if(!this.isDisposed()){for(var a=ma(Object.values(this.kb)),b=a.next();!b.done;b=a.next())b.value.dispose();this.Cb=!0}};k.getFrameName=function(){return this.nd};k.getOrigin=function(){return this.Ib};k.getWindow=function(){return this.ub};k.setGlobalParam=function(a,b){this.wa[a]=b};k.getGlobalParam=function(a){return this.wa[a]};
var Yh=function(a,b){E(!a.isDisposed(),"Cannot attach iframe in disposed context");b=new I(b);b.ob()||Zg(b,b.getId());b.Fb()||$g(b,"..");b.getOrigin()||ah(b,ff(b.getUrl()));b.getFrameName()||Yg(b,Ne(b.getId(),a.nd));var c=b.getFrameName();if(a.kb[c])return a.kb[c];var d=b.ob(),e=d;b.getOrigin()&&(e=d+"|"+b.getOrigin());var f=b.Fb(),g=Gg(b);g||(g=(g=b.getIframeEl())&&(g.getAttribute("data-postorigin")||g.src)||b.getUrl(),g=nd(g,"rpctoken"));ch(b,Rh(e,f,g,b.i._popupWindow));e=((window.gadgets||{}).rpc||
{}).setAuthToken;g&&e&&e(d,g);var h=new J(a,d,c,b),l=b.i.messageHandlersFilter;dd(b.i.messageHandlers,function(m,q){h.register(q,m,l)});Fg(b)&&h.ac();Xh(h,"_g_rpcReady");return h};K.prototype.qh=function(a){Yg(a,null);var b=a.getId();!b||qg.test(b)&&!this.getWindow().document.getElementById(b)||(hh.log("Ignoring requested iframe ID - "+b),a.i.id=null)};var ji=function(a,b){var c=nd(a,b);c||(c=ne(nd(a,"jcp",""))[b]);return c||""};
K.prototype.openChild=function(a){E(!this.isDisposed(),"Cannot open iframe in disposed context");var b=new I(a);ki(this,b);var c=b.getFrameName();if(c&&this.kb[c])return this.kb[c];this.qh(b);c=b.getUrl();E(c,"No url for new iframe");var d=b.i.queryParams||{};d.usegapi="1";b.i.queryParams=d;d=this.yi&&this.yi(c,b);d||(d=b.i.where,E(!!d,"No location for new iframe"),c=Ve(c,d,a),b.i.iframeEl=c,d=c.getAttribute("id"));Zg(b,d).i.id=d;ah(b,ff(b.i.eurl||""));this.Qi&&this.Qi(b,b.getIframeEl());c=Yh(this,
a);c.Cj&&c.Cj(c,a);(a=b.i.onCreate)&&a(c);b.i.disableRelayOpen||c.applyIframesApi("_open");return c};
var li=function(a,b,c){var d=b.i.canvasUrl;if(!d)return c;E(!b.i.allowPost&&!b.i.forcePost,"Post is not supported when using canvas url");var e=b.getUrl();E(e&&ff(e)===a.Ib&&ff(d)===a.Ib,"Wrong origin for canvas or hidden url "+d);b.setUrl(d);b.i.waitForOnload=!0;b.i.canvasUrl=null;return function(f){var g=f.getWindow(),h=g.location.hash;h=Ue(e)+(/#/.test(e)?h.replace(/^#/,"&"):h);g.location.replace(h);c&&c(f)}},mi=function(a,b,c){var d=b.i.relayOpen;if(d){var e=a.getParentIframe();d instanceof J?
(e=d,b.i.relayOpen=0):Number(d)>0&&(b.i.relayOpen=Number(d)-1);if(e){E(!!e.fj,"Relaying iframe open is disabled");if(d=b.getStyle())if(d=bi[d])bh(b,a),d(b.value()),bh(b,null);b.i.openerIframe=null;c.resolve(e.fj(b));return!0}}return!1},ni=function(a,b,c){var d=b.getStyle();if(d)if(E(!!di,"Defer style is disabled, when requesting style "+d),ai[d])ki(a,b);else return ei(d,function(){E(!!ai[d],"Fail to load style - "+d);c.resolve(a.open(b.value()))}),!0;return!1};
K.prototype.open=function(a,b){E(!this.isDisposed(),"Cannot open iframe in disposed context");var c=new I(a);b=li(this,c,b);var d=new fh(b);(b=c.getUrl())&&c.setUrl(Ue(b));if(mi(this,c,d)||ni(this,c,d)||mi(this,c,d))return d.promise;if(Vg(c)!=null){var e=setTimeout(function(){g.getIframeEl().src="about:blank";d.reject({timeout:"Exceeded time limit of :"+Vg(c)+"milliseconds"})},Vg(c)),f=d.resolve;d.resolve=function(h){clearTimeout(e);f(h)}}c.i.waitForOnload&&Jg(Tg(c),function(){d.resolve(g)});var g=
this.openChild(a);c.i.waitForOnload||d.resolve(g);return d.promise};K.prototype.getParentIframe=function(){return this.Ha};var oi=function(a,b){var c=a.getParentIframe(),d=!0;b.filter&&(d=b.filter.call(b.ff,b.params));return G(d).then(function(e){return e&&c?(b.hj&&b.hj.call(a,b.params),e=b.sender?b.sender(b.params):Xh(c,b.message,b.params),b.rm?e.then(function(){return!0}):!0):!1})};k=K.prototype;
k.closeSelf=function(a,b,c){a=oi(this,{sender:function(d){var e=pg.getParentIframe();dd(pg.kb,function(f){f!==e&&Xh(f,"_g_wasClosed",d)});return Xh(e,"_g_closeMe",d)},message:"_g_closeMe",params:a,ff:c,filter:this.getGlobalParam("onCloseSelfFilter")});b=new fh(b);b.resolve(a);return b.promise};k.restyleSelf=function(a,b,c){a=a||{};b=new fh(b);b.resolve(oi(this,{message:"_g_restyleMe",params:a,ff:c,filter:this.getGlobalParam("onRestyleSelfFilter"),rm:!0,hj:this.Oj}));return b.promise};
k.Oj=function(a){a.height==="auto"&&(a.height=ii())};k.setCloseSelfFilter=function(a){this.setGlobalParam("onCloseSelfFilter",a)};k.setRestyleSelfFilter=function(a){this.setGlobalParam("onRestyleSelfFilter",a)};var ki=function(a,b){var c=b.getStyle();if(c){b.Dj(null);var d=ai[c];E(d,"No such style: "+c);bh(b,a);d(b.value());bh(b,null)}};
K.prototype.ready=function(a,b,c,d){var e=b||{},f=this.getParentIframe();this.addOnOpenerHandler(function(h){dd(e,function(l,m){h.register(m,l,d)},this);h!==f&&h.send("_ready",g,void 0,d)},void 0,d);var g=a||{};g.height=g.height||"auto";this.Oj(g);f&&f.send("_ready",g,c,sg)};
K.prototype.connectIframes=function(a,b){a=new Wg(a);var c=new Wg(b),d=Fg(a);b=a.getIframe();var e=c.getIframe();if(e){var f=Gg(a),g=new I;dh(b,e,g);Eg(Dg((new Wg(g.value())).Tc(f),a.i.role),a.i.data).ac(d);var h=new I;dh(e,b,h);Eg(Dg((new Wg(h.value())).Tc(f),c.i.role),c.i.data).ac(!0);Xh(b,"_g_connect",g.value(),function(){d||Xh(e,"_g_connect",h.value())});d&&Xh(e,"_g_connect",h.value())}else c={},Eg(Dg(Hg(new Wg(c)),a.i.role),a.i.data),Xh(b,"_g_connect",c)};
var Zh=function(a){a.lg||(a.lg=kd(),a.Sg=kd())};K.prototype.addOnConnectHandler=function(a,b,c,d){Zh(this);typeof a==="object"?(b=new Lg(a),c=b.i.role||""):(b=Og(Ng(Mg(a),b).xe(c),d),c=a);d=this.lg[c]||[];a=!1;for(var e=0;e<d.length&&!a;e++)$h(this.kb[d[e].ff.getFrameName()],d[e].data,[b]),a=b.i.runOnce;c=$c(this.Sg,c,[]);a||b.i.dontWait||c.push(b)};
K.prototype.removeOnConnectHandler=function(a,b){a=$c(this.Sg,a,[]);if(b)for(var c=!1,d=0;!c&&d<a.length;d++)a[d].i.handler===b&&(c=!0,a.splice(d,1));else a.splice(0,a.length)};var $h=function(a,b,c){c=c||[];for(var d=0;d<c.length;d++){var e=c[d];if(e&&a){var f=e.i.filter||rg;if(a&&f(a)){f=e.i.apis||[];for(var g=0;g<f.length;g++)a.applyIframesApi(f[g]);e.i.handler&&(0,e.i.handler)(a,b);e.i.runOnce&&(c.splice(d,1),--d)}}}};
K.prototype.addOnOpenerHandler=function(a,b,c){var d=this.addOnConnectHandler;a=Og(Ng(Mg("_opener"),a).xe(b),c);a.i.runOnce=!0;d.call(this,a.value())};K.prototype.Qi=function(a,b){var c=a.i.controller;if(c){E(c.Ib===a.getOrigin(),"Wrong controller origin "+this.Ib+" !== "+a.getOrigin());var d=a.ob();Zg(a,c.ob());$g(a,c.Fb());var e=new I;Ug(Zg(e,d),a.i.controllerData);Bd(b,"load",function(){c.send("_g_control",e.value())})}};
var pi=function(a,b,c){a=a.getWindow();var d=a.document,e=c.i.reuseWindow;if(e){var f=c.getId();if(!f)throw Error("If you provide a reuseWindow, you must also provide an ID");}else f=Me(d,c);var g=f,h=c.i.rpcRelayUrl;if(h){h=xd(Yc,h.replace(Se,Te));g=c.i.fragmentParams||{};g.rly=f;c.i.fragmentParams=g;g=c.i.where||d.body;E(!!g,"Cannot open window in a page with no body");var l={};l.src=h;l.style="display:none;";l.id=f;l.name=f;Qe(d,g,l,f);g=f+"_relay"}b=Ue(b);var m=Oe(d,b,f,c.value());c.i.eurl=m;
b=c.i.openAsWindow;typeof b!=="string"&&(b=void 0);c=window.navigator.userAgent||"";/Trident|MSIE/i.test(c)&&/#/.test(c)&&(m="javascript:window.location.replace("+D.JSON.stringify(m).replace(/#/g,"\\x23")+")");if(e){var q=e;setTimeout(function(){q.location.replace(m)})}else q=Sb(a,m,g,b);return{id:f,Uj:q}};K.prototype.yi=function(a,b){if(b.i.openAsWindow){a=pi(this,a,b);var c=a.id;E(!!a.Uj,"Open popup window failed");b.i._popupWindow=a.Uj}return c};ng=kd();og=kd();pg=new K;yg("_g_rpcReady",J.prototype.ac);yg("_g_discover",J.prototype.Ik);yg("_g_ping",J.prototype.Fl);yg("_g_close",J.prototype.jk);yg("_g_closeMe",J.prototype.kk);yg("_g_restyle",J.prototype.Rl);yg("_g_restyleMe",J.prototype.Sl);yg("_g_wasClosed",J.prototype.sm);xg("control","_g_control",J.prototype.nk);xg("control","_g_disposeControl",J.prototype.uk);var qi=pg.getParentIframe();qi&&qi.register("_g_restyleDone",J.prototype.Ql,sg);yg("_g_connect",J.prototype.lk);var ri={};
ri._g_open=J.prototype.Cl;wg("_open",ri,sg);var si={Context:K,Iframe:J,SAME_ORIGIN_IFRAMES_FILTER:rg,CROSS_ORIGIN_IFRAMES_FILTER:sg,makeWhiteListIframesFilter:tg,getContext:zg,registerIframesApi:wg,registerIframesApiHandler:xg,registerStyle:ci,registerBeforeOpenStyle:fi,getStyle:di,getBeforeOpenStyle:gi,create:Ve};xf({instance:function(){return si},priority:2});xg("gapi.load","_g_gapi.load",function(a){return new F(function(b){md.load(a&&typeof a==="object"&&a.features||"",b)})});t("gapi.iframes.registerStyle",ci);t("gapi.iframes.registerBeforeOpenStyle",fi);t("gapi.iframes.getStyle",di);t("gapi.iframes.getBeforeOpenStyle",gi);t("gapi.iframes.registerIframesApi",wg);t("gapi.iframes.registerIframesApiHandler",xg);t("gapi.iframes.getContext",zg);t("gapi.iframes.SAME_ORIGIN_IFRAMES_FILTER",rg);t("gapi.iframes.CROSS_ORIGIN_IFRAMES_FILTER",sg);t("gapi.iframes.makeWhiteListIframesFilter",tg);t("gapi.iframes.Context",K);t("gapi.iframes.Context.prototype.isDisposed",K.prototype.isDisposed);
t("gapi.iframes.Context.prototype.getWindow",K.prototype.getWindow);t("gapi.iframes.Context.prototype.getFrameName",K.prototype.getFrameName);t("gapi.iframes.Context.prototype.getGlobalParam",K.prototype.getGlobalParam);t("gapi.iframes.Context.prototype.setGlobalParam",K.prototype.setGlobalParam);t("gapi.iframes.Context.prototype.open",K.prototype.open);t("gapi.iframes.Context.prototype.openChild",K.prototype.openChild);t("gapi.iframes.Context.prototype.getParentIframe",K.prototype.getParentIframe);
t("gapi.iframes.Context.prototype.closeSelf",K.prototype.closeSelf);t("gapi.iframes.Context.prototype.restyleSelf",K.prototype.restyleSelf);t("gapi.iframes.Context.prototype.setCloseSelfFilter",K.prototype.setCloseSelfFilter);t("gapi.iframes.Context.prototype.setRestyleSelfFilter",K.prototype.setRestyleSelfFilter);t("gapi.iframes.Context.prototype.addOnConnectHandler",K.prototype.addOnConnectHandler);t("gapi.iframes.Context.prototype.removeOnConnectHandler",K.prototype.removeOnConnectHandler);
t("gapi.iframes.Context.prototype.addOnOpenerHandler",K.prototype.addOnOpenerHandler);t("gapi.iframes.Context.prototype.connectIframes",K.prototype.connectIframes);t("gapi.iframes.Iframe",J);t("gapi.iframes.Iframe.prototype.isDisposed",J.prototype.isDisposed);t("gapi.iframes.Iframe.prototype.getContext",J.prototype.getContext);t("gapi.iframes.Iframe.prototype.getFrameName",J.prototype.getFrameName);t("gapi.iframes.Iframe.prototype.getId",J.prototype.getId);
t("gapi.iframes.Iframe.prototype.register",J.prototype.register);t("gapi.iframes.Iframe.prototype.unregister",J.prototype.unregister);t("gapi.iframes.Iframe.prototype.send",J.prototype.send);t("gapi.iframes.Iframe.prototype.applyIframesApi",J.prototype.applyIframesApi);t("gapi.iframes.Iframe.prototype.getIframeEl",J.prototype.getIframeEl);t("gapi.iframes.Iframe.prototype.getSiteEl",J.prototype.getSiteEl);t("gapi.iframes.Iframe.prototype.setSiteEl",J.prototype.setSiteEl);
t("gapi.iframes.Iframe.prototype.getWindow",J.prototype.getWindow);t("gapi.iframes.Iframe.prototype.getOrigin",J.prototype.getOrigin);t("gapi.iframes.Iframe.prototype.close",J.prototype.close);t("gapi.iframes.Iframe.prototype.restyle",J.prototype.restyle);t("gapi.iframes.Iframe.prototype.restyleDone",J.prototype.Pl);t("gapi.iframes.Iframe.prototype.registerWasRestyled",J.prototype.registerWasRestyled);t("gapi.iframes.Iframe.prototype.registerWasClosed",J.prototype.registerWasClosed);
t("gapi.iframes.Iframe.prototype.getParam",J.prototype.getParam);t("gapi.iframes.Iframe.prototype.setParam",J.prototype.setParam);t("gapi.iframes.Iframe.prototype.ping",J.prototype.ping);t("gapi.iframes.Iframe.prototype.getOpenParams",J.prototype.ri);t("gapi.iframes.create",Ve);var ti=function(a){return Array.prototype.map.call(a,function(b){b=b.toString(16);return b.length>1?b:"0"+b}).join("")};var ui=null,wi=function(a){var b=[];vi(a,function(c){b.push(c)});return b},vi=function(a,b){function c(l){for(;d<a.length;){var m=a.charAt(d++),q=ui[m];if(q!=null)return q;if(!/^[\s\xa0]*$/.test(m))throw Error("Unknown base64 encoding at char: "+m);}return l}xi();for(var d=0;;){var e=c(-1),f=c(0),g=c(64),h=c(64);if(h===64&&e===-1)break;b(e<<2|f>>4);g!=64&&(b(f<<4&240|g>>2),h!=64&&b(g<<6&192|h))}},xi=function(){if(!ui){ui={};for(var a="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split(""),
b=["+/=","+/","-_=","-_.","-_"],c=0;c<5;c++)for(var d=a.concat(b[c].split("")),e=0;e<d.length;e++){var f=d[e],g=ui[f];g===void 0?ui[f]=e:y(g===e)}}};function yi(a,b){this.blockSize=-1;this.blockSize=64;this.Pe=r.Uint8Array?new Uint8Array(this.blockSize):Array(this.blockSize);this.bc=this.qd=0;this.na=[];this.ul=a;this.Gi=b;this.om=r.Int32Array?new Int32Array(64):Array(64);zi===void 0&&(zi=r.Int32Array?new Int32Array(Ai):Ai);this.reset()}w(yi,se);for(var Bi=[],Ci=0;Ci<63;Ci++)Bi[Ci]=0;var Di=[].concat(128,Bi);yi.prototype.reset=function(){this.bc=this.qd=0;this.na=r.Int32Array?new Int32Array(this.Gi):Db(this.Gi)};
var Ei=function(a){var b=a.Pe;y(b.length==a.blockSize);for(var c=a.om,d=0,e=0;e<b.length;)c[d++]=b[e]<<24|b[e+1]<<16|b[e+2]<<8|b[e+3],e=d*4;for(b=16;b<64;b++)d=c[b-15]|0,e=c[b-2]|0,c[b]=((c[b-16]|0)+((d>>>7|d<<25)^(d>>>18|d<<14)^d>>>3)|0)+((c[b-7]|0)+((e>>>17|e<<15)^(e>>>19|e<<13)^e>>>10)|0)|0;b=a.na[0]|0;d=a.na[1]|0;e=a.na[2]|0;for(var f=a.na[3]|0,g=a.na[4]|0,h=a.na[5]|0,l=a.na[6]|0,m=a.na[7]|0,q=0;q<64;q++){var x=((b>>>2|b<<30)^(b>>>13|b<<19)^(b>>>22|b<<10))+(b&d^b&e^d&e)|0,A=(m+((g>>>6|g<<26)^
(g>>>11|g<<21)^(g>>>25|g<<7))|0)+(((g&h^~g&l)+(zi[q]|0)|0)+(c[q]|0)|0)|0;m=l;l=h;h=g;g=f+A|0;f=e;e=d;d=b;b=A+x|0}a.na[0]=a.na[0]+b|0;a.na[1]=a.na[1]+d|0;a.na[2]=a.na[2]+e|0;a.na[3]=a.na[3]+f|0;a.na[4]=a.na[4]+g|0;a.na[5]=a.na[5]+h|0;a.na[6]=a.na[6]+l|0;a.na[7]=a.na[7]+m|0};
yi.prototype.update=function(a,b){b===void 0&&(b=a.length);var c=0,d=this.qd;if(typeof a==="string")for(;c<b;)this.Pe[d++]=a.charCodeAt(c++),d==this.blockSize&&(Ei(this),d=0);else if(wa(a))for(;c<b;){var e=a[c++];if(!("number"==typeof e&&0<=e&&255>=e&&e==(e|0)))throw Error("message must be a byte array");this.Pe[d++]=e;d==this.blockSize&&(Ei(this),d=0)}else throw Error("message must be string or array");this.qd=d;this.bc+=b};
yi.prototype.digest=function(){var a=[],b=this.bc*8;this.qd<56?this.update(Di,56-this.qd):this.update(Di,this.blockSize-(this.qd-56));for(var c=63;c>=56;c--)this.Pe[c]=b&255,b/=256;Ei(this);for(c=b=0;c<this.ul;c++)for(var d=24;d>=0;d-=8)a[b++]=this.na[c]>>d&255;return a};
var Ai=[1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,
4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298],zi;function Fi(){yi.call(this,8,Gi)}w(Fi,yi);var Gi=[1779033703,3144134277,1013904242,2773480762,1359893119,2600822924,528734635,1541459225];var Hi=function(){this.Cb=this.Cb;this.xf=this.xf};Hi.prototype.Cb=!1;Hi.prototype.isDisposed=function(){return this.Cb};Hi.prototype.dispose=function(){this.Cb||(this.Cb=!0,this.Ud())};Hi.prototype[Symbol.dispose]=function(){this.dispose()};Hi.prototype.Ud=function(){if(this.xf)for(;this.xf.length;)this.xf.shift()()};var Ii=function(a,b){this.type=a;this.currentTarget=this.target=b;this.defaultPrevented=this.qe=!1};Ii.prototype.stopPropagation=function(){this.qe=!0};Ii.prototype.preventDefault=function(){this.defaultPrevented=!0};var Ji=function(a,b){Ii.call(this,a?a.type:"");this.relatedTarget=this.currentTarget=this.target=null;this.button=this.screenY=this.screenX=this.clientY=this.clientX=this.offsetY=this.offsetX=0;this.key="";this.charCode=this.keyCode=0;this.metaKey=this.shiftKey=this.altKey=this.ctrlKey=!1;this.state=null;this.pointerId=0;this.pointerType="";this.timeStamp=0;this.Ya=null;a&&this.init(a,b)};w(Ji,Ii);
Ji.prototype.init=function(a,b){var c=this.type=a.type,d=a.changedTouches&&a.changedTouches.length?a.changedTouches[0]:null;this.target=a.target||a.srcElement;this.currentTarget=b;b=a.relatedTarget;b||(c=="mouseover"?b=a.fromElement:c=="mouseout"&&(b=a.toElement));this.relatedTarget=b;d?(this.clientX=d.clientX!==void 0?d.clientX:d.pageX,this.clientY=d.clientY!==void 0?d.clientY:d.pageY,this.screenX=d.screenX||0,this.screenY=d.screenY||0):(this.offsetX=Ec||a.offsetX!==void 0?a.offsetX:a.layerX,this.offsetY=
Ec||a.offsetY!==void 0?a.offsetY:a.layerY,this.clientX=a.clientX!==void 0?a.clientX:a.pageX,this.clientY=a.clientY!==void 0?a.clientY:a.pageY,this.screenX=a.screenX||0,this.screenY=a.screenY||0);this.button=a.button;this.keyCode=a.keyCode||0;this.key=a.key||"";this.charCode=a.charCode||(c=="keypress"?a.keyCode:0);this.ctrlKey=a.ctrlKey;this.altKey=a.altKey;this.shiftKey=a.shiftKey;this.metaKey=a.metaKey;this.pointerId=a.pointerId||0;this.pointerType=a.pointerType;this.state=a.state;this.timeStamp=
a.timeStamp;this.Ya=a;a.defaultPrevented&&Ji.Id.preventDefault.call(this)};Ji.prototype.stopPropagation=function(){Ji.Id.stopPropagation.call(this);this.Ya.stopPropagation?this.Ya.stopPropagation():this.Ya.cancelBubble=!0};Ji.prototype.preventDefault=function(){Ji.Id.preventDefault.call(this);var a=this.Ya;a.preventDefault?a.preventDefault():a.returnValue=!1};Ji.prototype.Fk=function(){return this.Ya};var Ki="closure_listenable_"+(Math.random()*1E6|0);var Li=0;var Mi=function(a,b,c,d,e){this.listener=a;this.proxy=null;this.src=b;this.type=c;this.capture=!!d;this.Ze=e;this.key=++Li;this.ve=this.Le=!1},Ni=function(a){a.ve=!0;a.listener=null;a.proxy=null;a.src=null;a.Ze=null};function Oi(a){this.src=a;this.Qa={};this.Ee=0}Oi.prototype.add=function(a,b,c,d,e){var f=a.toString();a=this.Qa[f];a||(a=this.Qa[f]=[],this.Ee++);var g=Pi(a,b,d,e);g>-1?(b=a[g],c||(b.Le=!1)):(b=new Mi(b,this.src,f,!!d,e),b.Le=c,a.push(b));return b};Oi.prototype.remove=function(a,b,c,d){a=a.toString();if(!(a in this.Qa))return!1;var e=this.Qa[a];b=Pi(e,b,c,d);return b>-1?(Ni(e[b]),Bb(e,b),e.length==0&&(delete this.Qa[a],this.Ee--),!0):!1};
var Qi=function(a,b){var c=b.type;c in a.Qa&&Ab(a.Qa[c],b)&&(Ni(b),a.Qa[c].length==0&&(delete a.Qa[c],a.Ee--))};Oi.prototype.removeAll=function(a){a=a&&a.toString();var b=0,c;for(c in this.Qa)if(!a||c==a){for(var d=this.Qa[c],e=0;e<d.length;e++)++b,Ni(d[e]);delete this.Qa[c];this.Ee--}return b};Oi.prototype.Bg=function(a,b,c,d){a=this.Qa[a.toString()];var e=-1;a&&(e=Pi(a,b,c,d));return e>-1?a[e]:null};
Oi.prototype.hasListener=function(a,b){var c=a!==void 0,d=c?a.toString():"",e=b!==void 0;return Gb(this.Qa,function(f){for(var g=0;g<f.length;++g)if(!(c&&f[g].type!=d||e&&f[g].capture!=b))return!0;return!1})};var Pi=function(a,b,c,d){for(var e=0;e<a.length;++e){var f=a[e];if(!f.ve&&f.listener==b&&f.capture==!!c&&f.Ze==d)return e}return-1};var Ri="closure_lm_"+(Math.random()*1E6|0),Si={},Ti=0,Vi=function(a,b,c,d,e){if(d&&d.once)Ui(a,b,c,d,e);else if(Array.isArray(b))for(var f=0;f<b.length;f++)Vi(a,b[f],c,d,e);else c=Wi(c),a&&a[Ki]?a.listen(b,c,u(d)?!!d.capture:!!d,e):Xi(a,b,c,!1,d,e)},Xi=function(a,b,c,d,e,f){if(!b)throw Error("Invalid event type");var g=u(e)?!!e.capture:!!e,h=Yi(a);h||(a[Ri]=h=new Oi(a));c=h.add(b,c,d,g,f);if(!c.proxy){d=Zi();c.proxy=d;d.src=a;d.listener=c;if(a.addEventListener)re||(e=g),e===void 0&&(e=!1),a.addEventListener(b.toString(),
d,e);else if(a.attachEvent)a.attachEvent($i(b.toString()),d);else if(a.addListener&&a.removeListener)y(b==="change","MediaQueryList only has a change event"),a.addListener(d);else throw Error("addEventListener and attachEvent are unavailable.");Ti++}},Zi=function(){var a=aj,b=function(c){return a.call(b.src,b.listener,c)};return b},Ui=function(a,b,c,d,e){if(Array.isArray(b))for(var f=0;f<b.length;f++)Ui(a,b[f],c,d,e);else c=Wi(c),a&&a[Ki]?bj(a,b,c,u(d)?!!d.capture:!!d,e):Xi(a,b,c,!0,d,e)},cj=function(a,
b,c,d,e){if(Array.isArray(b))for(var f=0;f<b.length;f++)cj(a,b[f],c,d,e);else d=u(d)?!!d.capture:!!d,c=Wi(c),a&&a[Ki]?a.Eb.remove(String(b),c,d,e):a&&(a=Yi(a))&&(b=a.Bg(b,c,d,e))&&dj(b)},dj=function(a){if(typeof a!=="number"&&a&&!a.ve){var b=a.src;if(b&&b[Ki])Qi(b.Eb,a);else{var c=a.type,d=a.proxy;b.removeEventListener?b.removeEventListener(c,d,a.capture):b.detachEvent?b.detachEvent($i(c),d):b.addListener&&b.removeListener&&b.removeListener(d);Ti--;(c=Yi(b))?(Qi(c,a),c.Ee==0&&(c.src=null,b[Ri]=null)):
Ni(a)}}},$i=function(a){return a in Si?Si[a]:Si[a]="on"+a},aj=function(a,b){if(a.ve)a=!0;else{b=new Ji(b,this);var c=a.listener,d=a.Ze||a.src;a.Le&&dj(a);a=c.call(d,b)}return a},Yi=function(a){a=a[Ri];return a instanceof Oi?a:null},ej="__closure_events_fn_"+(Math.random()*1E9>>>0),Wi=function(a){y(a,"Listener can not be null.");if(typeof a==="function")return a;y(a.handleEvent,"An object listener must have handleEvent method.");a[ej]||(a[ej]=function(b){return a.handleEvent(b)});return a[ej]};var fj=function(){Hi.call(this);this.Eb=new Oi(this);this.Zj=this;this.Wg=null};w(fj,Hi);fj.prototype[Ki]=!0;k=fj.prototype;k.addEventListener=function(a,b,c,d){Vi(this,a,b,c,d)};k.removeEventListener=function(a,b,c,d){cj(this,a,b,c,d)};
k.dispatchEvent=function(a){gj(this);var b=this.Wg;if(b){var c=[];for(var d=1;b;b=b.Wg)c.push(b),y(++d<1E3,"infinite loop")}b=this.Zj;d=a.type||a;if(typeof a==="string")a=new Ii(a,b);else if(a instanceof Ii)a.target=a.target||b;else{var e=a;a=new Ii(d,b);Kb(a,e)}e=!0;var f;if(c)for(f=c.length-1;!a.qe&&f>=0;f--){var g=a.currentTarget=c[f];e=hj(g,d,!0,a)&&e}a.qe||(g=a.currentTarget=b,e=hj(g,d,!0,a)&&e,a.qe||(e=hj(g,d,!1,a)&&e));if(c)for(f=0;!a.qe&&f<c.length;f++)g=a.currentTarget=c[f],e=hj(g,d,!1,a)&&
e;return e};k.Ud=function(){fj.Id.Ud.call(this);this.Eb&&this.Eb.removeAll(void 0);this.Wg=null};k.listen=function(a,b,c,d){gj(this);return this.Eb.add(String(a),b,!1,c,d)};var bj=function(a,b,c,d,e){a.Eb.add(String(b),c,!0,d,e)},hj=function(a,b,c,d){b=a.Eb.Qa[String(b)];if(!b)return!0;b=b.concat();for(var e=!0,f=0;f<b.length;++f){var g=b[f];if(g&&!g.ve&&g.capture==c){var h=g.listener,l=g.Ze||g.src;g.Le&&Qi(a.Eb,g);e=h.call(l,d)!==!1&&e}}return e&&!d.defaultPrevented};
fj.prototype.Bg=function(a,b,c,d){return this.Eb.Bg(String(a),b,c,d)};fj.prototype.hasListener=function(a,b){return this.Eb.hasListener(a!==void 0?String(a):void 0,b)};var gj=function(a){y(a.Eb,"Event target is not initialized. Did you call the superclass (goog.events.EventTarget) constructor?")};function ij(){};var jj,kj=function(){};w(kj,ij);kj.prototype.Rd=function(){return new XMLHttpRequest};jj=new kj;function lj(){}w(lj,ij);lj.prototype.Rd=function(){var a=new XMLHttpRequest;if("withCredentials"in a)return a;if(typeof XDomainRequest!="undefined")return new mj;throw Error("Unsupported browser");};
var mj=function(){this.Qb=new XDomainRequest;this.readyState=0;this.onreadystatechange=null;this.responseType=this.responseText=this.response="";this.status=-1;this.responseXML=null;this.statusText="";this.Qb.onload=v(this.Mk,this);this.Qb.onerror=v(this.xi,this);this.Qb.onprogress=v(this.Ok,this);this.Qb.ontimeout=v(this.Sk,this)};k=mj.prototype;k.open=function(a,b,c){if(c!=null&&!c)throw Error("Only async requests are supported.");this.Qb.open(a,b)};
k.send=function(a){if(a)if(typeof a=="string")this.Qb.send(a);else throw Error("Only string data is supported");else this.Qb.send()};k.abort=function(){this.Qb.abort()};k.setRequestHeader=function(){};k.getResponseHeader=function(a){return a.toLowerCase()=="content-type"?this.Qb.contentType:""};k.Mk=function(){this.status=200;this.response=this.responseText=this.Qb.responseText;nj(this,4)};k.xi=function(){this.status=500;this.response=this.responseText="";nj(this,4)};k.Sk=function(){this.xi()};
k.Ok=function(){this.status=200;nj(this,1)};var nj=function(a,b){a.readyState=b;if(a.onreadystatechange)a.onreadystatechange()};mj.prototype.getAllResponseHeaders=function(){return"content-type: "+this.Qb.contentType};var oj=function(a){this.He=a.tm||null;this.ze=a.Km||!1;this.Yc=this.Fc=void 0};w(oj,ij);oj.prototype.Rd=function(){var a=new pj(this.He,this.ze);this.Fc&&a.uh(this.Fc);this.Yc&&a.Bj(this.Yc);return a};oj.prototype.uh=function(a){this.Fc=a};oj.prototype.Bj=function(a){this.Yc=a};
var pj=function(a,b){fj.call(this);this.He=a;this.ze=b;this.Yc=this.Fc=void 0;this.status=this.readyState=0;this.responseType=this.responseText=this.response=this.statusText="";this.onreadystatechange=this.responseXML=null;this.nh=new Headers;this.Fd=null;this.Si="GET";this.hb="";this.Sb=!1;this.Gh=this.Sd=this.Ue=null};w(pj,fj);
pj.prototype.open=function(a,b,c){y(!!c,"Only async requests are supported.");if(this.readyState!=0)throw this.abort(),Error("Error reopening a connection");this.Si=a;this.hb=b;this.readyState=1;qj(this)};pj.prototype.send=function(a){if(this.readyState!=1)throw this.abort(),Error("need to call open() first. ");this.Sb=!0;var b={headers:this.nh,method:this.Si,credentials:this.Fc,cache:this.Yc};a&&(b.body=a);(this.He||r).fetch(new Request(this.hb,b)).then(this.Rk.bind(this),this.Ye.bind(this))};
pj.prototype.abort=function(){var a=this;this.response=this.responseText="";this.nh=new Headers;this.status=0;this.Sd&&this.Sd.cancel("Request was aborted.").catch(function(){var b=a.Ea;b&&jc(b,Xb,"Fetch reader cancellation error.")});this.readyState>=1&&this.Sb&&this.readyState!=4&&(this.Sb=!1,rj(this));this.readyState=0};
pj.prototype.Rk=function(a){if(this.Sb&&(this.Ue=a,this.Fd||(this.status=this.Ue.status,this.statusText=this.Ue.statusText,this.Fd=a.headers,this.readyState=2,qj(this)),this.Sb&&(this.readyState=3,qj(this),this.Sb)))if(this.responseType==="arraybuffer")a.arrayBuffer().then(this.Pk.bind(this),this.Ye.bind(this));else if(typeof r.ReadableStream!=="undefined"&&"body"in a){this.Sd=a.body.getReader();if(this.ze){if(this.responseType)throw Error('responseType must be empty for "streamBinaryChunks" mode responses.');
this.response=[]}else this.response=this.responseText="",this.Gh=new TextDecoder;sj(this)}else a.text().then(this.Qk.bind(this),this.Ye.bind(this))};var sj=function(a){a.Sd.read().then(a.Kk.bind(a)).catch(a.Ye.bind(a))};pj.prototype.Kk=function(a){if(this.Sb){if(this.ze&&a.value)this.response.push(a.value);else if(!this.ze){var b=a.value?a.value:new Uint8Array(0);if(b=this.Gh.decode(b,{stream:!a.done}))this.response=this.responseText+=b}a.done?rj(this):qj(this);this.readyState==3&&sj(this)}};
pj.prototype.Qk=function(a){this.Sb&&(this.response=this.responseText=a,rj(this))};pj.prototype.Pk=function(a){this.Sb&&(this.response=a,rj(this))};pj.prototype.Ye=function(){var a=this.Ea;a&&jc(a,Xb,"Failed to fetch url "+this.hb);this.Sb&&rj(this)};var rj=function(a){a.readyState=4;a.Ue=null;a.Sd=null;a.Gh=null;qj(a)};k=pj.prototype;k.setRequestHeader=function(a,b){this.nh.append(a,b)};
k.getResponseHeader=function(a){return this.Fd?this.Fd.get(a.toLowerCase())||"":((a=this.Ea)&&jc(a,Xb,"Attempting to get response header but no headers have been received for url: "+this.hb),"")};k.getAllResponseHeaders=function(){if(!this.Fd){var a=this.Ea;a&&jc(a,Xb,"Attempting to get all response headers but no headers have been received for url: "+this.hb);return""}a=[];for(var b=this.Fd.entries(),c=b.next();!c.done;)c=c.value,a.push(c[0]+": "+c[1]),c=b.next();return a.join("\r\n")};
k.uh=function(a){this.Fc=a};k.Bj=function(a){this.Yc=a};var qj=function(a){a.onreadystatechange&&a.onreadystatechange.call(a)};Object.defineProperty(pj.prototype,"withCredentials",{get:function(){return this.Fc==="include"},set:function(a){this.uh(a?"include":"same-origin")}});/*

 Copyright 2005, 2007 Bob Ippolito. All Rights Reserved.
 Copyright The Closure Library Authors.
 SPDX-License-Identifier: MIT
*/
var uj=function(a){var b=tj;this.Mf=[];this.Vi=b;this.ci=a||null;this.ae=this.md=!1;this.Ua=void 0;this.Ch=this.Ph=this.eg=!1;this.Sf=0;this.Ha=null;this.fg=0};uj.prototype.cancel=function(a){if(this.md)this.Ua instanceof uj&&this.Ua.cancel();else{if(this.Ha){var b=this.Ha;delete this.Ha;a?b.cancel(a):(b.fg--,b.fg<=0&&b.cancel())}this.Vi?this.Vi.call(this.ci,this):this.Ch=!0;this.md||vj(this,new wj(this))}};uj.prototype.Wh=function(a,b){this.eg=!1;xj(this,a,b)};
var xj=function(a,b,c){a.md=!0;a.Ua=c;a.ae=!b;yj(a)},Aj=function(a){if(a.md){if(!a.Ch)throw new zj(a);a.Ch=!1}};uj.prototype.callback=function(a){Aj(this);Bj(a);xj(this,!0,a)};var vj=function(a,b){Aj(a);Bj(b);xj(a,!1,b)},Bj=function(a){y(!(a instanceof uj),"An execution sequence may not be initiated with a blocking Deferred.")};uj.prototype.addCallback=function(a,b){return Cj(this,a,null,b)};var Dj=function(a,b){Cj(a,null,b)};
uj.prototype.finally=function(a){var b=this;return new Promise(function(c,d){Cj(b,function(e){a();c(e)},function(e){a();d(e)})})};var Cj=function(a,b,c,d){y(!a.Ph,"Blocking Deferreds can not be re-used");var e=a.md;e||(b===c?b=c=Bf(b):(b=Bf(b),c=Bf(c)));a.Mf.push([b,c,d]);e&&yj(a);return a};uj.prototype.then=function(a,b,c){var d,e,f=new F(function(g,h){e=g;d=h});Cj(this,e,function(g){g instanceof wj?f.cancel():d(g);return Ej},this);return f.then(a,b,c)};uj.prototype.$goog_Thenable=!0;
var Fj=function(a){return yb(a.Mf,function(b){return typeof b[1]==="function"})},Ej={},yj=function(a){if(a.Sf&&a.md&&Fj(a)){var b=a.Sf,c=Gj[b];c&&(r.clearTimeout(c.Pa),delete Gj[b]);a.Sf=0}a.Ha&&(a.Ha.fg--,delete a.Ha);b=a.Ua;for(var d=c=!1;a.Mf.length&&!a.eg;){var e=a.Mf.shift(),f=e[0],g=e[1];e=e[2];if(f=a.ae?g:f)try{var h=f.call(e||a.ci,b);h===Ej&&(h=void 0);h!==void 0&&(a.ae=a.ae&&(h==b||h instanceof Error),a.Ua=b=h);if(Tf(b)||typeof r.Promise==="function"&&b instanceof r.Promise)d=!0,a.eg=!0}catch(l){b=
l,a.ae=!0,Fj(a)||(c=!0)}}a.Ua=b;d&&(h=v(a.Wh,a,!0),d=v(a.Wh,a,!1),b instanceof uj?(Cj(b,h,d),b.Ph=!0):b.then(h,d));c&&(b=new Hj(b),Gj[b.Pa]=b,a.Sf=b.Pa)},zj=function(){mb.call(this)};w(zj,mb);zj.prototype.message="Deferred has already fired";zj.prototype.name="AlreadyCalledError";var wj=function(){mb.call(this)};w(wj,mb);wj.prototype.message="Deferred was canceled";wj.prototype.name="CanceledError";var Hj=function(a){this.Pa=r.setTimeout(v(this.im,this),0);this.yb=a};
Hj.prototype.im=function(){y(Gj[this.Pa],"Cannot throw an error that is not scheduled.");delete Gj[this.Pa];throw this.yb;};var Gj={};var Lj=function(a){var b={},c=b.document||document,d=Za(a).toString(),e=(new Xc(c)).createElement("SCRIPT"),f={wj:e,Qf:void 0},g=new uj(f),h=null,l=b.timeout!=null?b.timeout:5E3;l>0&&(h=window.setTimeout(function(){Ij(e,!0);vj(g,new Jj(1,"Timeout reached for loading script "+d))},l),f.Qf=h);e.onload=e.onreadystatechange=function(){e.readyState&&e.readyState!="loaded"&&e.readyState!="complete"||(Ij(e,b.Gm||!1,h),g.callback(null))};e.onerror=function(){Ij(e,!0,h);vj(g,new Jj(0,"Error while loading script "+
d))};f=b.attributes||{};Kb(f,{type:"text/javascript",charset:"UTF-8"});Rc(e,f);Tb(e,a);Kj(c).appendChild(e);return g},Kj=function(a){var b;return(b=(a||document).getElementsByTagName("HEAD"))&&b.length!==0?b[0]:a.documentElement},tj=function(){if(this&&this.wj){var a=this.wj;a&&a.tagName=="SCRIPT"&&Ij(a,!0,this.Qf)}},Ij=function(a,b,c){c!=null&&r.clearTimeout(c);a.onload=function(){};a.onerror=function(){};a.onreadystatechange=function(){};b&&window.setTimeout(function(){Vc(a)},0)},Jj=function(a,
b){var c="Jsloader error (code #"+a+")";b&&(c+=": "+b);mb.call(this,c);this.code=a};w(Jj,mb);var Mj=RegExp("^(?:([^:/?#.]+):)?(?://(?:([^\\\\/?#]*)@)?([^\\\\/?#]*?)(?::([0-9]+))?(?=[\\\\/?#]|$))?([^?#]+)?(?:\\?([^#]*))?(?:#([\\s\\S]*))?$"),Nj=function(a,b){if(a){a=a.split("&");for(var c=0;c<a.length;c++){var d=a[c].indexOf("="),e=null;if(d>=0){var f=a[c].substring(0,d);e=a[c].substring(d+1)}else f=a[c];b(f,e?decodeURIComponent(e.replace(/\+/g," ")):"")}}};var Oj=function(a){fj.call(this);this.headers=new Map;this.Wj=a||null;this.kc=!1;this.v=null;this.fe=this.Ni=this.qf="";this.Lc=this.Hg=this.kf=this.vg=!1;this.Ce=0;this.Uc=null;this.Hf="";this.Hl=this.Vj=!1;this.ag=this.Hh=null};w(Oj,fj);var Pj=/^https?$/i,Qj=["POST","PUT"];k=Oj.prototype;k.setTrustToken=function(a){this.Hh=a};k.setAttributionReporting=function(a){this.ag=a};
k.send=function(a,b,c,d){if(this.v)throw Error("[goog.net.XhrIo] Object is active with another request="+this.qf+"; newUri="+a);b=b?b.toUpperCase():"GET";this.qf=a;this.fe="";this.Ni=b;this.vg=!1;this.kc=!0;this.v=this.Wj?this.Wj.Rd():jj.Rd();this.v.onreadystatechange=Bf(v(this.ej,this));this.Hl&&"onprogress"in this.v&&(this.v.onprogress=Bf(v(function(g){this.dj(g,!0)},this)),this.v.upload&&(this.v.upload.onprogress=Bf(v(this.dj,this))));try{kc(this.Ea,Rj(this,"Opening Xhr")),this.Hg=!0,this.v.open(b,
String(a),!0),this.Hg=!1}catch(g){kc(this.Ea,Rj(this,"Error opening Xhr: "+g.message));this.yb(5,g);return}a=c||"";c=new Map(this.headers);if(d)if(Object.getPrototypeOf(d)===Object.prototype)for(var e in d)c.set(e,d[e]);else if(typeof d.keys==="function"&&typeof d.get==="function"){e=ma(d.keys());for(var f=e.next();!f.done;f=e.next())f=f.value,c.set(f,d.get(f))}else throw Error("Unknown input type for opt_headers: "+String(d));d=Array.from(c.keys()).find(function(g){return"content-type"==g.toLowerCase()});
e=r.FormData&&a instanceof r.FormData;!zb(Qj,b)||d||e||c.set("Content-Type","application/x-www-form-urlencoded;charset=utf-8");b=ma(c);for(d=b.next();!d.done;d=b.next())c=ma(d.value),d=c.next().value,c=c.next().value,this.v.setRequestHeader(d,c);this.Hf&&(this.v.responseType=this.Hf);"withCredentials"in this.v&&this.v.withCredentials!==this.Vj&&(this.v.withCredentials=this.Vj);if("setTrustToken"in this.v&&this.Hh)try{this.v.setTrustToken(this.Hh)}catch(g){kc(this.Ea,Rj(this,"Error SetTrustToken: "+
g.message))}if("setAttributionReporting"in this.v&&this.ag)try{this.v.setAttributionReporting(this.ag)}catch(g){kc(this.Ea,Rj(this,"Error SetAttributionReporting: "+g.message))}try{this.Uc&&(clearTimeout(this.Uc),this.Uc=null),this.Ce>0&&(kc(this.Ea,Rj(this,"Will abort after "+this.Ce+"ms if incomplete")),this.Uc=setTimeout(this.Qf.bind(this),this.Ce)),kc(this.Ea,Rj(this,"Sending request")),this.kf=!0,this.v.send(a),this.kf=!1}catch(g){kc(this.Ea,Rj(this,"Send error: "+g.message)),this.yb(5,g)}};
k.Qf=function(){typeof ua!="undefined"&&this.v&&(this.fe="Timed out after "+this.Ce+"ms, aborting",kc(this.Ea,Rj(this,this.fe)),this.dispatchEvent("timeout"),this.abort(8))};k.yb=function(a,b){this.kc=!1;this.v&&(this.Lc=!0,this.v.abort(),this.Lc=!1);this.fe=b;Sj(this);Tj(this)};var Sj=function(a){a.vg||(a.vg=!0,a.dispatchEvent("complete"),a.dispatchEvent("error"))};
Oj.prototype.abort=function(){this.v&&this.kc&&(kc(this.Ea,Rj(this,"Aborting")),this.kc=!1,this.Lc=!0,this.v.abort(),this.Lc=!1,this.dispatchEvent("complete"),this.dispatchEvent("abort"),Tj(this))};Oj.prototype.Ud=function(){this.v&&(this.kc&&(this.kc=!1,this.Lc=!0,this.v.abort(),this.Lc=!1),Tj(this,!0));Oj.Id.Ud.call(this)};Oj.prototype.ej=function(){this.isDisposed()||(this.Hg||this.kf||this.Lc?Uj(this):this.Al())};Oj.prototype.Al=function(){Uj(this)};
var Uj=function(a){if(a.kc&&typeof ua!="undefined")if(a.kf&&Vj(a)==4)setTimeout(a.ej.bind(a),0);else if(a.dispatchEvent("readystatechange"),Vj(a)==4){kc(a.Ea,Rj(a,"Request complete"));a.kc=!1;try{var b=a.getStatus();a:switch(b){case 200:case 201:case 202:case 204:case 206:case 304:case 1223:var c=!0;break a;default:c=!1}var d;if(!(d=c)){var e;if(e=b===0){var f=String(a.qf).match(Mj)[1]||null;!f&&r.self&&r.self.location&&(f=r.self.location.protocol.slice(0,-1));e=!Pj.test(f?f.toLowerCase():"")}d=e}if(d)a.dispatchEvent("complete"),
a.dispatchEvent("success");else{try{var g=Vj(a)>2?a.v.statusText:""}catch(h){kc(a.Ea,"Can not get status: "+h.message),g=""}a.fe=g+" ["+a.getStatus()+"]";Sj(a)}}finally{Tj(a)}}};Oj.prototype.dj=function(a,b){y(a.type==="progress","goog.net.EventType.PROGRESS is of the same type as raw XHR progress.");this.dispatchEvent(Wj(a,"progress"));this.dispatchEvent(Wj(a,b?"downloadprogress":"uploadprogress"))};
var Wj=function(a,b){return{type:b,lengthComputable:a.lengthComputable,loaded:a.loaded,total:a.total}},Tj=function(a,b){if(a.v){a.Uc&&(clearTimeout(a.Uc),a.Uc=null);var c=a.v;a.v=null;b||a.dispatchEvent("ready");try{c.onreadystatechange=null}catch(d){(a=a.Ea)&&jc(a,Wb,"Problem encountered resetting onreadystatechange: "+d.message)}}};Oj.prototype.isActive=function(){return!!this.v};var Vj=function(a){return a.v?a.v.readyState:0};
Oj.prototype.getStatus=function(){try{return Vj(this)>2?this.v.status:-1}catch(a){return-1}};
Oj.prototype.getResponse=function(){try{if(!this.v)return null;if("response"in this.v)return this.v.response;switch(this.Hf){case "":case "text":return this.v.responseText;case "arraybuffer":if("mozResponseArrayBuffer"in this.v)return this.v.mozResponseArrayBuffer}var a=this.Ea;a&&jc(a,Wb,"Response type "+this.Hf+" is not supported on this browser");return null}catch(b){return kc(this.Ea,"Can not get response: "+b.message),null}};
Oj.prototype.getResponseHeader=function(a){if(this.v&&Vj(this)==4)return a=this.v.getResponseHeader(a),a===null?void 0:a};Oj.prototype.getAllResponseHeaders=function(){return this.v&&Vj(this)>=2?this.v.getAllResponseHeaders()||"":""};var Rj=function(a,b){return b+" ["+a.Ni+" "+a.qf+" "+a.getStatus()+"]"};var Xj=function(a,b){if(typeof a!=="function")if(a&&typeof a.handleEvent=="function")a=v(a.handleEvent,a);else throw Error("Invalid listener argument");return Number(b)>2147483647?-1:r.setTimeout(a,b||0)},Yj=function(a){var b=null;return(new F(function(c,d){b=Xj(function(){c(void 0)},a);b==-1&&d(Error("Failed to schedule timer."))})).l(function(c){r.clearTimeout(b);throw c;})};var Zj=function(a){if(a.Kc&&typeof a.Kc=="function")return a.Kc();if(typeof Map!=="undefined"&&a instanceof Map||typeof Set!=="undefined"&&a instanceof Set)return Array.from(a.values());if(typeof a==="string")return a.split("");if(wa(a)){for(var b=[],c=a.length,d=0;d<c;d++)b.push(a[d]);return b}b=[];c=0;for(d in a)b[c++]=a[d];return b},ak=function(a){if(a.Ag&&typeof a.Ag=="function")return a.Ag();if(!a.Kc||typeof a.Kc!="function"){if(typeof Map!=="undefined"&&a instanceof Map)return Array.from(a.keys());
if(!(typeof Set!=="undefined"&&a instanceof Set)){if(wa(a)||typeof a==="string"){var b=[];a=a.length;for(var c=0;c<a;c++)b.push(c);return b}b=[];c=0;for(var d in a)b[c++]=d;return b}}},bk=function(a,b,c){if(a.forEach&&typeof a.forEach=="function")a.forEach(b,c);else if(wa(a)||typeof a==="string")Array.prototype.forEach.call(a,b,c);else for(var d=ak(a),e=Zj(a),f=e.length,g=0;g<f;g++)b.call(c,e[g],d&&d[g],a)};var ck=function(a){this.Ga=this.Vc=this.Va="";this.Tb=null;this.Ic=this.Af="";this.Ab=this.jl=!1;if(a instanceof ck){this.Ab=a.Ab;dk(this,a.Va);var b=a.Vc;ek(this);this.Vc=b;fk(this,a.Ga);gk(this,a.Tb);this.setPath(a.getPath());hk(this,a.rb.clone());a=a.Ic;ek(this);this.Ic=a}else a&&(b=String(a).match(Mj))?(this.Ab=!1,dk(this,b[1]||"",!0),a=b[2]||"",ek(this),this.Vc=ik(a),fk(this,b[3]||"",!0),gk(this,b[4]),this.setPath(b[5]||"",!0),hk(this,b[6]||"",!0),a=b[7]||"",ek(this),this.Ic=ik(a)):(this.Ab=
!1,this.rb=new jk(null,this.Ab))};ck.prototype.toString=function(){var a=[],b=this.Va;b&&a.push(kk(b,lk,!0),":");var c=this.Ga;if(c||b=="file")a.push("//"),(b=this.Vc)&&a.push(kk(b,lk,!0),"@"),a.push(encodeURIComponent(String(c)).replace(/%25([0-9a-fA-F]{2})/g,"%$1")),c=this.Tb,c!=null&&a.push(":",String(c));if(c=this.getPath())this.Ga&&c.charAt(0)!="/"&&a.push("/"),a.push(kk(c,c.charAt(0)=="/"?mk:nk,!0));(c=this.rb.toString())&&a.push("?",c);(c=this.Ic)&&a.push("#",kk(c,ok));return a.join("")};
ck.prototype.resolve=function(a){var b=this.clone(),c=!!a.Va;c?dk(b,a.Va):c=!!a.Vc;if(c){var d=a.Vc;ek(b);b.Vc=d}else c=!!a.Ga;c?fk(b,a.Ga):c=a.Tb!=null;d=a.getPath();if(c)gk(b,a.Tb);else if(c=!!a.Af){if(d.charAt(0)!="/")if(this.Ga&&!this.Af)d="/"+d;else{var e=b.getPath().lastIndexOf("/");e!=-1&&(d=b.getPath().slice(0,e+1)+d)}e=d;if(e==".."||e==".")d="";else if(B(e,"./")||B(e,"/.")){d=e.lastIndexOf("/",0)==0;e=e.split("/");for(var f=[],g=0;g<e.length;){var h=e[g++];h=="."?d&&g==e.length&&f.push(""):
h==".."?((f.length>1||f.length==1&&f[0]!="")&&f.pop(),d&&g==e.length&&f.push("")):(f.push(h),d=!0)}d=f.join("/")}else d=e}c?b.setPath(d):c=a.rb.toString()!=="";c?hk(b,a.rb.clone()):c=!!a.Ic;c&&(a=a.Ic,ek(b),b.Ic=a);return b};ck.prototype.clone=function(){return new ck(this)};
var dk=function(a,b,c){ek(a);a.Va=c?ik(b,!0):b;a.Va&&(a.Va=a.Va.replace(/:$/,""))},fk=function(a,b,c){ek(a);a.Ga=c?ik(b,!0):b},gk=function(a,b){ek(a);if(b){b=Number(b);if(isNaN(b)||b<0)throw Error("Bad port number "+b);a.Tb=b}else a.Tb=null};ck.prototype.getPath=function(){return this.Af};ck.prototype.setPath=function(a,b){ek(this);this.Af=b?ik(a,!0):a;return this};var hk=function(a,b,c){ek(a);b instanceof jk?(a.rb=b,a.rb.xh(a.Ab)):(c||(b=kk(b,pk)),a.rb=new jk(b,a.Ab))};ck.prototype.getQuery=function(){return this.rb.toString()};
var L=function(a,b,c){ek(a);a.rb.set(b,c)},qk=function(a,b){return a.rb.get(b)};ck.prototype.removeParameter=function(a){ek(this);this.rb.remove(a);return this};var ek=function(a){if(a.jl)throw Error("Tried to modify a read-only Uri");};ck.prototype.xh=function(a){this.Ab=a;this.rb&&this.rb.xh(a)};
var M=function(a){return a instanceof ck?a.clone():new ck(a)},rk=function(a,b,c,d){var e=new ck(null);a&&dk(e,a);b&&fk(e,b);c&&gk(e,c);d&&e.setPath(d);return e},ik=function(a,b){return a?b?decodeURI(a.replace(/%25/g,"%2525")):decodeURIComponent(a):""},kk=function(a,b,c){return typeof a==="string"?(a=encodeURI(a).replace(b,sk),c&&(a=a.replace(/%25([0-9a-fA-F]{2})/g,"%$1")),a):null},sk=function(a){a=a.charCodeAt(0);return"%"+(a>>4&15).toString(16)+(a&15).toString(16)},lk=/[#\/\?@]/g,nk=/[#\?:]/g,mk=
/[#\?]/g,pk=/[#\?@]/g,ok=/#/g,jk=function(a,b){this.Na=this.qa=null;this.mb=a||null;this.Ab=!!b},tk=function(a){a.qa||(a.qa=new Map,a.Na=0,a.mb&&Nj(a.mb,function(b,c){a.add(decodeURIComponent(b.replace(/\+/g," ")),c)}))},uk=function(a){var b=ak(a);if(typeof b=="undefined")throw Error("Keys are undefined");var c=new jk(null);a=Zj(a);for(var d=0;d<b.length;d++){var e=b[d],f=a[d];Array.isArray(f)?c.setValues(e,f):c.add(e,f)}return c};
jk.prototype.add=function(a,b){tk(this);this.mb=null;a=this.nb(a);var c=this.qa.get(a);c||this.qa.set(a,c=[]);c.push(b);this.Na=sb(this.Na)+1;return this};jk.prototype.remove=function(a){tk(this);a=this.nb(a);return this.qa.has(a)?(this.mb=null,this.Na=sb(this.Na)-this.qa.get(a).length,this.qa.delete(a)):!1};jk.prototype.clear=function(){this.qa=this.mb=null;this.Na=0};jk.prototype.isEmpty=function(){tk(this);return this.Na==0};var vk=function(a,b){tk(a);b=a.nb(b);return a.qa.has(b)};k=jk.prototype;
k.forEach=function(a,b){tk(this);this.qa.forEach(function(c,d){c.forEach(function(e){a.call(b,e,d,this)},this)},this)};k.Ag=function(){tk(this);for(var a=Array.from(this.qa.values()),b=Array.from(this.qa.keys()),c=[],d=0;d<b.length;d++)for(var e=a[d],f=0;f<e.length;f++)c.push(b[d]);return c};k.Kc=function(a){tk(this);var b=[];if(typeof a==="string")vk(this,a)&&(b=b.concat(this.qa.get(this.nb(a))));else{a=Array.from(this.qa.values());for(var c=0;c<a.length;c++)b=b.concat(a[c])}return b};
k.set=function(a,b){tk(this);this.mb=null;a=this.nb(a);vk(this,a)&&(this.Na=sb(this.Na)-this.qa.get(a).length);this.qa.set(a,[b]);this.Na=sb(this.Na)+1;return this};k.get=function(a,b){if(!a)return b;a=this.Kc(a);return a.length>0?String(a[0]):b};k.setValues=function(a,b){this.remove(a);b.length>0&&(this.mb=null,this.qa.set(this.nb(a),Db(b)),this.Na=sb(this.Na)+b.length)};
k.toString=function(){if(this.mb)return this.mb;if(!this.qa)return"";for(var a=[],b=Array.from(this.qa.keys()),c=0;c<b.length;c++){var d=b[c],e=encodeURIComponent(String(d));d=this.Kc(d);for(var f=0;f<d.length;f++){var g=e;d[f]!==""&&(g+="="+encodeURIComponent(String(d[f])));a.push(g)}}return this.mb=a.join("&")};k.clone=function(){var a=new jk;a.mb=this.mb;this.qa&&(a.qa=new Map(this.qa),a.Na=this.Na);return a};k.nb=function(a){a=String(a);this.Ab&&(a=a.toLowerCase());return a};
k.xh=function(a){a&&!this.Ab&&(tk(this),this.mb=null,this.qa.forEach(function(b,c){var d=c.toLowerCase();c!=d&&(this.remove(c),this.setValues(d,b))},this));this.Ab=a};k.extend=function(a){for(var b=0;b<arguments.length;b++)bk(arguments[b],function(c,d){this.add(d,c)},this)};var wk=function(a){var b=Error();b.name="SecurityError";a="Failed to read a named property '"+a+"' from 'Window': ";if("location"in window)try{a+='Blocked a frame with origin "'+window.location.href+'" from accessing a cross-origin frame.'}catch(c){a+="An attempt was made to break through the security policy of the user agent."}b.message=a;throw b;},xk=function(){return{get closed(){return!0},get location(){wk("location")},get document(){wk("document")},postMessage:function(){},close:function(){},
focus:function(){}}};var yk={wm:{Ve:"https://staging-identitytoolkit.sandbox.googleapis.com/identitytoolkit/v3/relyingparty/",Jf:"https://staging-securetoken.sandbox.googleapis.com/v1/token",ef:"https://staging-identitytoolkit.sandbox.googleapis.com/v2/",id:"b"},Cm:{Ve:"https://www.googleapis.com/identitytoolkit/v3/relyingparty/",Jf:"https://securetoken.googleapis.com/v1/token",ef:"https://identitytoolkit.googleapis.com/v2/",id:"p"},Dm:{Ve:"https://staging-www.sandbox.googleapis.com/identitytoolkit/v3/relyingparty/",
Jf:"https://staging-securetoken.sandbox.googleapis.com/v1/token",ef:"https://staging-identitytoolkit.sandbox.googleapis.com/v2/",id:"s"},Em:{Ve:"https://www-googleapis-test.sandbox.google.com/identitytoolkit/v3/relyingparty/",Jf:"https://test-securetoken.sandbox.googleapis.com/v1/token",ef:"https://test-identitytoolkit.sandbox.googleapis.com/v2/",id:"t"}};
function zk(a){for(var b in yk)if(yk[b].id===a)return a=yk[b],{firebaseEndpoint:a.Ve,secureTokenEndpoint:a.Jf,identityPlatformEndpoint:a.ef};return null}var Ak;Ak=zk("__EID__")?"__EID__":void 0;var angular,Ck=function(){var a=Bk();return Bc&&!!Oc&&Oc==11||/Edge\/\d+/.test(a)},Dk=function(){return r.window&&r.window.location.href||self&&self.location&&self.location.href||""},Ek=function(a,b){b=b||r.window;var c="about:blank";a&&(c=bb(fb(a)));b.location.href=c},Fk=function(a,b){var c=[],d;for(d in a)d in b?typeof a[d]!=typeof b[d]?c.push(d):typeof a[d]=="object"&&a[d]!=null&&b[d]!=null?Fk(a[d],b[d]).length>0&&c.push(d):a[d]!==b[d]&&c.push(d):c.push(d);for(var e in b)e in a||c.push(e);return c},
Hk=function(){var a=Bk();a=Gk(a)!="Chrome"?null:(a=a.match(/\sChrome\/(\d+)/i))&&a.length==2?parseInt(a[1],10):null;return a&&a<30?!1:!Bc||!Oc||Oc>9},Ik=function(a){a=(a||Bk()).toLowerCase();return a.match(/android/)||a.match(/webos/)||a.match(/iphone|ipad|ipod/)||a.match(/blackberry/)||a.match(/windows phone/)||a.match(/iemobile/)?!0:!1},Jk=function(a){a=a||r.window;try{a.close()}catch(b){}},Kk=function(a,b,c){var d=Math.floor(Math.random()*1E9).toString();b=b||500;c=c||600;var e=(window.screen.availHeight-
c)/2,f=(window.screen.availWidth-b)/2;b={width:b,height:c,top:e>0?e:0,left:f>0?f:0,location:!0,resizable:!0,statusbar:!0,toolbar:!1};c=Bk().toLowerCase();d&&(b.target=d,B(c,"crios/")&&(b.target="_blank"));Gk(Bk())=="Firefox"&&(a=a||"http://localhost",b.scrollbars=!0);e=a||"";(d=b)||(d={});a=window;b=e instanceof $a?e:fb(typeof e.href!="undefined"?e.href:String(e));c=self.crossOriginIsolated!==void 0;f="strict-origin-when-cross-origin";window.Request&&(f=(new Request("/")).referrerPolicy);var g=d.noreferrer;
if(c&&g&&f==="unsafe-url")throw Error("Cannot use the noreferrer option on a page that sets a referrer-policy of `unsafe-url` in modern browsers!");c=g&&!c;e=d.target||e.target;f=[];g=[];for(l in d){var h=d[l];switch(l){case "width":case "height":case "top":case "left":f.push(l+"="+h);break;case "target":break;case "noopener":case "noreferrer":h?g.push(l):g.push(l+"=false");break;case "attributionsrc":f.push(l+(h?"="+h:""));break;default:f.push(l+"="+(h?1:0))}}["_blank","_self","_top","_parent",""].includes(e);
var l=f.join(",");(yc()||C("iPad")||C("iPod"))&&a.navigator&&a.navigator.standalone&&e&&e!="_self"?(l=Tc(document,"A"),b=ib(b),b!==void 0&&(l.href=b),l.target=e,c&&(l.rel="noreferrer"),((d=d.attributionsrc)||d==="")&&l.setAttribute("attributionsrc",d),d=document.createEvent("MouseEvent"),d.initMouseEvent("click",!0,!0,a,1),l.dispatchEvent(d),l=xk()):c?(l=Sb(a,"",e,l),a=bb(b),l&&(l.opener=null,a===""&&(a="javascript:''"),Rb.test(a)&&(a.indexOf("&")!=-1&&(a=a.replace(Lb,"&amp;")),a.indexOf("<")!=-1&&
(a=a.replace(Mb,"&lt;")),a.indexOf(">")!=-1&&(a=a.replace(Nb,"&gt;")),a.indexOf('"')!=-1&&(a=a.replace(Ob,"&quot;")),a.indexOf("'")!=-1&&(a=a.replace(Pb,"&#39;")),a.indexOf("\x00")!=-1&&(a=a.replace(Qb,"&#0;"))),a='<meta name="referrer" content="no-referrer"><meta http-equiv="refresh" content="0; url='+a+'">',lc("b/12014412, meta tag with sanitized URL"),a=Va(a),(d=l.document)&&d.write&&(d.write(Wa(a)),d.close()))):((l=Sb(a,b,e,l))&&d.noopener&&(l.opener=null),l&&d.noreferrer&&(l.opener=null));if(l)try{l.focus()}catch(m){}return l},
Lk=function(a){return new F(function(b){var c=function(){Yj(2E3).then(function(){if(!a||a.closed)b();else return c()})};return c()})},Nk=function(a,b){var c=M(b);b=c.Va;c=c.Ga;for(var d=0;d<a.length;d++){var e=a[d];e.indexOf("chrome-extension://")==0?e=M(e).Ga==c&&b=="chrome-extension":b!="http"&&b!="https"?e=!1:Mk.test(e)?e=c==e:(e=e.split(".").join("\\."),e=(new RegExp("^(.+\\."+e+"|"+e+")$","i")).test(c));if(e)return!0}return!1},Mk=/^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/,Ok=/^[^@]+@[^@]+$/,Pk=function(){var a=
null;return(new F(function(b){r.document.readyState=="complete"?b():(a=function(){b()},Ui(window,"load",a))})).l(function(b){cj(window,"load",a);throw b;})},Rk=function(){return Qk()?Pk().then(function(){return new F(function(a,b){var c=r.document,d=setTimeout(function(){b(Error("Cordova framework is not ready."))},1E3);c.addEventListener("deviceready",function(){clearTimeout(d);a()},!1)})}):H(Error("Cordova must run in an Android or iOS file scheme."))},Qk=function(){var a=Bk();return!(Sk()!=="file:"&&
Sk()!=="ionic:"||!a.toLowerCase().match(/iphone|ipad|ipod|android/))},Tk=function(){var a=r.window;try{return!(!a||a==a.top)}catch(b){return!1}},Uk=function(){return typeof r.WorkerGlobalScope!=="undefined"&&typeof r.importScripts==="function"},Vk=function(){return firebase.INTERNAL.hasOwnProperty("reactNative")?"ReactNative":firebase.INTERNAL.hasOwnProperty("node")?"Node":Uk()?"Worker":"Browser"},Wk=function(){var a=Vk();return a==="ReactNative"||a==="Node"},Xk=function(){for(var a=50,b=[];a>0;)b.push("1234567890abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ".charAt(Math.floor(Math.random()*
62))),a--;return b.join("")},Gk=function(a){var b=a.toLowerCase();if(B(b,"opera/")||B(b,"opr/")||B(b,"opios/"))return"Opera";if(B(b,"iemobile"))return"IEMobile";if(B(b,"msie")||B(b,"trident/"))return"IE";if(B(b,"edge/"))return"Edge";if(B(b,"firefox/"))return"Firefox";if(B(b,"silk/"))return"Silk";if(B(b,"blackberry"))return"Blackberry";if(B(b,"webos"))return"Webos";if(!B(b,"safari/")||B(b,"chrome/")||B(b,"crios/")||B(b,"android"))if(!B(b,"chrome/")&&!B(b,"crios/")||B(b,"edge/")){if(B(b,"android"))return"Android";
if((a=a.match(RegExp("([a-zA-Z\\d\\.]+)/[a-zA-Z\\d\\.]*$")))&&a.length==2)return a[1]}else return"Chrome";else return"Safari";return"Other"},Yk={DEFAULT:"FirebaseCore-web",ym:"FirebaseUI-web",Bm:"gcip-iap"},Zk=function(a,b,c){c=c||[];var d=[],e={},f;for(f in Yk)e[Yk[f]]=!0;for(f=0;f<c.length;f++)typeof e[c[f]]!=="undefined"&&(delete e[c[f]],d.push(c[f]));d.sort();c=d;c.length||(c=["FirebaseCore-web"]);d=Vk();return(d==="Browser"?Gk(Bk()):d==="Worker"?Gk(Bk())+"-"+d:d)+"/"+a+"/"+b+"/"+c.join(",")},
Bk=function(){return r.navigator&&r.navigator.userAgent||""},N=function(a,b){a=a.split(".");b=b||r;var c;for(c=0;c<a.length&&typeof b=="object"&&b!=null;c++)b=b[a[c]];c!=a.length&&(b=void 0);return b},al=function(){try{var a=r.localStorage,b=$k();if(a)return a.setItem(b,"1"),a.removeItem(b),Ck()?!!r.indexedDB:!0}catch(c){return Uk()&&!!r.indexedDB}return!1},cl=function(){return(bl()||Sk()==="chrome-extension:"||Qk())&&!Wk()&&al()&&!Uk()},bl=function(){return Sk()==="http:"||Sk()==="https:"},Sk=function(){return r.location&&
r.location.protocol||null},dl=function(a){a=a||Bk();return Ik(a)||Gk(a)=="Firefox"?!1:!0},el=function(a){return typeof a==="undefined"?null:JSON.stringify(a)},fl=function(a){var b={},c;for(c in a)a.hasOwnProperty(c)&&a[c]!==null&&a[c]!==void 0&&(b[c]=a[c]);return b},gl=function(a){if(a!==null)return JSON.parse(a)},$k=function(a){return a?a:""+Math.floor(Math.random()*1E9).toString()},hl=function(a){a=a||Bk();return Gk(a)=="Safari"||a.toLowerCase().match(/iphone|ipad|ipod/)?!1:!0},il=function(){var a=
r.___jsl;if(a&&a.H)for(var b in a.H)if(a.H[b].r=a.H[b].r||[],a.H[b].L=a.H[b].L||[],a.H[b].r=a.H[b].L.concat(),a.CP)for(var c=0;c<a.CP.length;c++)a.CP[c]=null},jl=function(a,b){if(a>b)throw Error("Short delay should be less than long delay!");this.Gj=a;this.rl=b;a=Bk();b=Vk();this.fl=Ik(a)||b==="ReactNative"};
jl.prototype.get=function(){var a=r.navigator;return(a&&typeof a.onLine==="boolean"&&(bl()||Sk()==="chrome-extension:"||typeof a.connection!=="undefined")?a.onLine:1)?this.fl?this.rl:this.Gj:Math.min(5E3,this.Gj)};
var kl=function(){var a=r.document;return a&&typeof a.visibilityState!=="undefined"?a.visibilityState=="visible":!0},ll=function(){var a=r.document,b=null;return kl()||!a?G():(new F(function(c){b=function(){kl()&&(a.removeEventListener("visibilitychange",b,!1),c())};a.addEventListener("visibilitychange",b,!1)})).l(function(c){a.removeEventListener("visibilitychange",b,!1);throw c;})},ml=function(a){typeof console!=="undefined"&&typeof console.warn==="function"&&console.warn(a)},nl=function(a){try{var b=
new Date(parseInt(a,10));if(!isNaN(b.getTime())&&!/[^0-9]/.test(a))return b.toUTCString()}catch(c){}return null},ol=function(){return!(!N("fireauth.oauthhelper",r)&&!N("fireauth.iframe",r))},pl=function(){var a=r.navigator;return a&&a.serviceWorker&&a.serviceWorker.controller||null},ql=function(){var a=r.navigator;return a&&a.serviceWorker?G().then(function(){return a.serviceWorker.ready}).then(function(b){return b.active||null}).l(function(){return null}):G(null)};var rl={};function sl(a){rl[a]||(rl[a]=!0,ml(a))};var tl;try{var ul={};Object.defineProperty(ul,"abcd",{configurable:!0,enumerable:!0,value:1});Object.defineProperty(ul,"abcd",{configurable:!0,enumerable:!0,value:2});tl=ul.abcd==2}catch(a){tl=!1}
var O=function(a,b,c){tl?Object.defineProperty(a,b,{configurable:!0,enumerable:!0,value:c}):a[b]=c},vl=function(a,b){if(b)for(var c in b)b.hasOwnProperty(c)&&O(a,c,b[c])},wl=function(a){var b={};vl(b,a);return b},xl=function(a,b){if(!b||!b.length)return!0;if(!a)return!1;for(var c=0;c<b.length;c++){var d=a[b[c]];if(d===void 0||d===null||d==="")return!1}return!0},yl=function(a){var b=a;if(typeof a=="object"&&a!=null){b="length"in a?[]:{};for(var c in a)O(b,c,yl(a[c]))}return b};var zl="oauth_consumer_key oauth_nonce oauth_signature oauth_signature_method oauth_timestamp oauth_token oauth_version".split(" "),Al=["client_id","response_type","scope","redirect_uri","state"],Bl={xm:{ee:"locale",zd:700,yd:600,providerId:"facebook.com",Ef:Al},zm:{ee:null,zd:500,yd:750,providerId:"github.com",Ef:Al},Am:{ee:"hl",zd:515,yd:680,providerId:"google.com",Ef:Al},Fm:{ee:"lang",zd:485,yd:705,providerId:"twitter.com",Ef:zl},vm:{ee:"locale",zd:640,yd:600,providerId:"apple.com",Ef:[]}};
function Cl(a){for(var b in Bl)if(Bl[b].providerId==a)return Bl[b];return null};var P=function(a,b,c){this.code="auth/"+a;this.message=b||Dl[a]||"";this.yj=c||null};w(P,Error);P.prototype.T=function(){var a={code:this.code,message:this.message};this.yj&&(a.serverResponse=this.yj);return a};P.prototype.toJSON=function(){return this.T()};
var El=function(a){var b=a&&a.code;return b?new P(b.substring(5),a.message,a.serverResponse):null},Dl={"api-key-service-blocked":"The request is denied because it violates [API key HTTP restrictions](https://cloud.google.com/docs/authentication/api-keys#adding_http_restrictions).","admin-restricted-operation":"This operation is restricted to administrators only.","argument-error":"","app-not-authorized":"This app, identified by the domain where it's hosted, is not authorized to use Firebase Authentication with the provided API key. Review your key configuration in the Google API console.",
"app-not-installed":"The requested mobile application corresponding to the identifier (Android package name or iOS bundle ID) provided is not installed on this device.","bad-request":"The requested action is invalid.","captcha-check-failed":"The reCAPTCHA response token provided is either invalid, expired, already used or the domain associated with it does not match the list of whitelisted domains.","code-expired":"The SMS code has expired. Please re-send the verification code to try again.","cordova-not-ready":"Cordova framework is not ready.",
"cors-unsupported":"This browser is not supported.","credential-already-in-use":"This credential is already associated with a different user account.","custom-token-mismatch":"The custom token corresponds to a different audience.","requires-recent-login":"This operation is sensitive and requires recent authentication. Log in again before retrying this request.","dynamic-link-not-activated":"Please activate Dynamic Links in the Firebase Console and agree to the terms and conditions.","email-change-needs-verification":"Multi-factor users must always have a verified email.",
"email-already-in-use":"The email address is already in use by another account.","expired-action-code":"The action code has expired. ","cancelled-popup-request":"This operation has been cancelled due to another conflicting popup being opened.","internal-error":"An internal error has occurred.","invalid-app-credential":"The phone verification request contains an invalid application verifier. The reCAPTCHA token response is either invalid or expired.","invalid-app-id":"The mobile app identifier is not registed for the current project.",
"invalid-user-token":"This user's credential isn't valid for this project. This can happen if the user's token has been tampered with, or if the user isn't for the project associated with this API key.","invalid-auth-event":"An internal error has occurred.","invalid-verification-code":"The SMS verification code used to create the phone auth credential is invalid. Please resend the verification code sms and be sure to use the verification code provided by the user.","invalid-continue-uri":"The continue URL provided in the request is invalid.",
"invalid-cordova-configuration":"The following Cordova plugins must be installed to enable OAuth sign-in: cordova-plugin-buildinfo, cordova-universal-links-plugin, cordova-plugin-browsertab, cordova-plugin-inappbrowser and cordova-plugin-customurlscheme.","invalid-custom-token":"The custom token format is incorrect. Please check the documentation.","invalid-dynamic-link-domain":"The provided dynamic link domain is not configured or authorized for the current project.","invalid-email":"The email address is badly formatted.",
"invalid-api-key":"Your API key is invalid, please check you have copied it correctly.","invalid-cert-hash":"The SHA-1 certificate hash provided is invalid.","invalid-credential":"The supplied auth credential is malformed or has expired.","invalid-message-payload":"The email template corresponding to this action contains invalid characters in its message. Please fix by going to the Auth email templates section in the Firebase Console.","invalid-multi-factor-session":"The request does not contain a valid proof of first factor successful sign-in.",
"invalid-oauth-provider":"EmailAuthProvider is not supported for this operation. This operation only supports OAuth providers.","invalid-oauth-client-id":"The OAuth client ID provided is either invalid or does not match the specified API key.","unauthorized-domain":"This domain is not authorized for OAuth operations for your Firebase project. Edit the list of authorized domains from the Firebase console.","invalid-action-code":"The action code is invalid. This can happen if the code is malformed, expired, or has already been used.",
"wrong-password":"The password is invalid or the user does not have a password.","invalid-persistence-type":"The specified persistence type is invalid. It can only be local, session or none.","invalid-phone-number":"The format of the phone number provided is incorrect. Please enter the phone number in a format that can be parsed into E.164 format. E.164 phone numbers are written in the format [+][country code][subscriber number including area code].","invalid-provider-id":"The specified provider ID is invalid.",
"invalid-recipient-email":"The email corresponding to this action failed to send as the provided recipient email address is invalid.","invalid-sender":"The email template corresponding to this action contains an invalid sender email or name. Please fix by going to the Auth email templates section in the Firebase Console.","invalid-verification-id":"The verification ID used to create the phone auth credential is invalid.","invalid-tenant-id":"The Auth instance's tenant ID is invalid.","multi-factor-info-not-found":"The user does not have a second factor matching the identifier provided.",
"multi-factor-auth-required":"Proof of ownership of a second factor is required to complete sign-in.","missing-android-pkg-name":"An Android Package Name must be provided if the Android App is required to be installed.","auth-domain-config-required":"Be sure to include authDomain when calling firebase.initializeApp(), by following the instructions in the Firebase console.","missing-app-credential":"The phone verification request is missing an application verifier assertion. A reCAPTCHA response token needs to be provided.",
"missing-verification-code":"The phone auth credential was created with an empty SMS verification code.","missing-continue-uri":"A continue URL must be provided in the request.","missing-iframe-start":"An internal error has occurred.","missing-ios-bundle-id":"An iOS Bundle ID must be provided if an App Store ID is provided.","missing-multi-factor-info":"No second factor identifier is provided.","missing-multi-factor-session":"The request is missing proof of first factor successful sign-in.","missing-or-invalid-nonce":"The request does not contain a valid nonce. This can occur if the SHA-256 hash of the provided raw nonce does not match the hashed nonce in the ID token payload.",
"missing-phone-number":"To send verification codes, provide a phone number for the recipient.","missing-verification-id":"The phone auth credential was created with an empty verification ID.","app-deleted":"This instance of FirebaseApp has been deleted.","account-exists-with-different-credential":"An account already exists with the same email address but different sign-in credentials. Sign in using a provider associated with this email address.","network-request-failed":"A network error (such as timeout, interrupted connection or unreachable host) has occurred.",
"no-auth-event":"An internal error has occurred.","no-such-provider":"User was not linked to an account with the given provider.","null-user":"A null user object was provided as the argument for an operation which requires a non-null user object.","operation-not-allowed":"The given sign-in provider is disabled for this Firebase project. Enable it in the Firebase console, under the sign-in method tab of the Auth section.","operation-not-supported-in-this-environment":'This operation is not supported in the environment this application is running on. "location.protocol" must be http, https or chrome-extension and web storage must be enabled.',
"password-does-not-meet-requirements":"The provided password does not meet the configured requirements.","popup-blocked":"Unable to establish a connection with the popup. It may have been blocked by the browser.","popup-closed-by-user":"The popup has been closed by the user before finalizing the operation.","provider-already-linked":"User can only be linked to one identity for the given provider.","quota-exceeded":"The project's quota for this operation has been exceeded.","redirect-cancelled-by-user":"The redirect operation has been cancelled by the user before finalizing.",
"redirect-operation-pending":"A redirect sign-in operation is already pending.","rejected-credential":"The request contains malformed or mismatching credentials.","second-factor-already-in-use":"The second factor is already enrolled on this account.","maximum-second-factor-count-exceeded":"The maximum allowed number of second factors on a user has been exceeded.","tenant-id-mismatch":"The provided tenant ID does not match the Auth instance's tenant ID",timeout:"The operation has timed out.","user-token-expired":"The user's credential is no longer valid. The user must sign in again.",
"too-many-requests":"We have blocked all requests from this device due to unusual activity. Try again later.","unauthorized-continue-uri":"The domain of the continue URL is not whitelisted.  Please whitelist the domain in the Firebase console.","unsupported-first-factor":"Enrolling a second factor or signing in with a multi-factor account requires sign-in with a supported first factor.","unsupported-persistence-type":"The current environment does not support the specified persistence type.","unsupported-tenant-operation":"This operation is not supported in a multi-tenant context.",
"unverified-email":"The operation requires a verified email.","user-cancelled":"The user did not grant your application the permissions it requested.","user-not-found":"There is no user record corresponding to this identifier. The user may have been deleted.","user-disabled":"The user account has been disabled by an administrator.","user-mismatch":"The supplied credentials do not correspond to the previously signed in user.","user-signed-out":"","weak-password":"The password must be 6 characters long or more.",
"web-storage-unsupported":"This browser is not supported or 3rd party cookies and data may be disabled."};var Fl=function(a,b,c,d,e,f,g){this.Ih=a;this.Ja=b||null;this.Kd=c||null;this.we=d||null;this.dh=f||null;this.ma=g||null;this.yb=e||null;if(this.Kd||this.yb){if(this.Kd&&this.yb)throw new P("invalid-auth-event");if(this.Kd&&!this.we)throw new P("invalid-auth-event");}else throw new P("invalid-auth-event");};k=Fl.prototype;k.getType=function(){return this.Ih};k.getUid=function(){var a=[];a.push(this.Ih);this.Ja&&a.push(this.Ja);this.we&&a.push(this.we);this.ma&&a.push(this.ma);return a.join("-")};
k.Yd=function(){return this.we};k.getError=function(){return this.yb};k.T=function(){return{type:this.Ih,eventId:this.Ja,urlResponse:this.Kd,sessionId:this.we,postBody:this.dh,tenantId:this.ma,error:this.yb&&this.yb.T()}};var Gl=function(a){a=a||{};return a.type?new Fl(a.type,a.eventId,a.urlResponse,a.sessionId,a.error&&El(a.error),a.postBody,a.tenantId):null};var Hl=function(a,b){if(!b||!a||!a.mfaEnrollmentId)throw new P("internal-error","Internal assert: invalid MultiFactorInfo object");O(this,"uid",a.mfaEnrollmentId);O(this,"displayName",a.displayName||null);O(this,"enrollmentTime",a.enrolledAt?(new Date(a.enrolledAt)).toUTCString():null);O(this,"factorId",b)};Hl.prototype.T=function(){return{uid:this.uid,displayName:this.displayName,factorId:this.factorId,enrollmentTime:this.enrollmentTime}};
var Kl=function(a){try{if(a.phoneInfo)return new Il(a);if(a.totpInfo)return new Jl(a)}catch(b){}return null},Il=function(a){var b=a.phoneInfo;if(!b)throw new P("internal-error","Internal assert: invalid Phone MultiFactorInfo object");Hl.call(this,a,"phone");O(this,"phoneNumber",b)};p(Il,Hl);Il.prototype.T=function(){var a=Hl.prototype.T.call(this);a.phoneNumber=this.phoneNumber;return a};
var Jl=function(a){var b=a.totpInfo;if(!b)throw new P("internal-error","Internal assert: invalid TOTP MultiFactorInfo object");Hl.call(this,a,"totp");O(this,"totpInfo",b)};p(Jl,Hl);Jl.prototype.T=function(){var a=Hl.prototype.T.call(this);a.totpInfo=this.totpInfo;return a};var Ll=function(a){var b={},c=a.email,d=a.newEmail,e=a.requestType;a=Kl(a.mfaInfo);if(!e||e!="EMAIL_SIGNIN"&&e!="VERIFY_AND_CHANGE_EMAIL"&&!c||e=="VERIFY_AND_CHANGE_EMAIL"&&!d||e=="REVERT_SECOND_FACTOR_ADDITION"&&!a)throw Error("Invalid checkActionCode response!");e=="VERIFY_AND_CHANGE_EMAIL"?(b.fromEmail=c||null,b.previousEmail=c||null,b.email=d):(b.fromEmail=d||null,b.previousEmail=d||null,b.email=c||null);b.multiFactorInfo=a||null;O(this,"operation",e);O(this,"data",yl(b))};var Nl=function(a){a=M(a);var b=qk(a,"apiKey")||null,c=qk(a,"oobCode")||null,d=qk(a,"mode")||null;d=d?Ml[d]||null:null;if(!b||!c||!d)throw new P("argument-error","apiKey, oobCodeand mode are required in a valid action code URL.");vl(this,{apiKey:b,operation:d,code:c,continueUrl:qk(a,"continueUrl")||null,languageCode:qk(a,"languageCode")||null,tenantId:qk(a,"tenantId")||null})},Ol=function(a){try{return new Nl(a)}catch(b){return null}},Ml={recoverEmail:"RECOVER_EMAIL",resetPassword:"PASSWORD_RESET",
revertSecondFactorAddition:"REVERT_SECOND_FACTOR_ADDITION",signIn:"EMAIL_SIGNIN",verifyAndChangeEmail:"VERIFY_AND_CHANGE_EMAIL",verifyEmail:"VERIFY_EMAIL"};var Pl=function(a){var b=M(a),c=qk(b,"link"),d=qk(M(c),"link");b=qk(b,"deep_link_id");return qk(M(b),"link")||b||d||c||a};var Ql=function(a){var b="unauthorized-domain",c=void 0,d=M(a);a=d.Ga;d=d.Va;d=="chrome-extension"?c=mc("This chrome extension ID (chrome-extension://%s) is not authorized to run this operation. If you are the app developer, add it to the OAuth redirect domains list in the Firebase console -> Auth section -> Sign in method tab.",a):d=="http"||d=="https"?c=mc("This domain (%s) is not authorized to run this operation. If you are the app developer, add it to the OAuth redirect domains list in the Firebase console -> Auth section -> Sign in method tab.",
a):b="operation-not-supported-in-this-environment";P.call(this,b,c)};p(Ql,P);var Sl=function(a){var b=Rl(a);if(!(b&&b.sub&&b.iss&&b.aud&&b.exp))throw Error("Invalid JWT");this.ll=a;this.yg=b.exp;this.ol=b.sub;a=Date.now()/1E3;this.Xk=b.iat||(a>this.yg?this.yg:a);this.Gc=b.email||null;this.hh=b.provider_id||b.firebase&&b.firebase.sign_in_provider||null;this.ma=b.firebase&&b.firebase.tenant||null;this.ek=!!b.is_anonymous||this.hh=="anonymous"};Sl.prototype.getEmail=function(){return this.Gc};Sl.prototype.isAnonymous=function(){return this.ek};Sl.prototype.toString=function(){return this.ll};
var Tl=function(a){try{return new Sl(a)}catch(b){return null}},Rl=function(a){if(!a)return null;a=a.split(".");if(a.length!=3)return null;a=a[1];for(var b=(4-a.length%4)%4,c=0;c<b;c++)a+=".";try{var d=wi(a);a=[];for(c=b=0;b<d.length;){var e=d[b++];if(e<128)a[c++]=String.fromCharCode(e);else if(e>191&&e<224){var f=d[b++];a[c++]=String.fromCharCode((e&31)<<6|f&63)}else if(e>239&&e<365){var g=d[b++],h=d[b++],l=d[b++],m=((e&7)<<18|(g&63)<<12|(h&63)<<6|l&63)-65536;a[c++]=String.fromCharCode(55296+(m>>
10));a[c++]=String.fromCharCode(56320+(m&1023))}else{var q=d[b++],x=d[b++];a[c++]=String.fromCharCode((e&15)<<12|(q&63)<<6|x&63)}}return JSON.parse(a.join(""))}catch(A){}return null};var Ul=function(a){var b=Rl(a);if(!(b&&b.exp&&b.auth_time&&b.iat))throw new P("internal-error","An internal error occurred. The token obtained by Firebase appears to be malformed. Please retry the operation.");vl(this,{token:a,expirationTime:nl(b.exp*1E3),authTime:nl(b.auth_time*1E3),issuedAtTime:nl(b.iat*1E3),signInProvider:b.firebase&&b.firebase.sign_in_provider?b.firebase.sign_in_provider:null,signInSecondFactor:b.firebase&&b.firebase.sign_in_second_factor?b.firebase.sign_in_second_factor:null,
claims:b})};var Vl=function(a,b){if(!a&&!b)throw new P("internal-error","Internal assert: no raw session string available");if(a&&b)throw new P("internal-error","Internal assert: unable to determine the session type");this.df=a||null;this.Ti=b||null;this.type=this.df?"enroll":"signin"};Vl.prototype.Xd=function(){return this.df?G(this.df):G(this.Ti)};Vl.prototype.T=function(){return this.type=="enroll"?{multiFactorSession:{idToken:this.df}}:{multiFactorSession:{pendingCredential:this.Ti}}};var Wl=function(){};Wl.prototype.Jc=function(){};Wl.prototype.ud=function(){};Wl.prototype.ie=function(){};Wl.prototype.T=function(){};
var Xl=function(a,b){return a.then(function(c){if(c.idToken){var d=Tl(c.idToken);if(!d||b!=d.ol)throw new P("user-mismatch");return c}throw new P("user-mismatch");}).l(function(c){throw c&&c.code&&c.code=="auth/user-not-found"?new P("user-mismatch"):c;})},Yl=function(a,b){if(b)this.Xb=b;else throw new P("internal-error","failed to construct a credential");O(this,"providerId",a);O(this,"signInMethod",a)};k=Yl.prototype;k.Jc=function(a){return Zl(a,this.Nc())};
k.ud=function(a,b){var c=this.Nc();c.idToken=b;return $l(a,c)};k.ie=function(a,b){var c=this.Nc();return Xl(am(a,c),b)};k.Nc=function(){return{pendingToken:this.Xb,requestUri:"http://localhost"}};k.T=function(){return{providerId:this.providerId,signInMethod:this.signInMethod,pendingToken:this.Xb}};
var bm=function(a){if(a&&a.providerId&&a.signInMethod&&a.providerId.indexOf("saml.")==0&&a.pendingToken)try{return new Yl(a.providerId,a.pendingToken)}catch(b){}return null},cm=function(a,b,c){this.Xb=null;if(b.idToken||b.accessToken)b.idToken&&O(this,"idToken",b.idToken),b.accessToken&&O(this,"accessToken",b.accessToken),b.nonce&&!b.pendingToken&&O(this,"nonce",b.nonce),b.pendingToken&&(this.Xb=b.pendingToken);else if(b.oauthToken&&b.oauthTokenSecret)O(this,"accessToken",b.oauthToken),O(this,"secret",
b.oauthTokenSecret);else throw new P("internal-error","failed to construct a credential");O(this,"providerId",a);O(this,"signInMethod",c)};k=cm.prototype;k.Jc=function(a){return Zl(a,this.Nc())};k.ud=function(a,b){var c=this.Nc();c.idToken=b;return $l(a,c)};k.ie=function(a,b){var c=this.Nc();return Xl(am(a,c),b)};
k.Nc=function(){var a={};this.idToken&&(a.id_token=this.idToken);this.accessToken&&(a.access_token=this.accessToken);this.secret&&(a.oauth_token_secret=this.secret);a.providerId=this.providerId;this.nonce&&!this.Xb&&(a.nonce=this.nonce);a={postBody:uk(a).toString(),requestUri:"http://localhost"};this.Xb&&(delete a.postBody,a.pendingToken=this.Xb);return a};
k.T=function(){var a={providerId:this.providerId,signInMethod:this.signInMethod};this.idToken&&(a.oauthIdToken=this.idToken);this.accessToken&&(a.oauthAccessToken=this.accessToken);this.secret&&(a.oauthTokenSecret=this.secret);this.nonce&&(a.nonce=this.nonce);this.Xb&&(a.pendingToken=this.Xb);return a};
var dm=function(a){if(a&&a.providerId&&a.signInMethod){var b={idToken:a.oauthIdToken,accessToken:a.oauthTokenSecret?null:a.oauthAccessToken,oauthTokenSecret:a.oauthTokenSecret,oauthToken:a.oauthTokenSecret&&a.oauthAccessToken,nonce:a.nonce,pendingToken:a.pendingToken};try{return new cm(a.providerId,b,a.signInMethod)}catch(c){}}return null},em=function(a,b){this.Kl=b||[];vl(this,{providerId:a,isOAuthProvider:!0});this.ai={};this.Ng=(Cl(a)||{}).ee||null;this.qg=null};
em.prototype.setCustomParameters=function(a){this.ai=Ib(a);return this};var fm=function(a){if(typeof a!=="string"||a.indexOf("saml.")!=0)throw new P("argument-error",'SAML provider IDs must be prefixed with "saml."');em.call(this,a,[])};w(fm,em);var gm=function(a){em.call(this,a,Al);this.rh=[]};w(gm,em);gm.prototype.addScope=function(a){zb(this.rh,a)||this.rh.push(a);return this};gm.prototype.ui=function(){return Db(this.rh)};
gm.prototype.credential=function(a,b){a=u(a)?{idToken:a.idToken||null,accessToken:a.accessToken||null,nonce:a.rawNonce||null}:{idToken:a||null,accessToken:b||null};if(!a.idToken&&!a.accessToken)throw new P("argument-error","credential failed: must provide the ID token and/or the access token.");return new cm(this.providerId,a,this.providerId)};var hm=function(){gm.call(this,"facebook.com")};w(hm,gm);O(hm,"PROVIDER_ID","facebook.com");O(hm,"FACEBOOK_SIGN_IN_METHOD","facebook.com");
var im=function(a){if(!a)throw new P("argument-error","credential failed: expected 1 argument (the OAuth access token).");var b=a;u(a)&&(b=a.accessToken);return(new hm).credential({accessToken:b})},jm=function(){gm.call(this,"github.com")};w(jm,gm);O(jm,"PROVIDER_ID","github.com");O(jm,"GITHUB_SIGN_IN_METHOD","github.com");
var km=function(a){if(!a)throw new P("argument-error","credential failed: expected 1 argument (the OAuth access token).");var b=a;u(a)&&(b=a.accessToken);return(new jm).credential({accessToken:b})},lm=function(){gm.call(this,"google.com");this.addScope("profile")};w(lm,gm);O(lm,"PROVIDER_ID","google.com");O(lm,"GOOGLE_SIGN_IN_METHOD","google.com");
var mm=function(a,b){var c=a;u(a)&&(c=a.idToken,b=a.accessToken);return(new lm).credential({idToken:c,accessToken:b})},nm=function(){em.call(this,"twitter.com",zl)};w(nm,em);O(nm,"PROVIDER_ID","twitter.com");O(nm,"TWITTER_SIGN_IN_METHOD","twitter.com");
var om=function(a,b){var c=a;u(c)||(c={oauthToken:a,oauthTokenSecret:b});if(!c.oauthToken||!c.oauthTokenSecret)throw new P("argument-error","credential failed: expected 2 arguments (the OAuth access token and secret).");return new cm("twitter.com",c,"twitter.com")},qm=function(a,b,c){this.Gc=a;this.je=b;O(this,"providerId","password");O(this,"signInMethod",c===pm.EMAIL_LINK_SIGN_IN_METHOD?pm.EMAIL_LINK_SIGN_IN_METHOD:pm.EMAIL_PASSWORD_SIGN_IN_METHOD)};
qm.prototype.Jc=function(a){return this.signInMethod==pm.EMAIL_LINK_SIGN_IN_METHOD?Q(a,rm,{email:this.Gc,oobCode:this.je}):Q(a,sm,{email:this.Gc,password:this.je})};qm.prototype.ud=function(a,b){return this.signInMethod==pm.EMAIL_LINK_SIGN_IN_METHOD?Q(a,tm,{idToken:b,email:this.Gc,oobCode:this.je}):Q(a,um,{idToken:b,email:this.Gc,password:this.je})};qm.prototype.ie=function(a,b){return Xl(this.Jc(a),b)};qm.prototype.T=function(){return{email:this.Gc,password:this.je,signInMethod:this.signInMethod}};
var vm=function(a){return a&&a.email&&a.password?new qm(a.email,a.password,a.signInMethod):null},pm=function(){vl(this,{providerId:"password",isOAuthProvider:!1})},xm=function(a,b){b=wm(b);if(!b)throw new P("argument-error","Invalid email link!");return new qm(a,b.code,pm.EMAIL_LINK_SIGN_IN_METHOD)},wm=function(a){a=Pl(a);return(a=Ol(a))&&a.operation==="EMAIL_SIGNIN"?a:null};vl(pm,{PROVIDER_ID:"password"});vl(pm,{EMAIL_LINK_SIGN_IN_METHOD:"emailLink"});vl(pm,{EMAIL_PASSWORD_SIGN_IN_METHOD:"password"});
var ym=function(a){if(!(a.verificationId&&a.Tf||a.Be&&a.phoneNumber))throw new P("internal-error");this.wa=a;O(this,"providerId","phone");this.providerId="phone";O(this,"signInMethod","phone")};ym.prototype.Jc=function(a){return a.verifyPhoneNumber(zm(this))};ym.prototype.ud=function(a,b){var c=zm(this);c.idToken=b;return Q(a,Am,c)};ym.prototype.ie=function(a,b){var c=zm(this);c.operation="REAUTH";a=Q(a,Bm,c);return Xl(a,b)};
ym.prototype.T=function(){var a={providerId:"phone"};this.wa.verificationId&&(a.verificationId=this.wa.verificationId);this.wa.Tf&&(a.verificationCode=this.wa.Tf);this.wa.Be&&(a.temporaryProof=this.wa.Be);this.wa.phoneNumber&&(a.phoneNumber=this.wa.phoneNumber);return a};
var Cm=function(a){if(a&&a.providerId==="phone"&&(a.verificationId&&a.verificationCode||a.temporaryProof&&a.phoneNumber)){var b={};z(["verificationId","verificationCode","temporaryProof","phoneNumber"],function(c){a[c]&&(b[c]=a[c])});return new ym(b)}return null},zm=function(a){return a.wa.Be&&a.wa.phoneNumber?{temporaryProof:a.wa.Be,phoneNumber:a.wa.phoneNumber}:{sessionInfo:a.wa.verificationId,code:a.wa.Tf}},Dm=function(a){try{this.Ke=a||firebase.auth()}catch(b){throw new P("argument-error","Either an instance of firebase.auth.Auth must be passed as an argument to the firebase.auth.PhoneAuthProvider constructor, or the default firebase App instance must be initialized via firebase.initializeApp().");
}vl(this,{providerId:"phone",isOAuthProvider:!1})};
Dm.prototype.verifyPhoneNumber=function(a,b){var c=this.Ke.o;return G(b.verify()).then(function(d){if(typeof d!=="string")throw new P("argument-error","An implementation of firebase.auth.ApplicationVerifier.prototype.verify() must return a firebase.Promise that resolves with a string.");switch(b.type){case "recaptcha":var e=u(a)?a.session:null,f=u(a)?a.phoneNumber:a;return(e&&e.type=="enroll"?e.Xd().then(function(g){return Em(c,{idToken:g,phoneEnrollmentInfo:{phoneNumber:f,recaptchaToken:d}})}):e&&
e.type=="signin"?e.Xd().then(function(g){return Fm(c,{mfaPendingCredential:g,mfaEnrollmentId:a.multiFactorHint&&a.multiFactorHint.uid||a.multiFactorUid,phoneSignInInfo:{recaptchaToken:d}})}):Gm(c,{phoneNumber:f,recaptchaToken:d})).then(function(g){typeof b.reset==="function"&&b.reset();return g},function(g){typeof b.reset==="function"&&b.reset();throw g;});default:throw new P("argument-error",'Only firebase.auth.ApplicationVerifiers with type="recaptcha" are currently supported.');}})};
var Hm=function(a,b){if(!a)throw new P("missing-verification-id");if(!b)throw new P("missing-verification-code");return new ym({verificationId:a,Tf:b})};vl(Dm,{PROVIDER_ID:"phone"});vl(Dm,{PHONE_SIGN_IN_METHOD:"phone"});
var Im=function(a){if(a.temporaryProof&&a.phoneNumber)return new ym({Be:a.temporaryProof,phoneNumber:a.phoneNumber});var b=a&&a.providerId;if(!b||b==="password")return null;var c=a&&a.oauthAccessToken,d=a&&a.oauthTokenSecret,e=a&&a.nonce,f=a&&a.oauthIdToken,g=a&&a.pendingToken;try{switch(b){case "google.com":return mm(f,c);case "facebook.com":return im(c);case "github.com":return km(c);case "twitter.com":return om(c,d);default:return c||d||f||g?g?b.indexOf("saml.")==0?new Yl(b,g):new cm(b,{pendingToken:g,
idToken:a.oauthIdToken,accessToken:a.oauthAccessToken},b):(new gm(b)).credential({idToken:f,accessToken:c,rawNonce:e}):null}}catch(h){return null}},Jm=function(a){if(!a.isOAuthProvider)throw new P("invalid-oauth-provider");};var Km=function(a,b,c){P.call(this,a,c);a=b||{};a.email&&O(this,"email",a.email);a.phoneNumber&&O(this,"phoneNumber",a.phoneNumber);a.credential&&O(this,"credential",a.credential);a.tenantId&&O(this,"tenantId",a.tenantId)};p(Km,P);Km.prototype.T=function(){var a={code:this.code,message:this.message};this.email&&(a.email=this.email);this.phoneNumber&&(a.phoneNumber=this.phoneNumber);this.tenantId&&(a.tenantId=this.tenantId);var b=this.credential&&this.credential.T();b&&Kb(a,b);return a};
Km.prototype.toJSON=function(){return this.T()};var Lm=function(a){if(a.code){var b=a.code||"";b.indexOf("auth/")==0&&(b=b.substring(5));var c={credential:Im(a),tenantId:a.tenantId};if(a.email)c.email=a.email;else if(a.phoneNumber)c.phoneNumber=a.phoneNumber;else if(!c.credential)return new P(b,a.message||void 0);return new Km(b,c,a.message)}return null};var Mm=pa(["https://apis.google.com/js/client.js?onload=",""]),Nm=function(a){this.um=a};p(Nm,ij);Nm.prototype.Rd=function(){return new this.um};
var Sm=function(a,b,c){this.ha=a;b=b||{};this.xj=b.secureTokenEndpoint||"https://securetoken.googleapis.com/v1/token";this.Zl=b.secureTokenTimeout||Om;this.Kf=Ib(b.secureTokenHeaders||Pm);this.ki=b.firebaseEndpoint||"https://www.googleapis.com/identitytoolkit/v3/relyingparty/";this.Di=b.identityPlatformEndpoint||"https://identitytoolkit.googleapis.com/v2/";this.Ck=b.firebaseTimeout||Qm;this.ld=Ib(b.firebaseHeaders||Rm);c&&(this.ld["X-Client-Version"]=c,this.Kf["X-Client-Version"]=c);a=Vk()=="Node";
a=r.XMLHttpRequest||a&&firebase.INTERNAL.node&&firebase.INTERNAL.node.XMLHttpRequest;if(!a&&!Uk())throw new P("internal-error","The XMLHttpRequest compatibility library was not found.");this.If=void 0;Uk()?this.If=new oj({tm:self}):Wk()?this.If=new Nm(a):this.If=new lj;this.ma=null},Tm,Um=function(a,b){b?a.ld["X-Firebase-Locale"]=b:delete a.ld["X-Firebase-Locale"]},Wm=function(a,b){b&&(a.xj=Vm("https://securetoken.googleapis.com/v1/token",b),a.ki=Vm("https://www.googleapis.com/identitytoolkit/v3/relyingparty/",
b),a.Di=Vm("https://identitytoolkit.googleapis.com/v2/",b))},Vm=function(a,b){a=M(a);b=M(b.url);a.setPath(a.Ga+a.getPath());dk(a,b.Va);fk(a,b.Ga);gk(a,b.Tb);return a.toString()},Xm=function(a,b){b?(a.ld["X-Client-Version"]=b,a.Kf["X-Client-Version"]=b):(delete a.ld["X-Client-Version"],delete a.Kf["X-Client-Version"])},Zm=function(a,b,c,d,e,f,g){Hk()||Uk()?a=v(a.bm,a):(Tm||(Tm=new F(function(h,l){Ym(h,l)})),a=v(a.am,a));a(b,c,d,e,f,g)};
Sm.prototype.bm=function(a,b,c,d,e,f){if(Uk()&&(typeof r.fetch==="undefined"||typeof r.Headers==="undefined"||typeof r.Request==="undefined"))throw new P("operation-not-supported-in-this-environment","fetch, Headers and Request native APIs or equivalent Polyfills must be available to support HTTP requests from a Worker environment.");var g=new Oj(this.If);if(f){g.Ce=Math.max(0,f);var h=setTimeout(function(){g.dispatchEvent("timeout")},f)}g.listen("complete",function(){h&&clearTimeout(h);var l=null;
try{var m=JSON,q=m.parse;try{var x=this.v?this.v.responseText:""}catch(A){kc(this.Ea,"Can not get responseText: "+A.message),x=""}l=q.call(m,x)||null}catch(A){l=null}b&&b(l)});bj(g,"ready",function(){h&&clearTimeout(h);this.dispose()});bj(g,"timeout",function(){h&&clearTimeout(h);this.dispose();b&&b(null)});g.send(a,c,d,e)};
var Ym=function(a,b){if(((window.gapi||{}).client||{}).request)a();else{r[$m]=function(){((window.gapi||{}).client||{}).request?a():b(Error("CORS_UNSUPPORTED"))};var c=lb(Mm,$m);Dj(Lj(c),function(){b(Error("CORS_UNSUPPORTED"))})}};
Sm.prototype.am=function(a,b,c,d,e){var f=this;Tm.then(function(){window.gapi.client.setApiKey(f.ha);var g=window.gapi.auth.getToken();window.gapi.auth.setToken(null);window.gapi.client.request({path:a,method:c,body:d,headers:e,authType:"none",callback:function(h){window.gapi.auth.setToken(g);b&&b(h)}})}).l(function(g){b&&b({error:{message:g&&g.message||"CORS_UNSUPPORTED"}})})};
var bn=function(a,b){return new F(function(c,d){b.grant_type=="refresh_token"&&b.refresh_token||b.grant_type=="authorization_code"&&b.code?Zm(a,a.xj+"?key="+encodeURIComponent(a.ha),function(e){e?e.error?d(an(e)):e.access_token&&e.refresh_token?c(e):d(new P("internal-error")):d(new P("network-request-failed"))},"POST",uk(b).toString(),a.Kf,a.Zl.get()):d(new P("internal-error"))})},cn=function(a,b,c,d,e,f,g){var h=M(b+c);L(h,"key",a.ha);g&&L(h,"cb",Date.now().toString());var l=d=="GET";if(l)for(var m in e)e.hasOwnProperty(m)&&
L(h,m,e[m]);return new F(function(q,x){Zm(a,h.toString(),function(A){A?A.error?x(an(A,f||{})):q(A):x(new P("network-request-failed"))},d,l?void 0:JSON.stringify(fl(e)),a.ld,a.Ck.get())})},dn=function(a){a=a.email;if(typeof a!=="string"||!Ok.test(a))throw new P("invalid-email");},en=function(a){"email"in a&&dn(a)},gn=function(a,b){return Q(a,fn,{identifier:b,continueUri:bl()?Dk():"http://localhost"}).then(function(c){return c.signinMethods||[]})},jn=function(a){return Q(a,hn,{}).then(function(b){return b.authorizedDomains||
[]})},kn=function(a){if(!a.idToken){if(a.mfaPendingCredential)throw new P("multi-factor-auth-required",null,Ib(a));throw new P("internal-error");}},ln=function(a){if(a.phoneNumber||a.temporaryProof){if(!a.phoneNumber||!a.temporaryProof)throw new P("internal-error");}else{if(!a.sessionInfo)throw new P("missing-verification-id");if(!a.code)throw new P("missing-verification-code");}};k=Sm.prototype;k.signInAnonymously=function(){return Q(this,mn,{})};
k.updateEmail=function(a,b){return Q(this,nn,{idToken:a,email:b})};k.updatePassword=function(a,b){return Q(this,um,{idToken:a,password:b})};k.updateProfile=function(a,b){var c={idToken:a},d=[];Fb(on,function(e,f){var g=b[f];g===null?d.push(e):f in b&&(c[f]=g)});d.length&&(c.deleteAttribute=d);return Q(this,nn,c)};k.sendPasswordResetEmail=function(a,b){a={requestType:"PASSWORD_RESET",email:a};Kb(a,b);return Q(this,pn,a)};
k.sendSignInLinkToEmail=function(a,b){a={requestType:"EMAIL_SIGNIN",email:a};Kb(a,b);return Q(this,qn,a)};k.sendEmailVerification=function(a,b){a={requestType:"VERIFY_EMAIL",idToken:a};Kb(a,b);return Q(this,rn,a)};k.verifyBeforeUpdateEmail=function(a,b,c){a={requestType:"VERIFY_AND_CHANGE_EMAIL",idToken:a,newEmail:b};Kb(a,c);return Q(this,sn,a)};var Gm=function(a,b){return Q(a,tn,b)};Sm.prototype.verifyPhoneNumber=function(a){return Q(this,un,a)};
var Em=function(a,b){return Q(a,vn,b).then(function(c){return c.phoneSessionInfo.sessionInfo})},wn=function(a){if(!a.phoneVerificationInfo)throw new P("internal-error");if(!a.phoneVerificationInfo.sessionInfo)throw new P("missing-verification-id");if(!a.phoneVerificationInfo.code)throw new P("missing-verification-code");},Fm=function(a,b){return Q(a,xn,b).then(function(c){return c.phoneResponseInfo.sessionInfo})},zn=function(a,b,c){return Q(a,yn,{idToken:b,deleteProvider:c})},An=function(a){if(!a.requestUri||
!a.sessionId&&!a.postBody&&!a.pendingToken)throw new P("internal-error");},Bn=function(a,b){b.oauthIdToken&&b.providerId&&b.providerId.indexOf("oidc.")==0&&!b.pendingToken&&(a.sessionId?b.nonce=a.sessionId:a.postBody&&(a=new jk(a.postBody),vk(a,"nonce")&&(b.nonce=a.get("nonce"))));return b},Dn=function(a){var b=null;a.needConfirmation?(a.code="account-exists-with-different-credential",b=Lm(a)):a.errorMessage=="FEDERATED_USER_ID_ALREADY_LINKED"?(a.code="credential-already-in-use",b=Lm(a)):a.errorMessage==
"EMAIL_EXISTS"?(a.code="email-already-in-use",b=Lm(a)):a.errorMessage&&(b=Cn(a.errorMessage));if(b)throw b;kn(a)},Zl=function(a,b){b.returnIdpCredential=!0;return Q(a,En,b)},$l=function(a,b){b.returnIdpCredential=!0;return Q(a,Fn,b)},am=function(a,b){b.returnIdpCredential=!0;b.autoCreate=!1;return Q(a,Gn,b)},Hn=function(a){if(!a.oobCode)throw new P("invalid-action-code");};Sm.prototype.confirmPasswordReset=function(a,b){return Q(this,In,{oobCode:a,newPassword:b})};
Sm.prototype.checkActionCode=function(a){return Q(this,Jn,{oobCode:a})};Sm.prototype.applyActionCode=function(a){return Q(this,Kn,{oobCode:a})};
var Q=function(a,b,c){if(!xl(c,b.Ma))return H(new P("internal-error"));var d=!!b.Fe,e=b.httpMethod||"POST",f;return G(c).then(b.oa).then(function(){b.Mb&&(c.returnSecureToken=!0);b.ta&&a.ma&&typeof c.tenantId==="undefined"&&(c.tenantId=a.ma);return d?cn(a,a.Di,b.endpoint,e,c,b.Zh,b.ig||!1):cn(a,a.ki,b.endpoint,e,c,b.Zh,b.ig||!1)}).then(function(g){f=g;return b.Ff?b.Ff(c,f):f}).then(b.Aa).then(function(){if(!b.Zb)return f;if(!(b.Zb in f))throw new P("internal-error");return f[b.Zb]})},Cn=function(a){return an({error:{errors:[{message:a}],
code:400,reason:a}})},an=function(a,b){var c=(a.error&&a.error.errors&&a.error.errors[0]||{}).reason||"";var d={keyInvalid:"invalid-api-key",ipRefererBlocked:"app-not-authorized"};if(c=d[c]?new P(d[c]):null)return c;c=a.error&&(a.error.reason||a.error.message)||"";d={INVALID_CUSTOM_TOKEN:"invalid-custom-token",CREDENTIAL_MISMATCH:"custom-token-mismatch",MISSING_CUSTOM_TOKEN:"internal-error",INVALID_IDENTIFIER:"invalid-email",MISSING_CONTINUE_URI:"internal-error",INVALID_EMAIL:"invalid-email",INVALID_PASSWORD:"wrong-password",
USER_DISABLED:"user-disabled",MISSING_PASSWORD:"internal-error",EMAIL_EXISTS:"email-already-in-use",PASSWORD_LOGIN_DISABLED:"operation-not-allowed",INVALID_IDP_RESPONSE:"invalid-credential",INVALID_PENDING_TOKEN:"invalid-credential",FEDERATED_USER_ID_ALREADY_LINKED:"credential-already-in-use",MISSING_OR_INVALID_NONCE:"missing-or-invalid-nonce",INVALID_MESSAGE_PAYLOAD:"invalid-message-payload",INVALID_RECIPIENT_EMAIL:"invalid-recipient-email",INVALID_SENDER:"invalid-sender",EMAIL_NOT_FOUND:"user-not-found",
RESET_PASSWORD_EXCEED_LIMIT:"too-many-requests",EXPIRED_OOB_CODE:"expired-action-code",INVALID_OOB_CODE:"invalid-action-code",MISSING_OOB_CODE:"internal-error",INVALID_PROVIDER_ID:"invalid-provider-id",CREDENTIAL_TOO_OLD_LOGIN_AGAIN:"requires-recent-login",INVALID_ID_TOKEN:"invalid-user-token",TOKEN_EXPIRED:"user-token-expired",USER_NOT_FOUND:"user-token-expired",CORS_UNSUPPORTED:"cors-unsupported",DYNAMIC_LINK_NOT_ACTIVATED:"dynamic-link-not-activated",INVALID_APP_ID:"invalid-app-id",TOO_MANY_ATTEMPTS_TRY_LATER:"too-many-requests",
WEAK_PASSWORD:"weak-password",PASSWORD_DOES_NOT_MEET_REQUIREMENTS:"password-does-not-meet-requirements",OPERATION_NOT_ALLOWED:"operation-not-allowed",USER_CANCELLED:"user-cancelled",CAPTCHA_CHECK_FAILED:"captcha-check-failed",INVALID_APP_CREDENTIAL:"invalid-app-credential",INVALID_CODE:"invalid-verification-code",INVALID_PHONE_NUMBER:"invalid-phone-number",INVALID_SESSION_INFO:"invalid-verification-id",INVALID_TEMPORARY_PROOF:"invalid-credential",INVALID_TENANT_ID:"invalid-tenant-id",MISSING_APP_CREDENTIAL:"missing-app-credential",
MISSING_CODE:"missing-verification-code",MISSING_PHONE_NUMBER:"missing-phone-number",MISSING_SESSION_INFO:"missing-verification-id",QUOTA_EXCEEDED:"quota-exceeded",SESSION_EXPIRED:"code-expired",REJECTED_CREDENTIAL:"rejected-credential",INVALID_CONTINUE_URI:"invalid-continue-uri",MISSING_ANDROID_PACKAGE_NAME:"missing-android-pkg-name",MISSING_IOS_BUNDLE_ID:"missing-ios-bundle-id",UNAUTHORIZED_DOMAIN:"unauthorized-continue-uri",INVALID_DYNAMIC_LINK_DOMAIN:"invalid-dynamic-link-domain",INVALID_OAUTH_CLIENT_ID:"invalid-oauth-client-id",
INVALID_CERT_HASH:"invalid-cert-hash",UNSUPPORTED_TENANT_OPERATION:"unsupported-tenant-operation",TENANT_ID_MISMATCH:"tenant-id-mismatch",ADMIN_ONLY_OPERATION:"admin-restricted-operation",INVALID_MFA_PENDING_CREDENTIAL:"invalid-multi-factor-session",MFA_ENROLLMENT_NOT_FOUND:"multi-factor-info-not-found",MISSING_MFA_PENDING_CREDENTIAL:"missing-multi-factor-session",MISSING_MFA_ENROLLMENT_ID:"missing-multi-factor-info",EMAIL_CHANGE_NEEDS_VERIFICATION:"email-change-needs-verification",SECOND_FACTOR_EXISTS:"second-factor-already-in-use",
SECOND_FACTOR_LIMIT_EXCEEDED:"maximum-second-factor-count-exceeded",UNSUPPORTED_FIRST_FACTOR:"unsupported-first-factor",UNVERIFIED_EMAIL:"unverified-email",API_KEY_SERVICE_BLOCKED:"api-key-service-blocked"};b=b||{};Kb(d,b);b=(b=c.match(/^[^\s]+\s*:\s*([\s\S]*)$/))&&b.length>1?b[1]:void 0;for(var e in d)if(c.indexOf(e)===0)return new P(d[e],b);!b&&a&&(b=el(a));return new P("internal-error",b)},Om=new jl(3E4,6E4),Pm={"Content-Type":"application/x-www-form-urlencoded"},Qm=new jl(3E4,6E4),Rm={"Content-Type":"application/json"},
$m="__fcb"+Math.floor(Math.random()*1E6).toString(),on={displayName:"DISPLAY_NAME",photoUrl:"PHOTO_URL"},Kn={endpoint:"setAccountInfo",oa:Hn,Zb:"email",ta:!0},Jn={endpoint:"resetPassword",oa:Hn,Aa:function(a){var b=a.requestType;if(!b||!a.email&&b!="EMAIL_SIGNIN"&&b!="VERIFY_AND_CHANGE_EMAIL")throw new P("internal-error");},ta:!0},Ln={endpoint:"signupNewUser",oa:function(a){dn(a);if(!a.password)throw new P("weak-password");},Aa:kn,Mb:!0,ta:!0},fn={endpoint:"createAuthUri",ta:!0},Mn={endpoint:"deleteAccount",
Ma:["idToken"]},yn={endpoint:"setAccountInfo",Ma:["idToken","deleteProvider"],oa:function(a){if(!Array.isArray(a.deleteProvider))throw new P("internal-error");}},rm={endpoint:"emailLinkSignin",Ma:["email","oobCode"],oa:dn,Aa:kn,Mb:!0,ta:!0},tm={endpoint:"emailLinkSignin",Ma:["idToken","email","oobCode"],oa:dn,Aa:kn,Mb:!0},Nn={endpoint:"accounts/mfaEnrollment:finalize",Ma:["idToken","phoneVerificationInfo"],oa:wn,Aa:kn,ta:!0,Fe:!0},On={endpoint:"accounts/mfaSignIn:finalize",Ma:["mfaPendingCredential",
"phoneVerificationInfo"],oa:wn,Aa:kn,ta:!0,Fe:!0},Pn={endpoint:"getAccountInfo"},qn={endpoint:"getOobConfirmationCode",Ma:["requestType"],oa:function(a){if(a.requestType!="EMAIL_SIGNIN")throw new P("internal-error");dn(a)},Zb:"email",ta:!0},rn={endpoint:"getOobConfirmationCode",Ma:["idToken","requestType"],oa:function(a){if(a.requestType!="VERIFY_EMAIL")throw new P("internal-error");},Zb:"email",ta:!0},sn={endpoint:"getOobConfirmationCode",Ma:["idToken","newEmail","requestType"],oa:function(a){if(a.requestType!=
"VERIFY_AND_CHANGE_EMAIL")throw new P("internal-error");},Zb:"email",ta:!0},pn={endpoint:"getOobConfirmationCode",Ma:["requestType"],oa:function(a){if(a.requestType!="PASSWORD_RESET")throw new P("internal-error");dn(a)},Zb:"email",ta:!0},hn={ig:!0,endpoint:"getProjectConfig",httpMethod:"GET"},Qn={ig:!0,endpoint:"getRecaptchaParam",httpMethod:"GET",Aa:function(a){if(!a.recaptchaSiteKey)throw new P("internal-error");}},In={endpoint:"resetPassword",oa:Hn,Zb:"email",ta:!0},tn={endpoint:"sendVerificationCode",
Ma:["phoneNumber","recaptchaToken"],Zb:"sessionInfo",ta:!0},nn={endpoint:"setAccountInfo",Ma:["idToken"],oa:en,Mb:!0},um={endpoint:"setAccountInfo",Ma:["idToken"],oa:function(a){en(a);if(!a.password)throw new P("weak-password");},Aa:kn,Mb:!0},mn={endpoint:"signupNewUser",Aa:kn,Mb:!0,ta:!0},vn={endpoint:"accounts/mfaEnrollment:start",Ma:["idToken","phoneEnrollmentInfo"],oa:function(a){if(!a.phoneEnrollmentInfo)throw new P("internal-error");if(!a.phoneEnrollmentInfo.phoneNumber)throw new P("missing-phone-number");
if(!a.phoneEnrollmentInfo.recaptchaToken)throw new P("missing-app-credential");},Aa:function(a){if(!a.phoneSessionInfo||!a.phoneSessionInfo.sessionInfo)throw new P("internal-error");},ta:!0,Fe:!0},xn={endpoint:"accounts/mfaSignIn:start",Ma:["mfaPendingCredential","mfaEnrollmentId","phoneSignInInfo"],oa:function(a){if(!a.phoneSignInInfo||!a.phoneSignInInfo.recaptchaToken)throw new P("missing-app-credential");},Aa:function(a){if(!a.phoneResponseInfo||!a.phoneResponseInfo.sessionInfo)throw new P("internal-error");
},ta:!0,Fe:!0},En={endpoint:"verifyAssertion",oa:An,Ff:Bn,Aa:Dn,Mb:!0,ta:!0},Gn={endpoint:"verifyAssertion",oa:An,Ff:Bn,Aa:function(a){if(a.errorMessage&&a.errorMessage=="USER_NOT_FOUND")throw new P("user-not-found");if(a.errorMessage)throw Cn(a.errorMessage);kn(a)},Mb:!0,ta:!0},Fn={endpoint:"verifyAssertion",oa:function(a){An(a);if(!a.idToken)throw new P("internal-error");},Ff:Bn,Aa:Dn,Mb:!0},Rn={endpoint:"verifyCustomToken",oa:function(a){if(!a.token)throw new P("invalid-custom-token");},Aa:kn,
Mb:!0,ta:!0},sm={endpoint:"verifyPassword",oa:function(a){dn(a);if(!a.password)throw new P("wrong-password");},Aa:kn,Mb:!0,ta:!0},un={endpoint:"verifyPhoneNumber",oa:ln,Aa:kn,ta:!0},Am={endpoint:"verifyPhoneNumber",oa:function(a){if(!a.idToken)throw new P("internal-error");ln(a)},Aa:function(a){if(a.temporaryProof)throw a.code="credential-already-in-use",Lm(a);kn(a)}},Bm={Zh:{USER_NOT_FOUND:"user-not-found"},endpoint:"verifyPhoneNumber",oa:ln,Aa:kn,ta:!0},Sn={endpoint:"accounts/mfaEnrollment:withdraw",
Ma:["idToken","mfaEnrollmentId"],Aa:function(a){if(!!a.idToken^!!a.refreshToken)throw new P("internal-error");},ta:!0,Fe:!0};var Tn=pa(["https://apis.google.com/js/api.js?onload=",""]),Vn=function(a){this.hb=a;this.jf=null;this.Tg=Un(this)};Vn.prototype.onReady=function(){return this.Tg};
var Un=function(a){return Wn().then(function(){return new F(function(b,c){N("gapi.iframes.getContext")().open({where:document.body,url:a.hb,messageHandlersFilter:N("gapi.iframes.CROSS_ORIGIN_IFRAMES_FILTER"),attributes:{style:{position:"absolute",top:"-100px",width:"1px",height:"1px"}},dontclear:!0},function(d){a.jf=d;a.jf.restyle({setHideOnLeave:!1});var e=setTimeout(function(){c(Error("Network Error"))},Xn.get()),f=function(){clearTimeout(e);b()};d.ping(f).then(f,function(){c(Error("Network Error"))})})})})};
Vn.prototype.sendMessage=function(a){var b=this;return this.Tg.then(function(){return new F(function(c){b.jf.send(a.type,a,c,N("gapi.iframes.CROSS_ORIGIN_IFRAMES_FILTER"))})})};Vn.prototype.kh=function(a,b){var c=this;this.Tg.then(function(){c.jf.register(a,b,N("gapi.iframes.CROSS_ORIGIN_IFRAMES_FILTER"))})};
var Wn=function(){return Yn?Yn:Yn=(new F(function(a,b){var c=function(){il();N("gapi.load")("gapi.iframes",{callback:a,ontimeout:function(){il();b(Error("Network Error"))},timeout:Zn.get()})};if(N("gapi.iframes.Iframe"))a();else if(N("gapi.load"))c();else{var d="__iframefcb"+Math.floor(Math.random()*1E6).toString();r[d]=function(){N("gapi.load")?c():b(Error("Network Error"))};d=lb(Tn,d);G(Lj(d)).l(function(){b(Error("Network Error"))})}})).l(function(a){Yn=null;throw a;})},Zn=new jl(3E4,6E4),Xn=new jl(5E3,
15E3),Yn=null;var $n=function(a,b,c,d){this.ya=a;this.ha=b;this.ka=c;this.N=d;this.Wc=null;this.N?(a=M(this.N.url),a=rk(a.Va,a.Ga,a.Tb,"/emulator/auth/iframe")):a=rk("https",this.ya,null,"/__/auth/iframe");this.hc=a;L(this.hc,"apiKey",this.ha);L(this.hc,"appName",this.ka);this.Ia=null;this.Da=[]};$n.prototype.zh=function(a){this.Wc=a;return this};$n.prototype.wh=function(a){this.Ia=a;return this};
$n.prototype.toString=function(){this.Wc?L(this.hc,"v",this.Wc):this.hc.removeParameter("v");this.Ia?L(this.hc,"eid",this.Ia):this.hc.removeParameter("eid");this.Da.length?L(this.hc,"fw",this.Da.join(",")):this.hc.removeParameter("fw");return this.hc.toString()};var ao=function(a,b,c,d,e,f){this.ya=a;this.ha=b;this.ka=c;this.hk=d;this.N=f;this.Wc=this.Ja=this.jh=null;this.Bd=e;this.ma=this.Ia=null};ao.prototype.yh=function(a){this.ma=a;return this};ao.prototype.zh=function(a){this.Wc=a;return this};
ao.prototype.wh=function(a){this.Ia=a;return this};
ao.prototype.toString=function(){if(this.N){var a=M(this.N.url);a=rk(a.Va,a.Ga,a.Tb,"/emulator/auth/handler")}else a=rk("https",this.ya,null,"/__/auth/handler");L(a,"apiKey",this.ha);L(a,"appName",this.ka);L(a,"authType",this.hk);if(this.Bd.isOAuthProvider){var b=this.Bd;try{var c=firebase.app(this.ka).auth().Gb}catch(h){c=null}b.qg=c;L(a,"providerId",this.Bd.providerId);c=this.Bd;b=fl(c.ai);for(var d in b)b[d]=b[d].toString();d=c.Kl;b=Ib(b);for(var e=0;e<d.length;e++){var f=d[e];f in b&&delete b[f]}c.Ng&&
c.qg&&!b[c.Ng]&&(b[c.Ng]=c.qg);Hb(b)||L(a,"customParameters",el(b))}typeof this.Bd.ui==="function"&&(c=this.Bd.ui(),c.length&&L(a,"scopes",c.join(",")));this.jh?L(a,"redirectUrl",this.jh):a.removeParameter("redirectUrl");this.Ja?L(a,"eventId",this.Ja):a.removeParameter("eventId");this.Wc?L(a,"v",this.Wc):a.removeParameter("v");if(this.Ie)for(var g in this.Ie)this.Ie.hasOwnProperty(g)&&!qk(a,g)&&L(a,g,this.Ie[g]);this.ma?L(a,"tid",this.ma):a.removeParameter("tid");this.Ia?L(a,"eid",this.Ia):a.removeParameter("eid");
g=bo(this.ka);g.length&&L(a,"fw",g.join(","));return a.toString()};var bo=function(a){try{return Db(firebase.app(a).auth().Da)}catch(b){return[]}},co=function(a,b,c,d,e,f){this.ya=a;this.ha=b;this.ka=c;this.N=f;this.Ec=d||null;this.Ia=e||null;this.o=this.Gg=this.ni=null;this.Vb=[];this.lf=this.Hb=null},eo=function(a){var b=b||Dk();return jn(a).then(function(c){if(!Nk(c,b))throw new Ql(Dk());})};k=co.prototype;
k.initialize=function(){if(this.lf)return this.lf;var a=this;return this.lf=Pk().then(function(){if(!a.Gg){var b=a.ya,c=a.ha,d=a.ka,e=a.Ec,f=a.Ia,g=bo(a.ka);b=(new $n(b,c,d,a.N)).zh(e).wh(f);b.Da=Db(g||[]);a.Gg=b.toString()}a.hf=new Vn(a.Gg);a.lh()})};k.ye=function(a,b,c){var d=new P("popup-closed-by-user"),e=new P("web-storage-unsupported"),f=this,g=!1;return this.Mc().then(function(){fo(f).then(function(h){h||(a&&Jk(a),b(e),g=!0)})}).l(function(){}).then(function(){if(!g)return Lk(a)}).then(function(){if(!g)return Yj(c).then(function(){b(d)})})};
k.Hj=function(){var a=Bk();return!dl(a)&&!hl(a)};k.zi=function(){return!1};
k.ne=function(a,b,c,d,e,f,g,h){if(!a)return H(new P("popup-blocked"));if(g&&!dl())return this.Mc().l(function(m){Jk(a);e(m)}),d(),G();this.Hb||(this.Hb=eo(go(this)));var l=this;return this.Hb.then(function(){var m=l.Mc().l(function(q){Jk(a);e(q);throw q;});d();return m}).then(function(){Jm(c);if(!g){var m=ho(l.ya,l.ha,l.ka,b,c,null,f,l.Ec,void 0,l.Ia,h,l.N);Ek(m,a)}}).l(function(m){m.code=="auth/network-request-failed"&&(l.Hb=null);throw m;})};
var go=function(a){a.o||(a.ni=a.Ec?Zk("JsCore",a.Ec,bo(a.ka)):null,a.o=new Sm(a.ha,zk(a.Ia),a.ni),a.N&&Wm(a.o,a.N));return a.o};co.prototype.oe=function(a,b,c,d){this.Hb||(this.Hb=eo(go(this)));var e=this;return this.Hb.then(function(){Jm(b);var f=ho(e.ya,e.ha,e.ka,a,b,Dk(),c,e.Ec,void 0,e.Ia,d,e.N);Ek(f)}).l(function(f){f.code=="auth/network-request-failed"&&(e.Hb=null);throw f;})};
co.prototype.Mc=function(){var a=this;return this.initialize().then(function(){return a.hf.onReady()}).l(function(){a.Hb=null;throw new P("network-request-failed");})};co.prototype.Mj=function(){return!0};var ho=function(a,b,c,d,e,f,g,h,l,m,q,x){a=new ao(a,b,c,d,e,x);a.jh=f;a.Ja=g;f=a.zh(h);f.Ie=Ib(l||null);return f.wh(m).yh(q).toString()};
co.prototype.lh=function(){if(!this.hf)throw Error("IfcHandler must be initialized!");var a=this;this.hf.kh("authEvent",function(b){var c={};if(b&&b.authEvent){var d=!1;b=Gl(b.authEvent);for(c=0;c<a.Vb.length;c++)d=a.Vb[c](b)||d;c={};c.status=d?"ACK":"ERROR";return G(c)}c.status="ERROR";return G(c)})};
var fo=function(a){var b={type:"webStorageSupport"};return a.initialize().then(function(){return a.hf.sendMessage(b)}).then(function(c){if(c&&c.length&&typeof c[0].webStorageSupport!=="undefined")return c[0].webStorageSupport;throw Error();})};co.prototype.Cc=function(a){this.Vb.push(a)};co.prototype.Ed=function(a){Cb(this.Vb,function(b){return b==a})};function io(){}io.prototype.render=function(){};io.prototype.reset=function(){};io.prototype.getResponse=function(){};io.prototype.execute=function(){};var jo=pa(["https://www.google.com/recaptcha/api.js?trustedtypes=true&onload=","&render=explicit&hl=",""]),ko=function(){this.hd=r.grecaptcha?Infinity:0;this.Bi=null;this.kg="__rcb"+Math.floor(Math.random()*1E6).toString()};
ko.prototype.Oi=function(a){var b=this;return new F(function(c,d){var e=setTimeout(function(){d(new P("network-request-failed"))},lo.get());if(!r.grecaptcha||a!==b.Bi&&!b.hd){r[b.kg]=function(){if(r.grecaptcha){b.Bi=a;var g=r.grecaptcha.render;r.grecaptcha.render=function(h,l){h=g(h,l);b.hd++;return h};clearTimeout(e);c(r.grecaptcha)}else clearTimeout(e),d(new P("internal-error"));delete r[b.kg]};var f=lb(jo,b.kg,a||"");G(Lj(f)).l(function(){clearTimeout(e);d(new P("internal-error","Unable to load external reCAPTCHA dependencies!"))})}else clearTimeout(e),
c(r.grecaptcha)})};ko.prototype.Th=function(){this.hd--};var lo=new jl(3E4,6E4),mo=null;var no=function(){this.ab={};this.hd=1E12};no.prototype.render=function(a,b){this.ab[this.hd.toString()]=new oo(a,b);return this.hd++};no.prototype.reset=function(a){var b=po(this,a);a=qo(a);b&&a&&(b.delete(),delete this.ab[a])};no.prototype.getResponse=function(a){return(a=po(this,a))?a.getResponse():null};no.prototype.execute=function(a){(a=po(this,a))&&a.execute()};
var po=function(a,b){return(b=qo(b))?a.ab[b]||null:null},qo=function(a){return(a=typeof a==="undefined"?1E12:a)?a.toString():null},ro=null,oo=function(a,b){this.wb=!1;this.wa=b;this.Jd=this.Gf=null;this.Ki=this.wa.size!=="invisible";this.fi=Pc(a);var c=this;this.Wi=function(){c.execute()};this.Ki?this.execute():Vi(this.fi,"click",this.Wi)};oo.prototype.getResponse=function(){so(this);return this.Gf};
oo.prototype.execute=function(){so(this);var a=this;this.Jd||(this.Jd=setTimeout(function(){a.Gf=Xk();var b=a.wa.callback,c=a.wa["expired-callback"];if(b)try{b(a.Gf)}catch(d){}a.Jd=setTimeout(function(){a.Jd=null;a.Gf=null;if(c)try{c()}catch(d){}a.Ki&&a.execute()},6E4)},500))};oo.prototype.delete=function(){so(this);this.wb=!0;clearTimeout(this.Jd);this.Jd=null;cj(this.fi,"click",this.Wi)};var so=function(a){if(a.wb)throw Error("reCAPTCHA mock was already deleted!");};var to=function(){};to.prototype.Oi=function(){ro||(ro=new no);return G(ro)};to.prototype.Th=function(){};var uo=null;var vo=function(a,b,c,d,e,f,g){O(this,"type","recaptcha");this.Nd=this.Qd=null;this.jd=!1;this.Vh=b;this.Zd=null;g?(uo||(uo=new to),g=uo):(mo||(mo=new ko),g=mo);this.rj=g;this.tc=c||{theme:"light",type:"image"};this.Ka=[];if(this.tc.sitekey)throw new P("argument-error","sitekey should not be provided for reCAPTCHA as one is automatically provisioned for the current project.");this.mf=this.tc.size==="invisible";if(!r.document)throw new P("operation-not-supported-in-this-environment","RecaptchaVerifier is only supported in a browser HTTP/HTTPS environment with DOM support.");
if(!Pc(b)||!this.mf&&Pc(b).hasChildNodes())throw new P("argument-error","reCAPTCHA container is either not found or already contains inner elements!");this.o=new Sm(a,f||null,e||null);this.Hk=d||function(){return null};var h=this;this.Rf=[];var l=this.tc.callback;this.tc.callback=function(q){h.Td(q);if(typeof l==="function")l(q);else if(typeof l==="string"){var x=N(l,r);typeof x==="function"&&x(q)}};var m=this.tc["expired-callback"];this.tc["expired-callback"]=function(){h.Td(null);if(typeof m===
"function")m();else if(typeof m==="string"){var q=N(m,r);typeof q==="function"&&q()}}};vo.prototype.Td=function(a){for(var b=0;b<this.Rf.length;b++)try{this.Rf[b](a)}catch(c){}};var wo=function(a,b){Cb(a.Rf,function(c){return c==b})};k=vo.prototype;k.u=function(a){var b=this;this.Ka.push(a);a.Ac(function(){Ab(b.Ka,a)});return a};
k.de=function(){var a=this;return this.Qd?this.Qd:this.Qd=this.u(G().then(function(){if(bl()&&!Uk())return Pk();throw new P("operation-not-supported-in-this-environment","RecaptchaVerifier is only supported in a browser HTTP/HTTPS environment.");}).then(function(){return a.rj.Oi(a.Hk())}).then(function(b){a.Zd=b;return Q(a.o,Qn,{})}).then(function(b){a.tc.sitekey=b.recaptchaSiteKey}).l(function(b){a.Qd=null;throw b;}))};
k.render=function(){xo(this);var a=this;return this.u(this.de().then(function(){if(a.Nd===null){var b=a.Vh;if(!a.mf){var c=Pc(b);b=Tc(document,"DIV");c.appendChild(b)}a.Nd=a.Zd.render(b,a.tc)}return a.Nd}))};k.verify=function(){xo(this);var a=this;return this.u(this.render().then(function(b){return new F(function(c){var d=a.Zd.getResponse(b);if(d)c(d);else{var e=function(f){f&&(wo(a,e),c(f))};a.Rf.push(e);a.mf&&a.Zd.execute(a.Nd)}})}))};k.reset=function(){xo(this);this.Nd!==null&&this.Zd.reset(this.Nd)};
var xo=function(a){if(a.jd)throw new P("internal-error","RecaptchaVerifier instance has been destroyed.");};vo.prototype.clear=function(){xo(this);this.jd=!0;this.rj.Th();for(var a=0;a<this.Ka.length;a++)this.Ka[a].cancel("RecaptchaVerifier instance has been destroyed.");this.mf||Uc(Pc(this.Vh))};
var yo=function(a,b,c){var d=!1;try{this.U=c||firebase.app()}catch(g){throw new P("argument-error","No firebase.app.App instance is currently initialized.");}if(this.U.options&&this.U.options.apiKey)c=this.U.options.apiKey;else throw new P("invalid-api-key");var e=this,f=null;try{f=Db(this.U.auth().Da)}catch(g){}try{d=this.U.auth().settings.appVerificationDisabledForTesting}catch(g){}f=firebase.SDK_VERSION?Zk("JsCore",firebase.SDK_VERSION,f):null;vo.call(this,c,a,b,function(){try{var g=e.U.auth().Gb}catch(h){g=
null}return g},f,zk(Ak),d)};w(yo,vo);var zo=function(a){this.He=a};zo.prototype.postMessage=function(a,b){this.He.postMessage(a,b)};var Ao=function(a){this.Gl=a;this.Uh=!1;this.tf=[]};
Ao.prototype.send=function(a,b,c){b=b===void 0?null:b;c=c===void 0?!1:c;var d=this,e;b=b||{};var f,g,h,l=null;if(this.Uh)return H(Error("connection_unavailable"));var m=c?800:50,q=typeof MessageChannel!=="undefined"?new MessageChannel:null;return(new F(function(x,A){q?(e=""+Math.floor(Math.random()*1E20).toString(),q.port1.start(),g=setTimeout(function(){A(Error("unsupported_event"))},m),f=function(S){S.data.eventId===e&&(S.data.status==="ack"?(clearTimeout(g),h=setTimeout(function(){A(Error("timeout"))},
3E3)):S.data.status==="done"?(clearTimeout(h),typeof S.data.response!=="undefined"?x(S.data.response):A(Error("unknown_error"))):(clearTimeout(g),clearTimeout(h),A(Error("invalid_response"))))},l={messageChannel:q,onMessage:f},d.tf.push(l),q.port1.addEventListener("message",f),d.Gl.postMessage({eventType:a,eventId:e,data:b},[q.port2])):A(Error("connection_unavailable"))})).then(function(x){Bo(d,l);return x}).l(function(x){Bo(d,l);throw x;})};
var Bo=function(a,b){if(b){var c=b.messageChannel,d=b.onMessage;c&&(c.port1.removeEventListener("message",d),c.port1.close());Cb(a.tf,function(e){return e==b})}};Ao.prototype.close=function(){for(;this.tf.length>0;)Bo(this,this.tf[0]);this.Uh=!0};var Co=function(a){this.wg=a;this.Db={};this.Ri=v(this.Lk,this)},Eo=function(a){z(Do,function(c){c.wg==a&&(b=c)});if(!b){var b=new Co(a);Do.push(b)}return b};
Co.prototype.Lk=function(a){var b=a.data.eventType,c=a.data.eventId,d=this.Db[b];if(d&&d.length>0){a.ports[0].postMessage({status:"ack",eventId:c,eventType:b,response:null});var e=[];z(d,function(f){e.push(G().then(function(){return f(a.origin,a.data.data)}))});bg(e).then(function(f){var g=[];z(f,function(h){g.push({fulfilled:h.mi,value:h.value,reason:h.reason?h.reason.message:void 0})});z(g,function(h){for(var l in h)typeof h[l]==="undefined"&&delete h[l]});a.ports[0].postMessage({status:"done",
eventId:c,eventType:b,response:g})})}};Co.prototype.subscribe=function(a,b){Hb(this.Db)&&this.wg.addEventListener("message",this.Ri);typeof this.Db[a]==="undefined"&&(this.Db[a]=[]);this.Db[a].push(b)};Co.prototype.unsubscribe=function(a,b){typeof this.Db[a]!=="undefined"&&b?(Cb(this.Db[a],function(c){return c==b}),this.Db[a].length==0&&delete this.Db[a]):b||delete this.Db[a];Hb(this.Db)&&this.wg.removeEventListener("message",this.Ri)};var Do=[];var Fo=function(a){this.tb=a||firebase.INTERNAL.reactNative&&firebase.INTERNAL.reactNative.AsyncStorage;if(!this.tb)throw new P("internal-error","The React Native compatibility library was not found.");this.type="asyncStorage"};k=Fo.prototype;k.get=function(a){return G(this.tb.getItem(a)).then(function(b){return b&&gl(b)})};k.set=function(a,b){return G(this.tb.setItem(a,el(b)))};k.remove=function(a){return G(this.tb.removeItem(a))};k.mc=function(){};k.zc=function(){};function Go(){this.storage={};this.type="inMemory"}k=Go.prototype;k.get=function(a){return G(this.storage[a])};k.set=function(a,b){this.storage[a]=b;return G()};k.remove=function(a){delete this.storage[a];return G()};k.mc=function(){};k.zc=function(){};var Jo=function(){if(!Ho()){if(Vk()=="Node")throw new P("internal-error","The LocalStorage compatibility library was not found.");throw new P("web-storage-unsupported");}this.tb=Io()||firebase.INTERNAL.node.localStorage;this.type="localStorage"},Io=function(){try{var a=r.localStorage,b=$k();a&&(a.setItem(b,"1"),a.removeItem(b));return a}catch(c){return null}},Ho=function(){var a=Vk()=="Node";a=Io()||a&&firebase.INTERNAL.node&&firebase.INTERNAL.node.localStorage;if(!a)return!1;try{return a.setItem("__sak",
"1"),a.removeItem("__sak"),!0}catch(b){return!1}};k=Jo.prototype;k.get=function(a){var b=this;return G().then(function(){var c=b.tb.getItem(a);return gl(c)})};k.set=function(a,b){var c=this;return G().then(function(){var d=el(b);d===null?c.remove(a):c.tb.setItem(a,d)})};k.remove=function(a){var b=this;return G().then(function(){b.tb.removeItem(a)})};k.mc=function(a){r.window&&Vi(r.window,"storage",a)};k.zc=function(a){r.window&&cj(r.window,"storage",a)};var Ko=function(){this.tb={};this.type="nullStorage"};k=Ko.prototype;k.get=function(){return G(null)};k.set=function(){return G()};k.remove=function(){return G()};k.mc=function(){};k.zc=function(){};var No=function(){if(!Lo()){if(Vk()=="Node")throw new P("internal-error","The SessionStorage compatibility library was not found.");throw new P("web-storage-unsupported");}this.tb=Mo()||firebase.INTERNAL.node.sessionStorage;this.type="sessionStorage"},Mo=function(){try{var a=r.sessionStorage,b=$k();a&&(a.setItem(b,"1"),a.removeItem(b));return a}catch(c){return null}},Lo=function(){var a=Vk()=="Node";a=Mo()||a&&firebase.INTERNAL.node&&firebase.INTERNAL.node.sessionStorage;if(!a)return!1;try{return a.setItem("__sak",
"1"),a.removeItem("__sak"),!0}catch(b){return!1}};k=No.prototype;k.get=function(a){var b=this;return G().then(function(){var c=b.tb.getItem(a);return gl(c)})};k.set=function(a,b){var c=this;return G().then(function(){var d=el(b);d===null?c.remove(a):c.tb.setItem(a,d)})};k.remove=function(a){var b=this;return G().then(function(){b.tb.removeItem(a)})};k.mc=function(){};k.zc=function(){};var Qo=function(){if(!Oo())throw new P("web-storage-unsupported");this.bi="firebaseLocalStorageDb";this.vf="firebaseLocalStorage";this.og="fbase_key";this.Sj="value";this.nm=1;this.Ra={};this.Ub=[];this.ke=0;this.Fi=r.indexedDB;this.type="indexedDB";this.Lf=this.uc=this.Cf=this.Zg=null;this.zj=!1;this.Vf=null;var a=this;Uk()&&self?(this.uc=Eo(Uk()?self:null),this.uc.subscribe("keyChanged",function(b,c){return Po(a).then(function(d){d.length>0&&z(a.Ub,function(e){e(d)});return{keyProcessed:zb(d,c.key)}})}),
this.uc.subscribe("ping",function(){return G(["keyChanged"])})):ql().then(function(b){if(a.Vf=b)a.Lf=new Ao(new zo(b)),a.Lf.send("ping",null,!0).then(function(c){c[0].fulfilled&&zb(c[0].value,"keyChanged")&&(a.zj=!0)}).l(function(){})})},Ro,So=function(a){return new F(function(b,c){var d=a.Fi.deleteDatabase(a.bi);d.onsuccess=function(){b()};d.onerror=function(e){c(Error(e.target.error))}})},To=function(a){return new F(function(b,c){var d=a.Fi.open(a.bi,a.nm);d.onerror=function(e){try{e.preventDefault()}catch(f){}c(Error(e.target.error))};
d.onupgradeneeded=function(e){e=e.target.result;try{e.createObjectStore(a.vf,{keyPath:a.og})}catch(f){c(f)}};d.onsuccess=function(e){e=e.target.result;e.objectStoreNames.contains(a.vf)?b(e):So(a).then(function(){return To(a)}).then(function(f){b(f)}).l(function(f){c(f)})}})},Uo=function(a){a.Ig||(a.Ig=To(a));return a.Ig},Vo=function(a,b){var c=0,d=function(e,f){Uo(a).then(b).then(e).l(function(g){if(++c>3)f(g);else return Uo(a).then(function(h){h.close();a.Ig=void 0;return d(e,f)}).l(function(h){f(h)})})};
return new F(d)},Oo=function(){try{return!!r.indexedDB}catch(a){return!1}},Wo=function(a,b){return b.objectStore(a.vf)},Xo=function(a,b,c){return b.transaction([a.vf],c?"readwrite":"readonly")},Yo=function(a){return new F(function(b,c){a.onsuccess=function(d){d&&d.target?b(d.target.result):b()};a.onerror=function(d){c(d.target.error)}})};
Qo.prototype.set=function(a,b){var c=this,d=!1;return Vo(this,function(e){e=Wo(c,Xo(c,e,!0));return Yo(e.get(a))}).then(function(e){return Vo(c,function(f){f=Wo(c,Xo(c,f,!0));if(e)return e.value=b,Yo(f.put(e));c.ke++;d=!0;var g={};g[c.og]=a;g[c.Sj]=b;return Yo(f.add(g))})}).then(function(){c.Ra[a]=b;return Zo(c,a)}).Ac(function(){d&&c.ke--})};var Zo=function(a,b){return a.Lf&&a.Vf&&pl()===a.Vf?a.Lf.send("keyChanged",{key:b},a.zj).then(function(){}).l(function(){}):G()};
Qo.prototype.get=function(a){var b=this;return Vo(this,function(c){return Yo(Wo(b,Xo(b,c,!1)).get(a))}).then(function(c){return c&&c.value})};Qo.prototype.remove=function(a){var b=!1,c=this;return Vo(this,function(d){b=!0;c.ke++;return Yo(Wo(c,Xo(c,d,!0))["delete"](a))}).then(function(){delete c.Ra[a];return Zo(c,a)}).Ac(function(){b&&c.ke--})};
var Po=function(a){return Uo(a).then(function(b){var c=Wo(a,Xo(a,b,!1));return c.getAll?Yo(c.getAll()):new F(function(d,e){var f=[],g=c.openCursor();g.onsuccess=function(h){(h=h.target.result)?(f.push(h.value),h["continue"]()):d(f)};g.onerror=function(h){e(h.target.error)}})}).then(function(b){var c={},d=[];if(a.ke==0){for(d=0;d<b.length;d++)c[b[d][a.og]]=b[d][a.Sj];d=Fk(a.Ra,c);a.Ra=c}return d})};Qo.prototype.mc=function(a){this.Ub.length==0&&this.Dh();this.Ub.push(a)};
Qo.prototype.zc=function(a){Cb(this.Ub,function(b){return b==a});this.Ub.length==0&&this.Of()};Qo.prototype.Dh=function(){var a=this;this.Of();var b=function(){a.Cf=setTimeout(function(){a.Zg=Po(a).then(function(c){c.length>0&&z(a.Ub,function(d){d(c)})}).then(function(){b()}).l(function(c){c.message!="STOP_EVENT"&&b()})},800)};b()};Qo.prototype.Of=function(){this.Zg&&this.Zg.cancel("STOP_EVENT");this.Cf&&(clearTimeout(this.Cf),this.Cf=null)};function $o(a){var b=this,c=null;this.Ub=[];this.type="indexedDB";this.hi=a;this.Jh=G().then(function(){if(Oo()){var d=$k(),e="__sak"+d;Ro||(Ro=new Qo);c=Ro;return c.set(e,d).then(function(){return c.get(e)}).then(function(f){if(f!==d)throw Error("indexedDB not supported!");return c.remove(e)}).then(function(){return c}).l(function(){return b.hi})}return b.hi}).then(function(d){b.type=d.type;d.mc(function(e){z(b.Ub,function(f){f(e)})});return d})}k=$o.prototype;k.get=function(a){return this.Jh.then(function(b){return b.get(a)})};
k.set=function(a,b){return this.Jh.then(function(c){return c.set(a,b)})};k.remove=function(a){return this.Jh.then(function(b){return b.remove(a)})};k.mc=function(a){this.Ub.push(a)};k.zc=function(a){Cb(this.Ub,function(b){return b==a})};var ep=function(){this.ug={Browser:ap,Node:bp,ReactNative:cp,Worker:dp}[Vk()]},fp,ap={ua:Jo,Pf:No},bp={ua:Jo,Pf:No},cp={ua:Fo,Pf:Ko},dp={ua:Jo,Pf:Ko};var gp=function(){this.Yf=!1;Object.defineProperty(this,"appVerificationDisabled",{get:function(){return this.Yf},set:function(a){this.Yf=a},enumerable:!1})};var hp=function(a){this.Kg(a)};
hp.prototype.Kg=function(a){var b=a.url;if(typeof b==="undefined")throw new P("missing-continue-uri");if(typeof b!=="string"||typeof b==="string"&&!b.length)throw new P("invalid-continue-uri");this.mk=b;this.Mh=this.Xf=null;this.Hi=!1;var c=a.android;if(c&&typeof c==="object"){b=c.packageName;var d=c.installApp;c=c.minimumVersion;if(typeof b==="string"&&b.length){this.Xf=b;if(typeof d!=="undefined"&&typeof d!=="boolean")throw new P("argument-error","installApp property must be a boolean when specified.");this.Hi=
!!d;if(typeof c!=="undefined"&&(typeof c!=="string"||typeof c==="string"&&!c.length))throw new P("argument-error","minimumVersion property must be a non empty string when specified.");this.Mh=c||null}else{if(typeof b!=="undefined")throw new P("argument-error","packageName property must be a non empty string when specified.");if(typeof d!=="undefined"||typeof c!=="undefined")throw new P("missing-android-pkg-name");}}else if(typeof c!=="undefined")throw new P("argument-error","android property must be a non null object when specified.");
this.Ci=null;if((b=a.iOS)&&typeof b==="object")if(b=b.bundleId,typeof b==="string"&&b.length)this.Ci=b;else{if(typeof b!=="undefined")throw new P("argument-error","bundleId property must be a non empty string when specified.");}else if(typeof b!=="undefined")throw new P("argument-error","iOS property must be a non null object when specified.");b=a.handleCodeInApp;if(typeof b!=="undefined"&&typeof b!=="boolean")throw new P("argument-error","handleCodeInApp property must be a boolean when specified.");
this.Sh=!!b;a=a.dynamicLinkDomain;if(typeof a!=="undefined"&&(typeof a!=="string"||typeof a==="string"&&!a.length))throw new P("argument-error","dynamicLinkDomain property must be a non empty string when specified.");this.vk=a||null};var ip=function(a){var b={};b.continueUrl=a.mk;b.canHandleCodeInApp=a.Sh;if(b.androidPackageName=a.Xf)b.androidMinimumVersion=a.Mh,b.androidInstallApp=a.Hi;b.iOSBundleId=a.Ci;b.dynamicLinkDomain=a.vk;for(var c in b)b[c]===null&&delete b[c];return b};var jp=function(a,b){this.rk=b;O(this,"verificationId",a)};jp.prototype.confirm=function(a){a=Hm(this.verificationId,a);return this.rk(a)};var kp=function(a,b,c,d){return(new Dm(a)).verifyPhoneNumber(b,c).then(function(e){return new jp(e,d)})};var lp=function(a,b,c){this.Dl=a;this.Ul=b;this.Jk=c;this.rf=3E4;this.Lh=96E4;this.Vl=!1;this.xd=null;this.Oc=this.rf;if(this.Lh<this.rf)throw Error("Proactive refresh lower bound greater than upper bound!");};lp.prototype.start=function(){this.Oc=this.rf;mp(this,!0)};
var np=function(a,b){if(b)return a.Oc=a.rf,a.Jk();b=a.Oc;a.Oc*=2;a.Oc>a.Lh&&(a.Oc=a.Lh);return b},mp=function(a,b){a.stop();a.xd=Yj(np(a,b)).then(function(){return a.Vl?G():ll()}).then(function(){return a.Dl()}).then(function(){mp(a,!0)}).l(function(c){a.Ul(c)&&mp(a,!1)})};lp.prototype.stop=function(){this.xd&&(this.xd.cancel(),this.xd=null)};var up=function(a){var b={};b["facebook.com"]=op;b["google.com"]=pp;b["github.com"]=qp;b["twitter.com"]=rp;var c=a&&a.providerId;try{if(c)return b[c]?new b[c](a):new sp(a);if(typeof a.idToken!=="undefined")return new tp(a)}catch(d){}return null},tp=function(a){var b=a.providerId;if(!b&&a.idToken){var c=Tl(a.idToken);c&&c.hh&&(b=c.hh)}if(!b)throw Error("Invalid additional user info!");if(b=="anonymous"||b=="custom")b=null;c=!1;typeof a.isNewUser!=="undefined"?c=!!a.isNewUser:a.kind==="identitytoolkit#SignupNewUserResponse"&&
(c=!0);O(this,"providerId",b);O(this,"isNewUser",c)},sp=function(a){tp.call(this,a);a=gl(a.rawUserInfo||"{}");O(this,"profile",yl(a||{}))};p(sp,tp);var op=function(a){sp.call(this,a);if(this.providerId!="facebook.com")throw Error("Invalid provider ID!");};p(op,sp);var qp=function(a){sp.call(this,a);if(this.providerId!="github.com")throw Error("Invalid provider ID!");O(this,"username",this.profile&&this.profile.login||null)};p(qp,sp);
var pp=function(a){sp.call(this,a);if(this.providerId!="google.com")throw Error("Invalid provider ID!");};p(pp,sp);var rp=function(a){sp.call(this,a);if(this.providerId!="twitter.com")throw Error("Invalid provider ID!");O(this,"username",a.screenName||null)};p(rp,sp);var vp={LOCAL:"local",NONE:"none",SESSION:"session"};function wp(a){var b=new P("invalid-persistence-type"),c=new P("unsupported-persistence-type");a:{for(d in vp)if(vp[d]==a){var d=!0;break a}d=!1}if(!d||typeof a!=="string")throw b;switch(Vk()){case "ReactNative":if(a==="session")throw c;break;case "Node":if(a!=="none")throw c;break;case "Worker":if(a==="session"||!Oo()&&a!=="none")throw c;break;default:if(!al()&&a!=="none")throw c;}}
var xp=function(){var a=!hl(Bk())&&Tk()?!0:!1,b=dl(),c=al();this.Ui="firebase";this.th=":";this.Wl=a;this.vj=b;this.Tj=c;this.Za={};fp||(fp=new ep);a=fp;try{this.jj=!Ck()&&ol()||!r.indexedDB?new a.ug.ua:new $o(Uk()?new Go:new a.ug.ua)}catch(d){this.jj=new Go,this.vj=!0}try{this.Lj=new a.ug.Pf}catch(d){this.Lj=new Go}this.Zk=new Go;this.Fh=v(this.Jj,this);this.Ra={}},yp,zp=function(){yp||(yp=new xp);return yp},Ap=function(a,b){switch(b){case "session":return a.Lj;case "none":return a.Zk;default:return a.jj}};
xp.prototype.nb=function(a,b){return this.Ui+this.th+a.name+(b?this.th+b:"")};var Bp=function(a,b,c){var d=a.nb(b,c),e=Ap(a,b.ua);return a.get(b,c).then(function(f){var g=null;try{g=gl(r.localStorage.getItem(d))}catch(h){}if(g&&!f)return r.localStorage.removeItem(d),a.set(b,g,c);g&&f&&e.type!="localStorage"&&r.localStorage.removeItem(d)})};k=xp.prototype;k.get=function(a,b){return Ap(this,a.ua).get(this.nb(a,b))};
k.remove=function(a,b){b=this.nb(a,b);a.ua=="local"&&(this.Ra[b]=null);return Ap(this,a.ua).remove(b)};k.set=function(a,b,c){var d=this.nb(a,c),e=this,f=Ap(this,a.ua);return f.set(d,b).then(function(){return f.get(d)}).then(function(g){a.ua=="local"&&(e.Ra[d]=g)})};k.addListener=function(a,b,c){a=this.nb(a,b);this.Tj&&(this.Ra[a]=r.localStorage.getItem(a));Hb(this.Za)&&this.Dh();this.Za[a]||(this.Za[a]=[]);this.Za[a].push(c)};
k.removeListener=function(a,b,c){a=this.nb(a,b);this.Za[a]&&(Cb(this.Za[a],function(d){return d==c}),this.Za[a].length==0&&delete this.Za[a]);Hb(this.Za)&&this.Of()};k.Dh=function(){Ap(this,"local").mc(this.Fh);this.vj||(Ck()||!ol())&&r.indexedDB||!this.Tj||Cp(this)};
var Cp=function(a){Dp(a);a.Pg=setInterval(function(){for(var b in a.Za){var c=r.localStorage.getItem(b),d=a.Ra[b];c!=d&&(a.Ra[b]=c,c=new Ji({type:"storage",key:b,target:window,oldValue:d,newValue:c,Yg:!0}),a.Jj(c))}},1E3)},Dp=function(a){a.Pg&&(clearInterval(a.Pg),a.Pg=null)};xp.prototype.Of=function(){Ap(this,"local").zc(this.Fh);Dp(this)};
xp.prototype.Jj=function(a){if(a&&a.Fk){var b=a.Ya.key;if(b==null)for(var c in this.Za){var d=this.Ra[c];typeof d==="undefined"&&(d=null);var e=r.localStorage.getItem(c);e!==d&&(this.Ra[c]=e,this.jg(c))}else if(b.indexOf(this.Ui+this.th)==0&&this.Za[b]){typeof a.Ya.Yg!=="undefined"?Ap(this,"local").zc(this.Fh):Dp(this);if(this.Wl)if(c=r.localStorage.getItem(b),d=a.Ya.newValue,d!==c)d!==null?r.localStorage.setItem(b,d):r.localStorage.removeItem(b);else if(this.Ra[b]===d&&typeof a.Ya.Yg==="undefined")return;
var f=this;c=function(){if(typeof a.Ya.Yg!=="undefined"||f.Ra[b]!==r.localStorage.getItem(b))f.Ra[b]=r.localStorage.getItem(b),f.jg(b)};Bc&&Oc&&Oc==10&&r.localStorage.getItem(b)!==a.Ya.newValue&&a.Ya.newValue!==a.Ya.oldValue?setTimeout(c,10):c()}}else z(a,v(this.jg,this))};xp.prototype.jg=function(a){this.Za[a]&&z(this.Za[a],function(b){b()})};var Ep=function(a){this.V=a;this.O=zp()},Gp=function(a){return a.O.get(Fp,a.V).then(function(b){return Gl(b)})},Hp=function(a){return a.O.remove(Fp,a.V)};Ep.prototype.Cc=function(a){this.O.addListener(Fp,this.V,a)};Ep.prototype.Ed=function(a){this.O.removeListener(Fp,this.V,a)};var Jp=function(a){return a.O.get(Ip,a.V).then(function(b){return Gl(b)})},Fp={name:"authEvent",ua:"local"},Ip={name:"redirectEvent",ua:"session"};var Kp=function(){this.O=zp()};Kp.prototype.Yd=function(){return this.O.get(Lp,void 0)};var Lp={name:"sessionId",ua:"session"};var Mp=function(){this.Qg=null;this.Oe=[]};Mp.prototype.subscribe=function(a){var b=this;this.Oe.push(a);this.Qg||(this.Qg=function(c){for(var d=0;d<b.Oe.length;d++)b.Oe[d](c)},a=N("universalLinks.subscribe",r),typeof a==="function"&&a(null,this.Qg))};Mp.prototype.unsubscribe=function(a){Cb(this.Oe,function(b){return b==a})};var Np=null;var Op=function(a,b,c,d,e,f){this.ya=a;this.ha=b;this.ka=c;this.N=f;this.Ec=d||null;this.Ia=e||null;this.Kj=b+":"+c;this.Xl=new Kp;this.oi=new Ep(this.Kj);this.Jg=null;this.Vb=[];this.dl=500;this.Il=2E3;this.be=this.Bf=null},Pp=function(a){return new P("invalid-cordova-configuration",a)};
Op.prototype.Mc=function(){return this.de?this.de:this.de=Rk().then(function(){if(typeof N("universalLinks.subscribe",r)!=="function")throw Pp("cordova-universal-links-plugin-fix is not installed");if(typeof N("BuildInfo.packageName",r)==="undefined")throw Pp("cordova-plugin-buildinfo is not installed");if(typeof N("cordova.plugins.browsertab.openUrl",r)!=="function")throw Pp("cordova-plugin-browsertab is not installed");if(typeof N("cordova.InAppBrowser.open",r)!=="function")throw Pp("cordova-plugin-inappbrowser is not installed");
},function(){throw new P("cordova-not-ready");})};var Qp=function(){for(var a=20,b=[];a>0;)b.push("1234567890abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ".charAt(Math.floor(Math.random()*62))),a--;return b.join("")},Rp=function(a){var b=new Fi;b.update(a);return ti(b.digest())};k=Op.prototype;k.ye=function(a,b){b(new P("operation-not-supported-in-this-environment"));return G()};k.ne=function(){return H(new P("operation-not-supported-in-this-environment"))};k.Mj=function(){return!1};k.Hj=function(){return!0};
k.zi=function(){return!0};
k.oe=function(a,b,c,d){if(this.Bf)return H(new P("redirect-operation-pending"));var e=this,f=r.document,g=null,h=null,l=null,m=null;return this.Bf=G().then(function(){Jm(b);return Sp(e)}).then(function(){return Tp(e,a,b,c,d)}).then(function(){return(new F(function(q,x){h=function(){var A=N("cordova.plugins.browsertab.close",r);q();typeof A==="function"&&A();e.be&&typeof e.be.close==="function"&&(e.be.close(),e.be=null);return!1};e.Cc(h);l=function(){g||(g=Yj(e.Il).then(function(){x(new P("redirect-cancelled-by-user"))}))};m=
function(){kl()&&l()};f.addEventListener("resume",l,!1);Bk().toLowerCase().match(/android/)||f.addEventListener("visibilitychange",m,!1)})).l(function(q){return Up(e).then(function(){throw q;})})}).Ac(function(){l&&f.removeEventListener("resume",l,!1);m&&f.removeEventListener("visibilitychange",m,!1);g&&g.cancel();h&&e.Ed(h);e.Bf=null})};
var Tp=function(a,b,c,d,e){var f=Qp(),g=new Fl(b,d,null,f,new P("no-auth-event"),null,e),h=N("BuildInfo.packageName",r);if(typeof h!=="string")throw new P("invalid-cordova-configuration");var l=N("BuildInfo.displayName",r),m={};if(Bk().toLowerCase().match(/iphone|ipad|ipod/))m.ibi=h;else if(Bk().toLowerCase().match(/android/))m.apn=h;else return H(new P("operation-not-supported-in-this-environment"));l&&(m.appDisplayName=l);f=Rp(f);m.sessionId=f;var q=ho(a.ya,a.ha,a.ka,b,c,null,d,a.Ec,m,a.Ia,e,a.N);
return a.Mc().then(function(){var x=a.Kj;return a.Xl.O.set(Fp,g.T(),x)}).then(function(){var x=N("cordova.plugins.browsertab.isAvailable",r);if(typeof x!=="function")throw new P("invalid-cordova-configuration");var A=null;x(function(S){if(S){A=N("cordova.plugins.browsertab.openUrl",r);if(typeof A!=="function")throw new P("invalid-cordova-configuration");A(q)}else{A=N("cordova.InAppBrowser.open",r);if(typeof A!=="function")throw new P("invalid-cordova-configuration");S=A;var Aa=Bk();Aa=!(!Aa.match(/(iPad|iPhone|iPod).*OS 7_\d/i)&&
!Aa.match(/(iPad|iPhone|iPod).*OS 8_\d/i));a.be=S(q,Aa?"_blank":"_system","location=yes")}})})};Op.prototype.Td=function(a){for(var b=0;b<this.Vb.length;b++)try{this.Vb[b](a)}catch(c){}};
var Sp=function(a){a.Jg||(a.Jg=a.Mc().then(function(){return new F(function(b){var c=function(d){b(d);a.Ed(c);return!1};a.Cc(c);Vp(a)})}));return a.Jg},Up=function(a){var b=null;return Gp(a.oi).then(function(c){b=c;return Hp(a.oi)}).then(function(){return b})},Vp=function(a){var b=new Fl("unknown",null,null,null,new P("no-auth-event")),c=!1,d=Yj(a.dl).then(function(){return Up(a).then(function(){c||a.Td(b)})}),e=function(g){c=!0;d&&d.cancel();Up(a).then(function(h){var l=b;if(h&&g&&g.url){var m=null;
l=Pl(g.url);l.indexOf("/__/auth/callback")!=-1&&(m=M(l),m=gl(qk(m,"firebaseError")||null),m=(m=typeof m==="object"?El(m):null)?new Fl(h.getType(),h.Ja,null,null,m,null,h.ma):new Fl(h.getType(),h.Ja,l,h.Yd(),null,null,h.ma));l=m||b}a.Td(l)})},f=r.handleOpenURL;r.handleOpenURL=function(g){g.toLowerCase().indexOf(N("BuildInfo.packageName",r).toLowerCase()+"://")==0&&e({url:g});if(typeof f==="function")try{f(g)}catch(h){console.error(h)}};Np||(Np=new Mp);Np.subscribe(e)};
Op.prototype.Cc=function(a){this.Vb.push(a);Sp(this).l(function(b){b.code==="auth/invalid-cordova-configuration"&&(b=new Fl("unknown",null,null,null,new P("no-auth-event")),a(b))})};Op.prototype.Ed=function(a){Cb(this.Vb,function(b){return b==a})};var Wp=function(a){this.V=a;this.O=zp()},Yp=function(a){return a.O.set(Xp,"pending",a.V)},Zp=function(a){return a.O.remove(Xp,a.V)},$p=function(a){return a.O.get(Xp,a.V).then(function(b){return b=="pending"})},Xp={name:"pendingRedirect",ua:"session"};var eq=function(a,b,c,d){this.Df={};this.Og=0;this.ya=a;this.ha=b;this.ka=c;this.N=d;this.Ae=[];this.td=!1;this.dg=v(this.Dg,this);this.vc=new aq(this);this.ah=new bq(this);this.le=new Wp(cq(this.ha,this.ka));this.Bc={};this.Bc.unknown=this.vc;this.Bc.signInViaRedirect=this.vc;this.Bc.linkViaRedirect=this.vc;this.Bc.reauthViaRedirect=this.vc;this.Bc.signInViaPopup=this.ah;this.Bc.linkViaPopup=this.ah;this.Bc.reauthViaPopup=this.ah;this.qb=dq(this.ya,this.ha,this.ka,Ak,this.N)},dq=function(a,b,c,d,
e){var f=firebase.SDK_VERSION||null;return Qk()?new Op(a,b,c,f,d,e):new co(a,b,c,f,d,e)};eq.prototype.reset=function(){this.td=!1;this.qb.Ed(this.dg);this.qb=dq(this.ya,this.ha,this.ka,null,this.N);this.Df={}};eq.prototype.ad=function(){this.vc.ad()};eq.prototype.initialize=function(){var a=this;this.td||(this.td=!0,this.qb.Cc(this.dg));var b=this.qb;return this.qb.Mc().l(function(c){a.qb==b&&a.reset();throw c;})};
var hq=function(a){a.qb.Hj()&&a.initialize().l(function(b){var c=new Fl("unknown",null,null,null,new P("operation-not-supported-in-this-environment"));fq(b)&&a.Dg(c)});a.qb.zi()||gq(a.vc)};k=eq.prototype;k.subscribe=function(a){zb(this.Ae,a)||this.Ae.push(a);if(!this.td){var b=this;$p(this.le).then(function(c){c?Zp(b.le).then(function(){b.initialize().l(function(d){var e=new Fl("unknown",null,null,null,new P("operation-not-supported-in-this-environment"));fq(d)&&b.Dg(e)})}):hq(b)}).l(function(){hq(b)})}};
k.unsubscribe=function(a){Cb(this.Ae,function(b){return b==a})};k.Dg=function(a){if(!a)throw new P("invalid-auth-event");Date.now()-this.Og>=6E5&&(this.Df={},this.Og=0);if(a&&a.getUid()&&this.Df.hasOwnProperty(a.getUid()))return!1;for(var b=!1,c=0;c<this.Ae.length;c++){var d=this.Ae[c];if(d.Rh(a.getType(),a.Ja)){if(b=this.Bc[a.getType()])b.oj(a,d),a&&(a.Yd()||a.Ja)&&(this.Df[a.getUid()]=!0,this.Og=Date.now());b=!0;break}}gq(this.vc);return b};k.getRedirectResult=function(){return this.vc.getRedirectResult()};
k.ne=function(a,b,c,d,e,f){var g=this;return this.qb.ne(a,b,c,function(){g.td||(g.td=!0,g.qb.Cc(g.dg))},function(){g.reset()},d,e,f)};var fq=function(a){return a&&a.code=="auth/cordova-not-ready"?!0:!1};eq.prototype.oe=function(a,b,c,d){var e=this,f;return Yp(this.le).then(function(){return e.qb.oe(a,b,c,d).l(function(g){if(fq(g))throw new P("operation-not-supported-in-this-environment");f=g;return Zp(e.le).then(function(){throw f;})}).then(function(){return e.qb.Mj()?new F(function(){}):Zp(e.le).then(function(){return e.getRedirectResult()}).then(function(){}).l(function(){})})})};
eq.prototype.ye=function(a,b,c,d){return this.qb.ye(c,function(e){a.Sc(b,null,e,d)},iq.get())};var cq=function(a,b,c){a=a+":"+b;c&&(a=a+":"+c.url);return a},kq=function(a,b,c,d){var e=cq(b,c,d);jq[e]||(jq[e]=new eq(a,b,c,d));return jq[e]},iq=new jl(2E3,1E4),lq=new jl(3E4,6E4),jq={},aq=function(a){this.O=a;this.Dd=null;this.te=[];this.re=[];this.Cd=null;this.Nj=this.ue=!1};aq.prototype.reset=function(){this.Dd=null;this.Cd&&(this.Cd.cancel(),this.Cd=null)};
aq.prototype.oj=function(a,b){if(a){this.reset();this.ue=!0;var c=a.getType(),d=a.Ja,e=a.getError()&&a.getError().code=="auth/web-storage-unsupported",f=a.getError()&&a.getError().code=="auth/operation-not-supported-in-this-environment";this.Nj=!(!e&&!f);c!="unknown"||e||f?a.yb?this.eh(a,b):b.Wd(c,d)?this.fh(a,b):H(new P("invalid-auth-event")):(mq(this,!1,null,null),G())}else H(new P("invalid-auth-event"))};var gq=function(a){a.ue||(a.ue=!0,mq(a,!1,null,null))};
aq.prototype.ad=function(){this.ue&&!this.Nj&&mq(this,!1,null,null)};aq.prototype.eh=function(a){mq(this,!0,null,a.getError());G()};aq.prototype.fh=function(a,b){var c=this,d=a.Ja,e=a.getType();b=b.Wd(e,d);d=a.Kd;e=a.Yd();var f=a.dh,g=a.ma,h=!!a.getType().match(/Redirect$/);b(d,e,g,f).then(function(l){mq(c,h,l,null)}).l(function(l){mq(c,h,null,l)})};
var nq=function(a,b){a.Dd=function(){return H(b)};if(a.re.length)for(var c=0;c<a.re.length;c++)a.re[c](b)},oq=function(a,b){a.Dd=function(){return G(b)};if(a.te.length)for(var c=0;c<a.te.length;c++)a.te[c](b)},mq=function(a,b,c,d){b?d?nq(a,d):oq(a,c):oq(a,{user:null});a.te=[];a.re=[]};aq.prototype.getRedirectResult=function(){var a=this;return new F(function(b,c){a.Dd?a.Dd().then(b,c):(a.te.push(b),a.re.push(c),pq(a))})};
var pq=function(a){var b=new P("timeout");a.Cd&&a.Cd.cancel();a.Cd=Yj(lq.get()).then(function(){a.Dd||(a.ue=!0,mq(a,!0,null,b))})},bq=function(a){this.O=a};bq.prototype.oj=function(a,b){if(a){var c=a.getType(),d=a.Ja;a.yb?this.eh(a,b):b.Wd(c,d)?this.fh(a,b):H(new P("invalid-auth-event"))}else H(new P("invalid-auth-event"))};bq.prototype.eh=function(a,b){var c=a.Ja,d=a.getType();b.Sc(d,null,a.getError(),c);G()};
bq.prototype.fh=function(a,b){var c=a.Ja,d=a.getType(),e=b.Wd(d,c),f=a.Kd,g=a.Yd();e(f,g,a.ma,a.dh).then(function(h){b.Sc(d,h,null,c)}).l(function(h){b.Sc(d,null,h,c)})};var qq=function(a,b,c){var d=b&&b.mfaPendingCredential;if(!d)throw new P("argument-error","Internal assert: Invalid MultiFactorResolver");this.Ke=a;this.wk=Ib(b);this.zl=c;this.Aj=new Vl(null,d);this.Ai=[];var e=this;z(b.mfaInfo||[],function(f){(f=Kl(f))&&e.Ai.push(f)});O(this,"auth",this.Ke);O(this,"session",this.Aj);O(this,"hints",this.Ai)};
qq.prototype.resolveSignIn=function(a){var b=this;return a.process(this.Ke.o,this.Aj).then(function(c){var d=Ib(b.wk);delete d.mfaInfo;delete d.mfaPendingCredential;Kb(d,c);return b.zl(d)})};var rq=function(a,b,c,d){P.call(this,"multi-factor-auth-required",d,b);this.Ol=new qq(a,b,c);O(this,"resolver",this.Ol)};p(rq,P);var sq=function(a,b,c){if(a&&u(a.serverResponse)&&a.code==="auth/multi-factor-auth-required")try{return new rq(b,a.serverResponse,c,a.message)}catch(d){}return null};var tq=function(){};tq.prototype.process=function(a,b,c){return b.type=="enroll"?uq(this,a,b,c):vq(this,a,b)};var uq=function(a,b,c,d){return c.Xd().then(function(e){e={idToken:e};typeof d!=="undefined"&&(e.displayName=d);Kb(e,{phoneVerificationInfo:zm(a.Rg)});return Q(b,Nn,e)})},vq=function(a,b,c){return c.Xd().then(function(d){d={mfaPendingCredential:d};Kb(d,{phoneVerificationInfo:zm(a.Rg)});return Q(b,On,d)})},wq=function(a){O(this,"factorId",a.providerId);this.Rg=a};w(wq,tq);
var xq=function(a){wq.call(this,a);if(this.Rg.providerId!=Dm.PROVIDER_ID)throw new P("argument-error","firebase.auth.PhoneMultiFactorAssertion requires a valid firebase.auth.PhoneAuthCredential");};w(xq,wq);var yq=function(a,b){Ii.call(this,a);for(var c in b)this[c]=b[c]};p(yq,Ii);var Aq=function(a,b){this.jc=a;this.Te=[];this.lm=v(this.Vk,this);Vi(this.jc,"userReloaded",this.lm);var c=[];b&&b.multiFactor&&b.multiFactor.enrolledFactors&&z(b.multiFactor.enrolledFactors,function(d){var e=null,f={};if(d){d.uid&&(f.mfaEnrollmentId=d.uid);d.displayName&&(f.displayName=d.displayName);d.enrollmentTime&&(f.enrolledAt=(new Date(d.enrollmentTime)).toISOString());if(d.phoneNumber){f.phoneInfo=d.phoneNumber;try{e=new Il(f)}catch(g){}}else if(d.totpInfo){f.totpInfo=d.totpInfo;try{e=new Jl(f)}catch(g){}}else e=
null;d=e}else d=null;d&&c.push(d)});zq(this,c)},Bq=function(a){var b=[];z(a.mfaInfo||[],function(c){(c=Kl(c))&&b.push(c)});return b};Aq.prototype.Vk=function(a){zq(this,Bq(a.mm))};var zq=function(a,b){a.Te=b;O(a,"enrolledFactors",b)};k=Aq.prototype;k.copy=function(a){zq(this,a.Te)};k.getSession=function(){return this.jc.getIdToken().then(function(a){return new Vl(a,null)})};
k.enroll=function(a,b){var c=this,d=this.jc.o;return this.getSession().then(function(e){return a.process(d,e,b)}).then(function(e){Cq(c.jc,e);return c.jc.reload()})};k.unenroll=function(a){var b=this,c=typeof a==="string"?a:a.uid,d=this.jc.o;return this.jc.getIdToken().then(function(e){return Q(d,Sn,{idToken:e,mfaEnrollmentId:c})}).then(function(e){var f=wb(b.Te,function(g){return g.uid!=c});zq(b,f);Cq(b.jc,e);return b.jc.reload().l(function(g){if(g.code!="auth/user-token-expired")throw g;})})};
k.T=function(){return{multiFactor:{enrolledFactors:xb(this.Te,function(a){return a.T()})}}};var Dq=function(a){this.o=a;this.Wa=this.Ta=null;this.kd=Date.now()};Dq.prototype.T=function(){return{apiKey:this.o.ha,refreshToken:this.Ta,accessToken:this.Wa&&this.Wa.toString(),expirationTime:this.kd}};var Eq=function(a,b){typeof b==="undefined"&&(a.Wa?(b=a.Wa,b=b.yg-b.Xk):b=0);a.kd=Date.now()+b*1E3},Fq=function(a,b){a.Wa=Tl(b.idToken||"");a.Ta=b.refreshToken;b=b.expiresIn;Eq(a,typeof b!=="undefined"?Number(b):void 0)};Dq.prototype.copy=function(a){this.Wa=a.Wa;this.Ta=a.Ta;this.kd=a.kd};
var Gq=function(a,b){return bn(a.o,b).then(function(c){a.Wa=Tl(c.access_token);a.Ta=c.refresh_token;Eq(a,c.expires_in);return{accessToken:a.Wa.toString(),refreshToken:a.Ta}}).l(function(c){c.code=="auth/user-token-expired"&&(a.Ta=null);throw c;})};Dq.prototype.getToken=function(a){a=!!a;return this.Wa&&!this.Ta?H(new P("user-token-expired")):a||!this.Wa||Date.now()>this.kd-3E4?this.Ta?Gq(this,{grant_type:"refresh_token",refresh_token:this.Ta}):G(null):G({accessToken:this.Wa.toString(),refreshToken:this.Ta})};var Hq=function(a,b){this.Xh=a||null;this.Mi=b||null;vl(this,{lastSignInTime:nl(b||null),creationTime:nl(a||null)})};Hq.prototype.clone=function(){return new Hq(this.Xh,this.Mi)};Hq.prototype.T=function(){return{lastLoginAt:this.Mi,createdAt:this.Xh}};
var Iq=function(a,b,c,d,e,f){vl(this,{uid:a,displayName:d||null,photoURL:e||null,email:c||null,phoneNumber:f||null,providerId:b})},R=function(a,b,c){fj.call(this);this.Ka=[];this.ha=a.apiKey;this.ka=a.appName;this.ya=a.authDomain||null;var d=firebase.SDK_VERSION?Zk("JsCore",firebase.SDK_VERSION):null;this.o=new Sm(this.ha,zk(Ak),d);(this.N=a.emulatorConfig||null)&&Wm(this.o,this.N);this.Nb=new Dq(this.o);Jq(this,b.idToken);Fq(this.Nb,b);O(this,"refreshToken",this.Nb.Ta);Kq(this,c||{});this.me=!1;
this.ya&&cl()&&(this.W=kq(this.ya,this.ha,this.ka,this.N));this.Nf=[];this.Ob=null;this.Ad=Lq(this);this.Md=v(this.Fg,this);var e=this;this.Gb=null;this.Zi=function(f){e.Gd(f.languageCode)};this.Mg=null;this.Xi=function(f){Mq(e,f.emulatorConfig)};this.rg=null;this.Da=[];this.Yi=function(f){Nq(e,f.Ek)};this.zg=null;this.uf=new Aq(this,c);O(this,"multiFactor",this.uf)};p(R,fj);R.prototype.Gd=function(a){this.Gb=a;Um(this.o,a)};
var Mq=function(a,b){a.N=b;Wm(a.o,b);a.W&&(b=a.W,a.W=kq(a.ya,a.ha,a.ka,a.N),a.me&&(b.unsubscribe(a),a.W.subscribe(a)))},Oq=function(a,b){a.Mg&&cj(a.Mg,"languageCodeChanged",a.Zi);(a.Mg=b)&&Vi(b,"languageCodeChanged",a.Zi)},Pq=function(a,b){a.rg&&cj(a.rg,"emulatorConfigChanged",a.Xi);(a.rg=b)&&Vi(b,"emulatorConfigChanged",a.Xi)},Nq=function(a,b){a.Da=b;Xm(a.o,firebase.SDK_VERSION?Zk("JsCore",firebase.SDK_VERSION,a.Da):null)},Qq=function(a,b){a.zg&&cj(a.zg,"frameworkChanged",a.Yi);(a.zg=b)&&Vi(b,"frameworkChanged",
a.Yi)};R.prototype.Fg=function(){this.Ad.xd&&(this.Ad.stop(),this.Ad.start())};
var Rq=function(a){try{return firebase.app(a.ka).auth()}catch(b){throw new P("internal-error","No firebase.auth.Auth instance is available for the Firebase App '"+a.ka+"'!");}},Lq=function(a){return new lp(function(){return a.getIdToken(!0)},function(b){return b&&b.code=="auth/network-request-failed"?!0:!1},function(){var b=a.Nb.kd-Date.now()-3E5;return b>0?b:0})},Sq=function(a){a.jd||a.Ad.xd||(a.Ad.start(),cj(a,"tokenChanged",a.Md),Vi(a,"tokenChanged",a.Md))},Tq=function(a){cj(a,"tokenChanged",a.Md);
a.Ad.stop()},Jq=function(a,b){a.Li=b;O(a,"_lat",b)},Uq=function(a,b){Cb(a.Nf,function(c){return c==b})},Vq=function(a){for(var b=[],c=0;c<a.Nf.length;c++)b.push(a.Nf[c](a));return bg(b).then(function(){return a})},Wq=function(a){a.W&&!a.me&&(a.me=!0,a.W.subscribe(a))},Kq=function(a,b){vl(a,{uid:b.uid,displayName:b.displayName||null,photoURL:b.photoURL||null,email:b.email||null,emailVerified:b.emailVerified||!1,phoneNumber:b.phoneNumber||null,isAnonymous:b.isAnonymous||!1,tenantId:b.tenantId||null,
metadata:new Hq(b.createdAt,b.lastLoginAt),providerData:[]});a.o.ma=a.tenantId},Xq=function(){},Yq=function(a){return G().then(function(){if(a.jd)throw new P("app-deleted");})},Zq=function(a){return xb(a.providerData,function(b){return b.providerId})},ar=function(a,b){b&&($q(a,b.providerId),a.providerData.push(b))},$q=function(a,b){Cb(a.providerData,function(c){return c.providerId==b})},br=function(a,b,c){(b!="uid"||c)&&a.hasOwnProperty(b)&&O(a,b,c)};
R.prototype.copy=function(a){var b=this;b!=a&&(vl(this,{uid:a.uid,displayName:a.displayName,photoURL:a.photoURL,email:a.email,emailVerified:a.emailVerified,phoneNumber:a.phoneNumber,isAnonymous:a.isAnonymous,tenantId:a.tenantId,providerData:[]}),a.metadata?O(this,"metadata",a.metadata.clone()):O(this,"metadata",new Hq),z(a.providerData,function(c){ar(b,c)}),this.Nb.copy(a.Nb),O(this,"refreshToken",this.Nb.Ta),this.uf.copy(a.uf))};R.prototype.reload=function(){var a=this;return this.u(Yq(this).then(function(){return cr(a).then(function(){return Vq(a)}).then(Xq)}))};
var cr=function(a){return a.getIdToken().then(function(b){var c=a.isAnonymous;return Q(a.o,Pn,{idToken:b}).then(v(a.El,a)).then(function(){c||br(a,"isAnonymous",!1);return b})})};R.prototype.getIdTokenResult=function(a){return this.getIdToken(a).then(function(b){return new Ul(b)})};
R.prototype.getIdToken=function(a){var b=this;return this.u(Yq(this).then(function(){return b.Nb.getToken(a)}).then(function(c){if(!c)throw new P("internal-error");c.accessToken!=b.Li&&(Jq(b,c.accessToken),b.rc());br(b,"refreshToken",c.refreshToken);return c.accessToken}))};var Cq=function(a,b){b.idToken&&a.Li!=b.idToken&&(Fq(a.Nb,b),a.rc(),Jq(a,b.idToken),br(a,"refreshToken",a.Nb.Ta))};R.prototype.rc=function(){this.dispatchEvent(new yq("tokenChanged"))};
R.prototype.El=function(a){a=a.users;if(!a||!a.length)throw new P("internal-error");a=a[0];Kq(this,{uid:a.localId,displayName:a.displayName,photoURL:a.photoUrl,email:a.email,emailVerified:!!a.emailVerified,phoneNumber:a.phoneNumber,lastLoginAt:a.lastLoginAt,createdAt:a.createdAt,tenantId:a.tenantId});for(var b=dr(a),c=0;c<b.length;c++)ar(this,b[c]);br(this,"isAnonymous",!(this.email&&a.passwordHash)&&!(this.providerData&&this.providerData.length));this.dispatchEvent(new yq("userReloaded",{mm:a}))};
var dr=function(a){return(a=a.providerUserInfo)&&a.length?xb(a,function(b){return new Iq(b.rawId,b.providerId,b.email,b.displayName,b.photoUrl,b.phoneNumber)}):[]};R.prototype.reauthenticateAndRetrieveDataWithCredential=function(a){sl("firebase.User.prototype.reauthenticateAndRetrieveDataWithCredential is deprecated. Please use firebase.User.prototype.reauthenticateWithCredential instead.");return this.reauthenticateWithCredential(a)};
R.prototype.reauthenticateWithCredential=function(a){var b=this,c=null;return this.u(a.ie(this.o,this.uid).then(function(d){Cq(b,d);c=er(b,d,"reauthenticate");b.Ob=null;return b.reload()}).then(function(){return c}),!0)};var fr=function(a,b){return cr(a).then(function(){if(zb(Zq(a),b))return Vq(a).then(function(){throw new P("provider-already-linked");})})};
R.prototype.linkAndRetrieveDataWithCredential=function(a){sl("firebase.User.prototype.linkAndRetrieveDataWithCredential is deprecated. Please use firebase.User.prototype.linkWithCredential instead.");return this.linkWithCredential(a)};R.prototype.linkWithCredential=function(a){var b=this,c=null;return this.u(fr(this,a.providerId).then(function(){return b.getIdToken()}).then(function(d){return a.ud(b.o,d)}).then(function(d){c=er(b,d,"link");return gr(b,d)}).then(function(){return c}))};
R.prototype.linkWithPhoneNumber=function(a,b){var c=this;return this.u(fr(this,"phone").then(function(){return kp(Rq(c),a,b,v(c.linkWithCredential,c))}))};R.prototype.reauthenticateWithPhoneNumber=function(a,b){var c=this;return this.u(G().then(function(){return kp(Rq(c),a,b,v(c.reauthenticateWithCredential,c))}),!0)};var er=function(a,b,c){var d=Im(b);b=up(b);return wl({user:a,credential:d,additionalUserInfo:b,operationType:c})},gr=function(a,b){Cq(a,b);return a.reload().then(function(){return a})};
k=R.prototype;k.updateEmail=function(a){var b=this;return this.u(this.getIdToken().then(function(c){return b.o.updateEmail(c,a)}).then(function(c){Cq(b,c);return b.reload()}))};k.updatePhoneNumber=function(a){var b=this;return this.u(this.getIdToken().then(function(c){return a.ud(b.o,c)}).then(function(c){Cq(b,c);return b.reload()}))};k.updatePassword=function(a){var b=this;return this.u(this.getIdToken().then(function(c){return b.o.updatePassword(c,a)}).then(function(c){Cq(b,c);return b.reload()}))};
k.updateProfile=function(a){if(a.displayName===void 0&&a.photoURL===void 0)return Yq(this);var b=this;return this.u(this.getIdToken().then(function(c){return b.o.updateProfile(c,{displayName:a.displayName,photoUrl:a.photoURL})}).then(function(c){Cq(b,c);br(b,"displayName",c.displayName||null);br(b,"photoURL",c.photoUrl||null);z(b.providerData,function(d){d.providerId==="password"&&(O(d,"displayName",b.displayName),O(d,"photoURL",b.photoURL))});return Vq(b)}).then(Xq))};
k.unlink=function(a){var b=this;return this.u(cr(this).then(function(c){return zb(Zq(b),a)?zn(b.o,c,[a]).then(function(d){var e={};z(d.providerUserInfo||[],function(f){e[f.providerId]=!0});z(Zq(b),function(f){e[f]||$q(b,f)});e[Dm.PROVIDER_ID]||O(b,"phoneNumber",null);return Vq(b)}):Vq(b).then(function(){throw new P("no-such-provider");})}))};k.delete=function(){var a=this;return this.u(this.getIdToken().then(function(b){return Q(a.o,Mn,{idToken:b})}).then(function(){a.dispatchEvent(new yq("userDeleted"))})).then(function(){a.destroy()})};
k.Rh=function(a,b){return a=="linkViaPopup"&&(this.Kb||null)==b&&this.Jb||a=="reauthViaPopup"&&(this.Kb||null)==b&&this.Jb||a=="linkViaRedirect"&&(this.wc||null)==b||a=="reauthViaRedirect"&&(this.wc||null)==b?!0:!1};k.Sc=function(a,b,c,d){a!="linkViaPopup"&&a!="reauthViaPopup"||d!=(this.Kb||null)||(c&&this.Qc?this.Qc(c):b&&!c&&this.Jb&&this.Jb(b),this.La&&(this.La.cancel(),this.La=null),delete this.Jb,delete this.Qc)};
k.Wd=function(a,b){return a=="linkViaPopup"&&b==(this.Kb||null)?v(this.ii,this):a=="reauthViaPopup"&&b==(this.Kb||null)?v(this.ji,this):a=="linkViaRedirect"&&(this.wc||null)==b?v(this.ii,this):a=="reauthViaRedirect"&&(this.wc||null)==b?v(this.ji,this):null};k.We=function(){return $k(this.uid+":::")};k.linkWithPopup=function(a){var b=this;return hr(this,"linkViaPopup",a,function(){return fr(b,a.providerId).then(function(){return Vq(b)})},!1)};
k.reauthenticateWithPopup=function(a){return hr(this,"reauthViaPopup",a,function(){return G()},!0)};
var hr=function(a,b,c,d,e){if(!cl())return H(new P("operation-not-supported-in-this-environment"));if(a.Ob&&!e)return H(a.Ob);var f=Cl(c.providerId),g=a.We(),h=null;(!dl()||Tk())&&a.ya&&c.isOAuthProvider&&(h=ho(a.ya,a.ha,a.ka,b,c,null,g,firebase.SDK_VERSION||null,null,null,a.tenantId,a.N));var l=Kk(h,f&&f.zd,f&&f.yd);d=d().then(function(){ir(a);if(!e)return a.getIdToken().then(function(){})}).then(function(){return a.W.ne(l,b,c,g,!!h,a.tenantId)}).then(function(){return new F(function(m,q){a.Sc(b,
null,new P("cancelled-popup-request"),a.Kb||null);a.Jb=m;a.Qc=q;a.Kb=g;a.La=a.W.ye(a,b,l,g)})}).then(function(m){l&&Jk(l);return m?wl(m):null}).l(function(m){l&&Jk(l);throw m;});return a.u(d,e)};R.prototype.linkWithRedirect=function(a){var b=this;return jr(this,"linkViaRedirect",a,function(){return fr(b,a.providerId)},!1)};R.prototype.reauthenticateWithRedirect=function(a){return jr(this,"reauthViaRedirect",a,function(){return G()},!0)};
var jr=function(a,b,c,d,e){if(!cl())return H(new P("operation-not-supported-in-this-environment"));if(a.Ob&&!e)return H(a.Ob);var f=null,g=a.We();d=d().then(function(){ir(a);if(!e)return a.getIdToken().then(function(){})}).then(function(){a.wc=g;return Vq(a)}).then(function(h){a.xc&&(h=a.xc,h=h.O.set(kr,a.T(),h.V));return h}).then(function(){return a.W.oe(b,c,g,a.tenantId)}).l(function(h){f=h;if(a.xc)return lr(a.xc);throw f;}).then(function(){if(f)throw f;});return a.u(d,e)},ir=function(a){if(!a.W||
!a.me){if(a.W&&!a.me)throw new P("internal-error");throw new P("auth-domain-config-required");}};k=R.prototype;k.ii=function(a,b,c,d){var e=this;this.La&&(this.La.cancel(),this.La=null);var f=null;c=this.getIdToken().then(function(g){return $l(e.o,{requestUri:a,postBody:d,sessionId:b,idToken:g})}).then(function(g){f=er(e,g,"link");return gr(e,g)}).then(function(){return f});return this.u(c)};
k.ji=function(a,b,c,d){var e=this;this.La&&(this.La.cancel(),this.La=null);var f=null,g=G().then(function(){return Xl(am(e.o,{requestUri:a,sessionId:b,postBody:d,tenantId:c}),e.uid)}).then(function(h){f=er(e,h,"reauthenticate");Cq(e,h);e.Ob=null;return e.reload()}).then(function(){return f});return this.u(g,!0)};
k.sendEmailVerification=function(a){var b=this,c=null;return this.u(this.getIdToken().then(function(d){c=d;return typeof a==="undefined"||Hb(a)?{}:ip(new hp(a))}).then(function(d){return b.o.sendEmailVerification(c,d)}).then(function(d){if(b.email!=d)return b.reload()}).then(function(){}))};
k.verifyBeforeUpdateEmail=function(a,b){var c=this,d=null;return this.u(this.getIdToken().then(function(e){d=e;return typeof b==="undefined"||Hb(b)?{}:ip(new hp(b))}).then(function(e){return c.o.verifyBeforeUpdateEmail(d,a,e)}).then(function(e){if(c.email!=e)return c.reload()}).then(function(){}))};k.destroy=function(){for(var a=0;a<this.Ka.length;a++)this.Ka[a].cancel("app-deleted");Oq(this,null);Pq(this,null);Qq(this,null);this.Ka=[];this.jd=!0;Tq(this);O(this,"refreshToken",null);this.W&&this.W.unsubscribe(this)};
k.u=function(a,b){var c=this,d=mr(this,a,b);this.Ka.push(d);d.Ac(function(){Ab(c.Ka,d)});return d.l(function(e){var f=null;e&&e.code==="auth/multi-factor-auth-required"&&(f=sq(e.T(),Rq(c),v(c.Eg,c)));throw f||e;})};k.Eg=function(a){var b=null,c=this;a=Xl(G(a),c.uid).then(function(d){b=er(c,d,"reauthenticate");Cq(c,d);c.Ob=null;return c.reload()}).then(function(){return b});return this.u(a,!0)};
var mr=function(a,b,c){return a.Ob&&!c?(b.cancel(),H(a.Ob)):b.l(function(d){!d||d.code!="auth/user-disabled"&&d.code!="auth/user-token-expired"||(a.Ob||a.dispatchEvent(new yq("userInvalidated")),a.Ob=d);throw d;})};R.prototype.toJSON=function(){return this.T()};
R.prototype.T=function(){var a={uid:this.uid,displayName:this.displayName,photoURL:this.photoURL,email:this.email,emailVerified:this.emailVerified,phoneNumber:this.phoneNumber,isAnonymous:this.isAnonymous,tenantId:this.tenantId,providerData:[],apiKey:this.ha,appName:this.ka,authDomain:this.ya,stsTokenManager:this.Nb.T(),redirectEventId:this.wc||null};this.metadata&&Kb(a,this.metadata.T());z(this.providerData,function(b){var c=a.providerData,d=c.push,e={},f;for(f in b)b.hasOwnProperty(f)&&(e[f]=b[f]);
d.call(c,e)});Kb(a,this.uf.T());return a};
var nr=function(a){if(!a.apiKey)return null;var b={apiKey:a.apiKey,authDomain:a.authDomain,appName:a.appName,emulatorConfig:a.emulatorConfig},c={};if(a.stsTokenManager&&a.stsTokenManager.accessToken){c.idToken=a.stsTokenManager.accessToken;c.refreshToken=a.stsTokenManager.refreshToken||null;var d=a.stsTokenManager.expirationTime;d&&(c.expiresIn=(d-Date.now())/1E3)}else return null;var e=new R(b,c,a);a.providerData&&z(a.providerData,function(f){f&&ar(e,wl(f))});a.redirectEventId&&(e.wc=a.redirectEventId);
return e},or=function(a,b,c,d){var e=new R(a,b);c&&(e.xc=c);d&&Nq(e,d);return e.reload().then(function(){return e})},pr=function(a,b,c,d){b=b||{apiKey:a.ha,authDomain:a.ya,appName:a.ka};var e=a.Nb,f={};f.idToken=e.Wa&&e.Wa.toString();f.refreshToken=e.Ta;b=new R(b,f);c&&(b.xc=c);d&&Nq(b,d);b.copy(a);return b};O(R.prototype,"providerId","firebase");var qr=function(a){this.V=a;this.O=zp()},lr=function(a){return a.O.remove(kr,a.V)},rr=function(a,b){return a.O.get(kr,a.V).then(function(c){c&&b&&(c.authDomain=b);return nr(c||{})})},kr={name:"redirectUser",ua:"session"};var tr=function(a){this.V=a;this.O=zp();this.Xa=null;this.Ug=this.Kg();this.O.addListener(sr("local"),this.V,v(this.fm,this))};tr.prototype.fm=function(){var a=this,b=sr("local");ur(this,function(){return G().then(function(){return a.Xa&&a.Xa.ua!="local"?a.O.get(b,a.V):null}).then(function(c){if(c)return vr(a,"local").then(function(){a.Xa=b})})})};var vr=function(a,b){var c=[],d;for(d in vp)vp[d]!==b&&c.push(a.O.remove(sr(vp[d]),a.V));c.push(a.O.remove(wr,a.V));return ag(c)};
tr.prototype.Kg=function(){var a=this,b=sr("local"),c=sr("session"),d=sr("none");return Bp(this.O,b,this.V).then(function(){return a.O.get(c,a.V)}).then(function(e){return e?c:a.O.get(d,a.V).then(function(f){return f?d:a.O.get(b,a.V).then(function(g){return g?b:a.O.get(wr,a.V).then(function(h){return h?sr(h):b})})})}).then(function(e){a.Xa=e;return vr(a,e.ua)}).l(function(){a.Xa||(a.Xa=b)})};var sr=function(a){return{name:"authUser",ua:a}};
tr.prototype.setPersistence=function(a){var b=null,c=this;wp(a);return ur(this,function(){return a!=c.Xa.ua?c.O.get(c.Xa,c.V).then(function(d){b=d;return vr(c,a)}).then(function(){c.Xa=sr(a);if(b)return c.O.set(c.Xa,b,c.V)}):G()})};
var xr=function(a){return ur(a,function(){return a.O.set(wr,a.Xa.ua,a.V)})},yr=function(a,b){return ur(a,function(){return a.O.set(a.Xa,b.T(),a.V)})},zr=function(a){return ur(a,function(){return a.O.remove(a.Xa,a.V)})},Ar=function(a,b,c){return ur(a,function(){return a.O.get(a.Xa,a.V).then(function(d){d&&b&&(d.authDomain=b);d&&c&&(d.emulatorConfig=c);return nr(d||{})})})},ur=function(a,b){a.Ug=a.Ug.then(b,b);return a.Ug},wr={name:"persistence",ua:"session"};var T=function(a){fj.call(this);this.wb=!1;this.Ej=new gp;O(this,"settings",this.Ej);O(this,"app",a);if(this.U().options&&this.U().options.apiKey)a=firebase.SDK_VERSION?Zk("JsCore",firebase.SDK_VERSION):null,this.o=new Sm(this.U().options&&this.U().options.apiKey,zk(Ak),a);else throw new P("invalid-api-key");this.Ka=[];this.Dc=[];this.Ld=[];this.yl=firebase.INTERNAL.createSubscribe(v(this.al,this));this.Ge=void 0;this.Bl=firebase.INTERNAL.createSubscribe(v(this.bl,this));Br(this,null);a=this.U().options.apiKey;
var b=this.U().name;this.Pb=new tr(a+":"+b);a=this.U().options.apiKey;b=this.U().name;this.Rc=new qr(a+":"+b);this.Je=this.u(Cr(this));this.Lb=this.u(Dr(this));this.nf=!1;this.Cg=v(this.hm,this);this.Rj=v(this.oc,this);this.Md=v(this.Fg,this);this.Pj=v(this.Tk,this);this.Qj=v(this.Uk,this);this.W=null;Er(this);this.INTERNAL={};this.INTERNAL["delete"]=v(this.delete,this);this.INTERNAL.logFramework=v(this.ql,this);this.Hc=0;Fr(this);this.Da=[];this.N=null};p(T,fj);
T.prototype.setPersistence=function(a){a=this.Pb.setPersistence(a);return this.u(a)};T.prototype.Gd=function(a){this.Gb===a||this.wb||(this.Gb=a,Um(this.o,this.Gb),this.dispatchEvent(new Gr(this.Gb)))};T.prototype.useDeviceLanguage=function(){var a=r.navigator;this.Gd(a?a.languages&&a.languages[0]||a.language||a.userLanguage||null:null)};
T.prototype.useEmulator=function(a,b){if(!this.N){if(this.W)throw new P("argument-error","useEmulator() must be called immediately following firebase.auth() initialization.");b=b?!!b.disableWarnings:!1;Hr(b);this.N={url:a,disableWarnings:b};this.Ej.Yf=!0;Wm(this.o,this.N);this.dispatchEvent(new Ir(this.N))}};
var Hr=function(a){typeof console!=="undefined"&&typeof console.info==="function"&&console.info("WARNING: You are using the Auth Emulator, which is intended for local testing only.  Do not use with production credentials.");r.document&&!a&&Pk().then(function(){var b=r.document.createElement("p");b.innerText="Running in emulator mode. Do not use with production credentials.";b.style.position="fixed";b.style.width="100%";b.style.backgroundColor="#ffffff";b.style.border=".1em solid #000000";b.style.color=
"#b50000";b.style.bottom="0px";b.style.left="0px";b.style.margin="0px";b.style.zIndex=1E4;b.style.textAlign="center";b.classList.add("firebase-emulator-warning");r.document.body.appendChild(b)})};T.prototype.ql=function(a){this.Da.push(a);Xm(this.o,firebase.SDK_VERSION?Zk("JsCore",firebase.SDK_VERSION,this.Da):null);this.dispatchEvent(new Jr(this.Da))};T.prototype.yh=function(a){this.ma===a||this.wb||(this.ma=a,this.o.ma=this.ma)};
var Fr=function(a){Object.defineProperty(a,"lc",{get:function(){return this.Gb},set:function(b){this.Gd(b)},enumerable:!1});a.Gb=null;Object.defineProperty(a,"ti",{get:function(){return this.ma},set:function(b){this.yh(b)},enumerable:!1});a.ma=null;Object.defineProperty(a,"emulatorConfig",{get:function(){if(this.N){var b=M(this.N.url);b=wl({protocol:b.Va,host:b.Ga,port:b.Tb,options:wl({disableWarnings:this.N.disableWarnings})})}else b=null;return b},enumerable:!1})};
T.prototype.toJSON=function(){return{apiKey:this.U().options.apiKey,authDomain:this.U().options.authDomain,appName:this.U().name,currentUser:U(this)&&U(this).T()}};
var Kr=function(a){return a.xk||H(new P("auth-domain-config-required"))},Er=function(a){var b=a.U().options.authDomain,c=a.U().options.apiKey;b&&cl()&&(a.xk=a.Je.then(function(){if(!a.wb){a.W=kq(b,c,a.U().name,a.N);a.W.subscribe(a);U(a)&&Wq(U(a));if(a.yc){Wq(a.yc);var d=a.yc;d.Gd(a.Gb);Oq(d,a);d=a.yc;Nq(d,a.Da);Qq(d,a);d=a.yc;Mq(d,a.N);Pq(d,a);a.yc=null}return a.W}}))};k=T.prototype;
k.Rh=function(a,b){switch(a){case "unknown":case "signInViaRedirect":return!0;case "signInViaPopup":return this.Kb==b&&!!this.Jb;default:return!1}};k.Sc=function(a,b,c,d){a=="signInViaPopup"&&this.Kb==d&&(c&&this.Qc?this.Qc(c):b&&!c&&this.Jb&&this.Jb(b),this.La&&(this.La.cancel(),this.La=null),delete this.Jb,delete this.Qc)};k.Wd=function(a,b){return a=="signInViaRedirect"||a=="signInViaPopup"&&this.Kb==b&&this.Jb?v(this.Bk,this):null};
k.Bk=function(a,b,c,d){var e=this,f={requestUri:a,postBody:d,sessionId:b,tenantId:c};this.La&&(this.La.cancel(),this.La=null);return e.Je.then(function(){return Lr(e,Zl(e.o,f))})};k.We=function(){return $k()};
k.signInWithPopup=function(a){if(!cl())return H(new P("operation-not-supported-in-this-environment"));var b=this,c=Cl(a.providerId),d=this.We(),e=null;(!dl()||Tk())&&this.U().options.authDomain&&a.isOAuthProvider&&(e=ho(this.U().options.authDomain,this.U().options.apiKey,this.U().name,"signInViaPopup",a,null,d,firebase.SDK_VERSION||null,null,null,this.ma,this.N));var f=Kk(e,c&&c.zd,c&&c.yd);c=Kr(this).then(function(g){return g.ne(f,"signInViaPopup",a,d,!!e,b.ma)}).then(function(){return new F(function(g,
h){b.Sc("signInViaPopup",null,new P("cancelled-popup-request"),b.Kb);b.Jb=g;b.Qc=h;b.Kb=d;b.La=b.W.ye(b,"signInViaPopup",f,d)})}).then(function(g){f&&Jk(f);return g?wl(g):null}).l(function(g){f&&Jk(f);throw g;});return this.u(c)};k.signInWithRedirect=function(a){if(!cl())return H(new P("operation-not-supported-in-this-environment"));var b=this,c=Kr(this).then(function(){return xr(b.Pb)}).then(function(){return b.W.oe("signInViaRedirect",a,void 0,b.ma)});return this.u(c)};
var Mr=function(a){if(!cl())return H(new P("operation-not-supported-in-this-environment"));var b=Kr(a).then(function(){return a.W.getRedirectResult()}).then(function(c){return c?wl(c):null});return a.u(b)};T.prototype.getRedirectResult=function(){var a=this;return Mr(this).then(function(b){a.W&&a.W.ad();return b}).l(function(b){a.W&&a.W.ad();throw b;})};
T.prototype.updateCurrentUser=function(a){if(!a)return H(new P("null-user"));if(this.ma!=a.tenantId)return H(new P("tenant-id-mismatch"));var b=this,c={};c.apiKey=this.U().options.apiKey;c.authDomain=this.U().options.authDomain;c.appName=this.U().name;var d=pr(a,c,b.Rc,Db(b.Da));return this.u(this.Lb.then(function(){if(b.U().options.apiKey!=a.ha)return d.reload()}).then(function(){if(U(b)&&a.uid==U(b).uid)return U(b).copy(a),b.oc(a);Br(b,d);Wq(d);return b.oc(d)}).then(function(){b.rc()}))};
var Nr=function(a,b){var c={};c.apiKey=a.U().options.apiKey;c.authDomain=a.U().options.authDomain;c.appName=a.U().name;a.N&&(c.emulatorConfig=a.N);return a.Je.then(function(){return or(c,b,a.Rc,Db(a.Da))}).then(function(d){if(U(a)&&d.uid==U(a).uid)return U(a).copy(d),a.oc(d);Br(a,d);Wq(d);return a.oc(d)}).then(function(){a.rc()})},Br=function(a,b){U(a)&&(Uq(U(a),a.Rj),cj(U(a),"tokenChanged",a.Md),cj(U(a),"userDeleted",a.Pj),cj(U(a),"userInvalidated",a.Qj),Tq(U(a)));b&&(b.Nf.push(a.Rj),Vi(b,"tokenChanged",
a.Md),Vi(b,"userDeleted",a.Pj),Vi(b,"userInvalidated",a.Qj),a.Hc>0&&Sq(b));O(a,"currentUser",b);b&&(b.Gd(a.Gb),Oq(b,a),Nq(b,a.Da),Qq(b,a),Mq(b,a.N),Pq(b,a))};T.prototype.signOut=function(){var a=this,b=this.Lb.then(function(){a.W&&a.W.ad();if(!U(a))return G();Br(a,null);return zr(a.Pb).then(function(){a.rc()})});return this.u(b)};
var Or=function(a){var b=a.U().options.authDomain;b=rr(a.Rc,b).then(function(c){if(a.yc=c)c.xc=a.Rc;return lr(a.Rc)});return a.u(b)},Cr=function(a){var b=a.U().options.authDomain,c=Or(a).then(function(){return Ar(a.Pb,b,a.N)}).then(function(d){return d?(d.xc=a.Rc,a.yc&&(a.yc.wc||null)==(d.wc||null)?d:d.reload().then(function(){return yr(a.Pb,d).then(function(){return d})}).l(function(e){return e.code=="auth/network-request-failed"?d:zr(a.Pb)})):null}).then(function(d){Br(a,d||null)});return a.u(c)},
Dr=function(a){return a.Je.then(function(){return Mr(a)}).l(function(){}).then(function(){if(!a.wb)return a.Cg()}).l(function(){}).then(function(){if(!a.wb){a.nf=!0;var b=a.Pb;b.O.addListener(sr("local"),b.V,a.Cg)}})};k=T.prototype;
k.hm=function(){var a=this,b=this.U().options.authDomain;return Ar(this.Pb,b).then(function(c){if(!a.wb){var d;if(d=U(a)&&c){d=U(a).uid;var e=c.uid;d=d===void 0||d===null||d===""||e===void 0||e===null||e===""?!1:d==e}if(d)return U(a).copy(c),U(a).getIdToken();if(U(a)||c)Br(a,c),c&&(Wq(c),c.xc=a.Rc),a.W&&a.W.subscribe(a),a.rc()}})};k.oc=function(a){return yr(this.Pb,a)};k.Fg=function(){this.rc();this.oc(U(this))};k.Tk=function(){this.signOut()};k.Uk=function(){this.signOut()};
var Lr=function(a,b){var c=null,d=null;return a.u(b.then(function(e){c=Im(e);d=up(e);return Nr(a,e)},function(e){var f=null;e&&e.code==="auth/multi-factor-auth-required"&&(f=sq(e.T(),a,v(a.Eg,a)));throw f||e;}).then(function(){return wl({user:U(a),credential:c,additionalUserInfo:d,operationType:"signIn"})}))};k=T.prototype;k.Eg=function(a){var b=this;return this.Lb.then(function(){return Lr(b,G(a))})};k.al=function(a){var b=this;this.addAuthTokenListener(function(){a.next(U(b))})};
k.bl=function(a){var b=this;Pr(this,function(){a.next(U(b))})};k.onIdTokenChanged=function(a,b,c){var d=this;this.nf&&firebase.Promise.resolve().then(function(){typeof a==="function"?a(U(d)):typeof a.next==="function"&&a.next(U(d))});return this.yl(a,b,c)};k.onAuthStateChanged=function(a,b,c){var d=this;this.nf&&firebase.Promise.resolve().then(function(){d.Ge=d.getUid();typeof a==="function"?a(U(d)):typeof a.next==="function"&&a.next(U(d))});return this.Bl(a,b,c)};
k.Gk=function(a){var b=this,c=this.Lb.then(function(){return U(b)?U(b).getIdToken(a).then(function(d){return{accessToken:d}}):null});return this.u(c)};k.signInWithCustomToken=function(a){var b=this;return this.Lb.then(function(){return Lr(b,Q(b.o,Rn,{token:a}))}).then(function(c){var d=c.user;br(d,"isAnonymous",!1);b.oc(d);return c})};k.signInWithEmailAndPassword=function(a,b){var c=this;return this.Lb.then(function(){return Lr(c,Q(c.o,sm,{email:a,password:b}))})};
k.createUserWithEmailAndPassword=function(a,b){var c=this;return this.Lb.then(function(){return Lr(c,Q(c.o,Ln,{email:a,password:b}))})};k.signInWithCredential=function(a){var b=this;return this.Lb.then(function(){return Lr(b,a.Jc(b.o))})};k.signInAndRetrieveDataWithCredential=function(a){sl("firebase.auth.Auth.prototype.signInAndRetrieveDataWithCredential is deprecated. Please use firebase.auth.Auth.prototype.signInWithCredential instead.");return this.signInWithCredential(a)};
k.signInAnonymously=function(){var a=this;return this.Lb.then(function(){var b=U(a);if(b&&b.isAnonymous){var c=wl({providerId:null,isNewUser:!1});return wl({user:b,credential:null,additionalUserInfo:c,operationType:"signIn"})}return Lr(a,a.o.signInAnonymously()).then(function(d){var e=d.user;br(e,"isAnonymous",!0);a.oc(e);return d})})};k.U=function(){return this.app};var U=function(a){return a.currentUser};T.prototype.getUid=function(){return U(this)&&U(this).uid||null};
var Qr=function(a){return U(a)&&U(a)._lat||null};k=T.prototype;k.rc=function(){if(this.nf){for(var a=0;a<this.Dc.length;a++)if(this.Dc[a])this.Dc[a](Qr(this));if(this.Ge!==this.getUid()&&this.Ld.length)for(this.Ge=this.getUid(),a=0;a<this.Ld.length;a++)if(this.Ld[a])this.Ld[a](Qr(this))}};k.ak=function(a){this.addAuthTokenListener(a);this.Hc++;this.Hc>0&&U(this)&&Sq(U(this))};k.Jl=function(a){var b=this;z(this.Dc,function(c){c==a&&b.Hc--});this.Hc<0&&(this.Hc=0);this.Hc==0&&U(this)&&Tq(U(this));this.removeAuthTokenListener(a)};
k.addAuthTokenListener=function(a){var b=this;this.Dc.push(a);this.u(this.Lb.then(function(){b.wb||zb(b.Dc,a)&&a(Qr(b))}))};k.removeAuthTokenListener=function(a){Cb(this.Dc,function(b){return b==a})};var Pr=function(a,b){a.Ld.push(b);a.u(a.Lb.then(function(){!a.wb&&zb(a.Ld,b)&&a.Ge!==a.getUid()&&(a.Ge=a.getUid(),b(Qr(a)))}))};k=T.prototype;
k.delete=function(){this.wb=!0;for(var a=0;a<this.Ka.length;a++)this.Ka[a].cancel("app-deleted");this.Ka=[];this.Pb&&(a=this.Pb,a.O.removeListener(sr("local"),a.V,this.Cg));this.W&&(this.W.unsubscribe(this),this.W.ad());return firebase.Promise.resolve()};k.u=function(a){var b=this;this.Ka.push(a);a.Ac(function(){Ab(b.Ka,a)});return a};k.fetchSignInMethodsForEmail=function(a){return this.u(gn(this.o,a))};k.isSignInWithEmailLink=function(a){return!!wm(a)};
k.sendSignInLinkToEmail=function(a,b){var c=this;return this.u(G().then(function(){var d=new hp(b);if(!d.Sh)throw new P("argument-error","handleCodeInApp must be true when sending sign in link to email");return ip(d)}).then(function(d){return c.o.sendSignInLinkToEmail(a,d)}).then(function(){}))};k.verifyPasswordResetCode=function(a){return this.checkActionCode(a).then(function(b){return b.data.email})};k.confirmPasswordReset=function(a,b){return this.u(this.o.confirmPasswordReset(a,b).then(function(){}))};
k.checkActionCode=function(a){return this.u(this.o.checkActionCode(a).then(function(b){return new Ll(b)}))};k.applyActionCode=function(a){return this.u(this.o.applyActionCode(a).then(function(){}))};k.sendPasswordResetEmail=function(a,b){var c=this;return this.u(G().then(function(){return typeof b==="undefined"||Hb(b)?{}:ip(new hp(b))}).then(function(d){return c.o.sendPasswordResetEmail(a,d)}).then(function(){}))};
k.signInWithPhoneNumber=function(a,b){return this.u(kp(this,a,b,v(this.signInWithCredential,this)))};k.signInWithEmailLink=function(a,b){var c=this;return this.u(G().then(function(){b=b||Dk();var d=xm(a,b),e=wm(b);if(!e)throw new P("argument-error","Invalid email link!");if(e.tenantId!==c.ma)throw new P("tenant-id-mismatch");return c.signInWithCredential(d)}))};var Gr=function(a){Ii.call(this,"languageCodeChanged");this.languageCode=a};p(Gr,Ii);
var Ir=function(a){Ii.call(this,"emulatorConfigChanged");this.emulatorConfig=a};p(Ir,Ii);var Jr=function(a){Ii.call(this,"frameworkChanged");this.Ek=a};p(Jr,Ii);var Sr=function(a,b,c,d){a:{c=Array.prototype.slice.call(c);var e=0;for(var f=!1,g=0;g<b.length;g++)if(b[g].optional)f=!0;else{if(f)throw new P("internal-error","Argument validator encountered a required argument after an optional argument.");e++}f=b.length;if(c.length<e||f<c.length)d="Expected "+(e==f?e==1?"1 argument":e+" arguments":e+"-"+f+" arguments")+" but got "+c.length+".";else{for(e=0;e<c.length;e++)if(f=b[e].optional&&c[e]===void 0,!b[e].Ca(c[e])&&!f){c=e;b=b[e];if(c<0||c>=Rr.length)throw new P("internal-error",
"Argument validator received an unsupported number of arguments.");c=Rr[c];d=(d?"":c+" argument ")+(b.name?'"'+b.name+'" ':"")+"must be "+b.Fa+".";break a}d=null}}if(d)throw new P("argument-error",a+" failed: "+d);},Rr="First Second Third Fourth Fifth Sixth Seventh Eighth Ninth".split(" "),V=function(a,b){return{name:a||"",Fa:"a valid string",optional:!!b,Ca:function(c){return typeof c==="string"}}},Tr=function(a,b){return{name:a||"",Fa:"a boolean",optional:!!b,Ca:function(c){return typeof c==="boolean"}}},
W=function(a,b){return{name:a||"",Fa:"a valid object",optional:!!b,Ca:u}},Ur=function(a,b){return{name:a||"",Fa:"a function",optional:!!b,Ca:Sf}},Vr=function(a,b){return{name:a||"",Fa:"null",optional:!!b,Ca:function(c){return c===null}}},Wr=function(){return{name:"",Fa:"an HTML element",optional:!1,Ca:function(a){return!!(a&&a instanceof Element)}}},Xr=function(){return{name:"auth",Fa:"an instance of Firebase Auth",optional:!0,Ca:function(a){return!!(a&&a instanceof T)}}},Yr=function(){return{name:"app",
Fa:"an instance of Firebase App",optional:!0,Ca:function(a){return!!(a&&a instanceof firebase.app.App)}}},Zr=function(a){return{name:a?a+"Credential":"credential",Fa:a?"a valid "+a+" credential":"a valid credential",optional:!1,Ca:function(b){if(!b)return!1;var c=!a||b.providerId===a;return!(!b.Jc||!c)}}},$r=function(){return{name:"multiFactorAssertion",Fa:"a valid multiFactorAssertion",optional:!1,Ca:function(a){return a?!!a.process:!1}}},as=function(){return{name:"authProvider",Fa:"a valid Auth provider",
optional:!1,Ca:function(a){return!!(a&&a.providerId&&a.hasOwnProperty&&a.hasOwnProperty("isOAuthProvider"))}}},bs=function(a,b){return u(a)&&typeof a.type==="string"&&a.type===b&&typeof a.Xd==="function"},cs=function(a){return u(a)&&typeof a.uid==="string"},ds=function(){return{name:"applicationVerifier",Fa:"an implementation of firebase.auth.ApplicationVerifier",optional:!1,Ca:function(a){return!(!a||typeof a.type!=="string"||typeof a.verify!=="function")}}},X=function(a,b,c,d){return{name:c||"",
Fa:a.Fa+" or "+b.Fa,optional:!!d,Ca:function(e){return a.Ca(e)||b.Ca(e)}}};var Y=function(a,b){for(var c in b){var d=b[c].name;a[d]=es(d,a[c],b[c].j)}},fs=function(a,b){for(var c in b){var d=b[c].name;d!==c&&Object.defineProperty(a,d,{get:za(function(e){return this[e]},c),set:za(function(e,f,g,h){Sr(e,[g],[h],!0);this[f]=h},d,c,b[c].Zf),enumerable:!0})}},Z=function(a,b,c,d){a[b]=es(b,c,d)},es=function(a,b,c){if(!c)return b;var d=gs(a);a=function(){var g=Array.prototype.slice.call(arguments);Sr(d,c,g);return b.apply(this,g)};for(var e in b)a[e]=b[e];for(var f in b.prototype)a.prototype[f]=
b.prototype[f];return a},gs=function(a){a=a.split(".");return a[a.length-1]};function hs(){}O(hs,"FACTOR_ID","phone");Y(T.prototype,{applyActionCode:{name:"applyActionCode",j:[V("code")]},checkActionCode:{name:"checkActionCode",j:[V("code")]},confirmPasswordReset:{name:"confirmPasswordReset",j:[V("code"),V("newPassword")]},createUserWithEmailAndPassword:{name:"createUserWithEmailAndPassword",j:[V("email"),V("password")]},fetchSignInMethodsForEmail:{name:"fetchSignInMethodsForEmail",j:[V("email")]},getRedirectResult:{name:"getRedirectResult",j:[]},isSignInWithEmailLink:{name:"isSignInWithEmailLink",j:[V("emailLink")]},
onAuthStateChanged:{name:"onAuthStateChanged",j:[X(W(),Ur(),"nextOrObserver"),Ur("opt_error",!0),Ur("opt_completed",!0)]},onIdTokenChanged:{name:"onIdTokenChanged",j:[X(W(),Ur(),"nextOrObserver"),Ur("opt_error",!0),Ur("opt_completed",!0)]},sendPasswordResetEmail:{name:"sendPasswordResetEmail",j:[V("email"),X(W("opt_actionCodeSettings",!0),Vr(null,!0),"opt_actionCodeSettings",!0)]},sendSignInLinkToEmail:{name:"sendSignInLinkToEmail",j:[V("email"),W("actionCodeSettings")]},setPersistence:{name:"setPersistence",
j:[V("persistence")]},signInAndRetrieveDataWithCredential:{name:"signInAndRetrieveDataWithCredential",j:[Zr()]},signInAnonymously:{name:"signInAnonymously",j:[]},signInWithCredential:{name:"signInWithCredential",j:[Zr()]},signInWithCustomToken:{name:"signInWithCustomToken",j:[V("token")]},signInWithEmailAndPassword:{name:"signInWithEmailAndPassword",j:[V("email"),V("password")]},signInWithEmailLink:{name:"signInWithEmailLink",j:[V("email"),V("emailLink",!0)]},signInWithPhoneNumber:{name:"signInWithPhoneNumber",
j:[V("phoneNumber"),ds()]},signInWithPopup:{name:"signInWithPopup",j:[as()]},signInWithRedirect:{name:"signInWithRedirect",j:[as()]},updateCurrentUser:{name:"updateCurrentUser",j:[X(function(a){return{name:"user",Fa:"an instance of Firebase User",optional:!!a,Ca:function(b){return!!(b&&b instanceof R)}}}(),Vr(),"user")]},signOut:{name:"signOut",j:[]},toJSON:{name:"toJSON",j:[V(null,!0)]},useDeviceLanguage:{name:"useDeviceLanguage",j:[]},useEmulator:{name:"useEmulator",j:[V("url"),W("options",!0)]},
verifyPasswordResetCode:{name:"verifyPasswordResetCode",j:[V("code")]}});fs(T.prototype,{lc:{name:"languageCode",Zf:X(V(),Vr(),"languageCode")},ti:{name:"tenantId",Zf:X(V(),Vr(),"tenantId")}});T.Persistence=vp;T.Persistence.LOCAL="local";T.Persistence.SESSION="session";T.Persistence.NONE="none";
Y(R.prototype,{"delete":{name:"delete",j:[]},getIdTokenResult:{name:"getIdTokenResult",j:[Tr("opt_forceRefresh",!0)]},getIdToken:{name:"getIdToken",j:[Tr("opt_forceRefresh",!0)]},linkAndRetrieveDataWithCredential:{name:"linkAndRetrieveDataWithCredential",j:[Zr()]},linkWithCredential:{name:"linkWithCredential",j:[Zr()]},linkWithPhoneNumber:{name:"linkWithPhoneNumber",j:[V("phoneNumber"),ds()]},linkWithPopup:{name:"linkWithPopup",j:[as()]},linkWithRedirect:{name:"linkWithRedirect",j:[as()]},reauthenticateAndRetrieveDataWithCredential:{name:"reauthenticateAndRetrieveDataWithCredential",
j:[Zr()]},reauthenticateWithCredential:{name:"reauthenticateWithCredential",j:[Zr()]},reauthenticateWithPhoneNumber:{name:"reauthenticateWithPhoneNumber",j:[V("phoneNumber"),ds()]},reauthenticateWithPopup:{name:"reauthenticateWithPopup",j:[as()]},reauthenticateWithRedirect:{name:"reauthenticateWithRedirect",j:[as()]},reload:{name:"reload",j:[]},sendEmailVerification:{name:"sendEmailVerification",j:[X(W("opt_actionCodeSettings",!0),Vr(null,!0),"opt_actionCodeSettings",!0)]},toJSON:{name:"toJSON",j:[V(null,
!0)]},unlink:{name:"unlink",j:[V("provider")]},updateEmail:{name:"updateEmail",j:[V("email")]},updatePassword:{name:"updatePassword",j:[V("password")]},updatePhoneNumber:{name:"updatePhoneNumber",j:[Zr("phone")]},updateProfile:{name:"updateProfile",j:[W("profile")]},verifyBeforeUpdateEmail:{name:"verifyBeforeUpdateEmail",j:[V("email"),X(W("opt_actionCodeSettings",!0),Vr(null,!0),"opt_actionCodeSettings",!0)]}});Y(no.prototype,{execute:{name:"execute"},render:{name:"render"},reset:{name:"reset"},getResponse:{name:"getResponse"}});
Y(io.prototype,{execute:{name:"execute"},render:{name:"render"},reset:{name:"reset"},getResponse:{name:"getResponse"}});Y(F.prototype,{Ac:{name:"finally"},l:{name:"catch"},then:{name:"then"}});fs(gp.prototype,{appVerificationDisabled:{name:"appVerificationDisabledForTesting",Zf:Tr("appVerificationDisabledForTesting")}});Y(jp.prototype,{confirm:{name:"confirm",j:[V("verificationCode")]}});
Z(Wl,"fromJSON",function(a){a=typeof a==="string"?JSON.parse(a):a;for(var b,c=[dm,vm,Cm,bm],d=0;d<c.length;d++)if(b=c[d](a))return b;return null},[X(V(),W(),"json")]);Z(pm,"credential",function(a,b){return new qm(a,b)},[V("email"),V("password")]);Y(qm.prototype,{T:{name:"toJSON",j:[V(null,!0)]}});Y(hm.prototype,{addScope:{name:"addScope",j:[V("scope")]},setCustomParameters:{name:"setCustomParameters",j:[W("customOAuthParameters")]}});Z(hm,"credential",im,[X(V(),W(),"token")]);
Z(pm,"credentialWithLink",xm,[V("email"),V("emailLink")]);Y(jm.prototype,{addScope:{name:"addScope",j:[V("scope")]},setCustomParameters:{name:"setCustomParameters",j:[W("customOAuthParameters")]}});Z(jm,"credential",km,[X(V(),W(),"token")]);Y(lm.prototype,{addScope:{name:"addScope",j:[V("scope")]},setCustomParameters:{name:"setCustomParameters",j:[W("customOAuthParameters")]}});Z(lm,"credential",mm,[X(V(),X(W(),Vr()),"idToken"),X(V(),Vr(),"accessToken",!0)]);
Y(nm.prototype,{setCustomParameters:{name:"setCustomParameters",j:[W("customOAuthParameters")]}});Z(nm,"credential",om,[X(V(),W(),"token"),V("secret",!0)]);Y(gm.prototype,{addScope:{name:"addScope",j:[V("scope")]},credential:{name:"credential",j:[X(V(),X(W(),Vr()),"optionsOrIdToken"),X(V(),Vr(),"accessToken",!0)]},setCustomParameters:{name:"setCustomParameters",j:[W("customOAuthParameters")]}});Y(cm.prototype,{T:{name:"toJSON",j:[V(null,!0)]}});Y(Yl.prototype,{T:{name:"toJSON",j:[V(null,!0)]}});
Z(Dm,"credential",Hm,[V("verificationId"),V("verificationCode")]);
Y(Dm.prototype,{verifyPhoneNumber:{name:"verifyPhoneNumber",j:[X(V(),function(a,b){return{name:a||"phoneInfoOptions",Fa:"valid phone info options",optional:!!b,Ca:function(c){return c?c.session&&c.phoneNumber?bs(c.session,"enroll")&&typeof c.phoneNumber==="string":c.session&&c.multiFactorHint?bs(c.session,"signin")&&cs(c.multiFactorHint):c.session&&c.multiFactorUid?bs(c.session,"signin")&&typeof c.multiFactorUid==="string":c.phoneNumber?typeof c.phoneNumber==="string":!1:!1}}}(),"phoneInfoOptions"),
ds()]}});Y(ym.prototype,{T:{name:"toJSON",j:[V(null,!0)]}});Y(P.prototype,{toJSON:{name:"toJSON",j:[V(null,!0)]}});Y(Km.prototype,{toJSON:{name:"toJSON",j:[V(null,!0)]}});Y(Ql.prototype,{toJSON:{name:"toJSON",j:[V(null,!0)]}});Y(rq.prototype,{toJSON:{name:"toJSON",j:[V(null,!0)]}});Y(qq.prototype,{resolveSignIn:{name:"resolveSignIn",j:[$r()]}});
Y(Aq.prototype,{getSession:{name:"getSession",j:[]},enroll:{name:"enroll",j:[$r(),V("displayName",!0)]},unenroll:{name:"unenroll",j:[X({name:"multiFactorInfo",Fa:"a valid multiFactorInfo",optional:!1,Ca:cs},V(),"multiFactorInfoIdentifier")]}});Y(yo.prototype,{clear:{name:"clear",j:[]},render:{name:"render",j:[]},verify:{name:"verify",j:[]}});Z(Nl,"parseLink",Ol,[V("link")]);Z(hs,"assertion",function(a){return new xq(a)},[Zr("phone")]);
(function(){if(typeof firebase!=="undefined"&&firebase.INTERNAL&&firebase.INTERNAL.registerService){var a={ActionCodeInfo:{Operation:{EMAIL_SIGNIN:"EMAIL_SIGNIN",PASSWORD_RESET:"PASSWORD_RESET",RECOVER_EMAIL:"RECOVER_EMAIL",REVERT_SECOND_FACTOR_ADDITION:"REVERT_SECOND_FACTOR_ADDITION",VERIFY_AND_CHANGE_EMAIL:"VERIFY_AND_CHANGE_EMAIL",VERIFY_EMAIL:"VERIFY_EMAIL"}},Auth:T,AuthCredential:Wl,Error:P};Z(a,"EmailAuthProvider",pm,[]);Z(a,"FacebookAuthProvider",hm,[]);Z(a,"GithubAuthProvider",jm,[]);Z(a,
"GoogleAuthProvider",lm,[]);Z(a,"TwitterAuthProvider",nm,[]);Z(a,"OAuthProvider",gm,[V("providerId")]);Z(a,"SAMLAuthProvider",fm,[V("providerId")]);Z(a,"PhoneAuthProvider",Dm,[Xr()]);Z(a,"RecaptchaVerifier",yo,[X(V(),Wr(),"recaptchaContainer"),W("recaptchaParameters",!0),Yr()]);Z(a,"ActionCodeURL",Nl,[]);Z(a,"PhoneMultiFactorGenerator",hs,[]);firebase.INTERNAL.registerService("auth",function(b,c){b=new T(b);c({INTERNAL:{getUid:v(b.getUid,b),getToken:v(b.Gk,b),addAuthTokenListener:v(b.ak,b),removeAuthTokenListener:v(b.Jl,
b)}});return b},a,function(b,c){if(b==="create")try{c.auth()}catch(d){}});firebase.INTERNAL.extendNamespace({User:R})}else throw Error("Cannot find the firebase namespace; be sure to include firebase-app.js before this library.");})();var is=function(a){this.Vg=pg.getParentIframe();this.Ib=this.Vg.getOrigin();this.Hb=a;this.Eh=!1};is.prototype.od=function(){return this.Ib};is.prototype.start=function(){var a=this;return this.Hb(this.Ib).then(function(){a.Eh=!0}).l(function(b){throw b;})};is.prototype.sendMessage=function(a){var b=this;if(this.Eh)return new F(function(c){b.Vg.send(a.type,a,c,sg)});throw new P("missing-iframe-start");};
is.prototype.kh=function(a,b){if(this.Eh)this.Vg.register(a,b,sg);else throw new P("missing-iframe-start");};var js=function(a){this.zf=new is(a)};js.prototype.od=function(){return this.zf.od()};js.prototype.start=function(){var a=this;return this.zf.start().then(function(){a.lh()})};var ks=function(a,b){return a.zf.sendMessage({type:"authEvent",authEvent:b&&b.T()}).then(function(c){if(!c||!c.length||c[c.length-1].status!="ACK")throw new P("internal-error");})};js.prototype.lh=function(){this.zf.kh("webStorageSupport",function(){return G({status:"ACK",webStorageSupport:!0})})};var ls=function(a,b,c){var d=(qk(M(Dk()),"fw")||"").split(","),e=this;this.ha=a;this.ka=b;this.Ia=c||null;this.Da=d||[];this.Hd=new Ep(this.ha+":"+this.ka);this.vl=new Kp;this.o=new Sm(a,zk(this.Ia),Zk("Iframe","2.20.6",this.Da));this.qc=new js(function(f){return jn(e.o).then(function(g){if(!Nk(g,f))throw e.destroy(),new Ql(f);})});this.uc=Eo(r);this.Ei=!1;this.uc.subscribe("getParentOrigin",function(f){if(f===r.window.location.origin)return G(e.qc.od());throw Error("Invalid origin");
});this.uc.subscribe("sendAuthEvent",function(f,g){var h=g.storageKey,l=null;try{l=Gl(g.authEvent)}catch(m){}if(f===r.window.location.origin&&h===e.ha+":"+e.ka&&l)return e.Ei?ks(e.qc,l).then(function(){return!0}).l(function(){return!1}):e.vl.O.set(Fp,l.T(),h).then(function(){return!0}).l(function(){return!1});throw Error("Invalid origin or request");})};ls.prototype.od=function(){return this.qc.od()};
ls.prototype.start=function(){var a=this;return this.qc.start().then(function(){a.Ei=!0;a.cj=a.aj.bind(a);return ms(a).Ac(function(){a.Hd.Cc(a.cj);a.aj(!1)})})};ls.prototype.aj=function(a){var b=this,c=null;return Gp(this.Hd).then(function(d){if(c=d)return ks(b.qc,d);if(a)return ks(b.qc,new Fl("unknown",null,null,null,new P("no-auth-event")))}).then(function(){if(c)return Hp(b.Hd)}).l(function(){})};
var ms=function(a){var b=null;return Jp(a.Hd).then(function(c){if(b=c)return ks(a.qc,c);c=al()?"no-auth-event":"web-storage-unsupported";return ks(a.qc,new Fl("unknown",null,null,null,new P(c)))}).then(function(){if(b){var c=a.Hd;return c.O.remove(Ip,c.V)}}).l(function(){})};ls.prototype.destroy=function(){this.jd=!0;this.Hd.Ed(this.cj);this.uc.unsubscribe("getParentOrigin");this.uc.unsubscribe("sendAuthEvent")};
var ns=null,os=function(){var a=qk(M(Dk()),"apiKey"),b=qk(M(Dk()),"appName")||"";if(!a)throw new P("invalid-api-key");var c=qk(M(Dk()),"eid")||null;ns=new ls(a,b,c);ns.start().l(function(d){if(d&&d.code=="auth/unauthorized-domain")d=M(ns.od()),d=d.Va=="chrome-extension"?mc("Info: The current chrome extension ID is not authorized for OAuth operations. This will prevent signInWithPopup and linkWithPopup from working. Add your chrome extension (chrome-extension://%s) to the OAuth redirect domains list in the Firebase console -> Auth section -> Sign in method tab.",
d.Ga):mc("Info: The current domain is not authorized for OAuth operations. This will prevent signInWithPopup, signInWithRedirect, linkWithPopup and linkWithRedirect from working. Add your domain (%s) to the OAuth redirect domains list in the Firebase console -> Authentication -> Settings -> Authorized domains tab.",d.Ga),ml(d);else if(d&&d.message)ml(d.message);else throw d;})};t("fireauth.iframe.AuthRelay.initialize",function(){r.document.readyState=="complete"?os():Ui(window,"load",function(){os()})});}).call(this);
