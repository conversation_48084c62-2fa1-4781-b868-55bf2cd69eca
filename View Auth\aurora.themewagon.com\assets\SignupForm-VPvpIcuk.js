import{j as e,S as j,Q as r,bi as A,T as t,L as a,o as F,B as P,a0 as p,d as q,a as C}from"./index-CP4gzJXp.js";import{a as W}from"./index.esm-Bw9oClnr.js";import{o as k}from"./yup-Bdh5_3QG.js";import{c as B,a as n}from"./index.esm-CVsSWzb0.js";import{P as I}from"./PasswordTextField-Bu7nMFM0.js";import{V as L}from"./ViewOnlyAlert-CkXljFy_.js";import{S as E}from"./SocialAuth-DUKTxlfk.js";import{A as G}from"./Alert-BWvPB4gW.js";const V=B({name:n().required("This field is required"),email:n().email("Please provide a valid email address.").required("This field is required"),password:n().required("This field is required")}).required(),K=({provider:g="jwt",handleSignup:f,socialAuth:b=!0,loginLink:y})=>{var o,l,d,c,m,x,h;const{register:i,handleSubmit:v,setError:w,formState:{errors:s,isSubmitting:z}}=W({resolver:k(V)}),S=async T=>{await f(T).catch(u=>{u&&w("root.credential",{type:"manual",message:u.message})})};return e.jsxs(j,{direction:"column",sx:{height:1,alignItems:"center",justifyContent:"space-between",pt:{md:10},pb:10},children:[e.jsx("div",{}),e.jsxs(r,{container:!0,sx:{height:1,maxWidth:"35rem",rowGap:4,alignContent:{md:"center"},p:{xs:3,sm:5},mb:5},children:[g==="firebase"&&!0&&e.jsx(r,{size:12,sx:{mb:1},children:e.jsx(L,{docLink:`${A.authentication}#firebase`})}),e.jsx(r,{size:12,children:e.jsxs(j,{direction:{xs:"column",sm:"row"},spacing:1,sx:{justifyContent:"space-between",alignItems:{xs:"flex-start",sm:"flex-end"}},children:[e.jsx(t,{variant:"h4",children:"Sign up"}),e.jsxs(t,{variant:"subtitle2",sx:{color:"text.secondary"},children:["Already have an account?",e.jsx(a,{href:y,sx:{ml:1},children:"Log in"})]})]})}),b&&e.jsxs(e.Fragment,{children:[e.jsx(r,{size:12,children:e.jsx(E,{})}),e.jsx(r,{size:12,children:e.jsx(F,{sx:{color:"text.secondary"},children:"or use email"})})]}),e.jsx(r,{size:12,children:e.jsxs(P,{component:"form",noValidate:!0,onSubmit:v(S),children:[((l=(o=s.root)==null?void 0:o.credential)==null?void 0:l.message)&&e.jsx(G,{severity:"error",sx:{mb:3},children:(c=(d=s.root)==null?void 0:d.credential)==null?void 0:c.message}),e.jsxs(r,{container:!0,children:[e.jsx(r,{sx:{mb:3},size:12,children:e.jsx(p,{fullWidth:!0,size:"large",id:"name",type:"text",label:"Name",variant:"filled",error:!!s.name,helperText:e.jsx(e.Fragment,{children:(m=s.name)==null?void 0:m.message}),...i("name")})}),e.jsx(r,{sx:{mb:3},size:12,children:e.jsx(p,{fullWidth:!0,size:"large",id:"email",type:"email",label:"Email",variant:"filled",error:!!s.email,helperText:e.jsx(e.Fragment,{children:(x=s.email)==null?void 0:x.message}),...i("email")})}),e.jsx(r,{sx:{mb:4},size:12,children:e.jsx(I,{fullWidth:!0,size:"large",id:"password",label:"Password",variant:"filled",error:!!s.password,helperText:e.jsx(e.Fragment,{children:(h=s.password)==null?void 0:h.message}),...i("password")})}),e.jsx(r,{sx:{mb:2.5},size:12,children:e.jsxs(t,{variant:"body2",sx:{color:"text.secondary"},children:[e.jsx(q,{icon:"material-symbols:info-outline-rounded",fontSize:16,color:"warning.main",sx:{verticalAlign:"text-bottom"}})," ","This site is protected by reCAPTCHA and the Google Privacy Policy and Terms of Service apply. By clicking the Create Account button, you are agreeing to the"," ",e.jsx(a,{href:"#!",children:"terms and conditions."})]})}),e.jsx(r,{size:12,children:e.jsx(C,{loading:z,fullWidth:!0,type:"submit",size:"large",variant:"contained",children:"Create Account"})})]})]})})]}),e.jsx(a,{href:"#!",variant:"subtitle2",sx:{flex:1},children:"Trouble signing in?"})]})};export{K as S};
