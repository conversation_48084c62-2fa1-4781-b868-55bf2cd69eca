import{c0 as b,r as it}from"./index-CP4gzJXp.js";var he=e=>e.type==="checkbox",ue=e=>e instanceof Date,j=e=>e==null;const _t=e=>typeof e=="object";var B=e=>!j(e)&&!Array.isArray(e)&&_t(e)&&!ue(e),gt=e=>B(e)&&e.target?he(e.target)?e.target.checked:e.target.value:e,It=e=>e.substring(0,e.search(/\.\d+(\.|$)/))||e,ht=(e,s)=>e.has(It(s)),Bt=e=>{const s=e.constructor&&e.constructor.prototype;return B(s)&&s.hasOwnProperty("isPrototypeOf")},He=typeof window<"u"&&typeof window.HTMLElement<"u"&&typeof document<"u";function U(e){let s;const t=Array.isArray(e),i=typeof FileList<"u"?e instanceof FileList:!1;if(e instanceof Date)s=new Date(e);else if(e instanceof Set)s=new Set(e);else if(!(He&&(e instanceof Blob||i))&&(t||B(e)))if(s=t?[]:{},!t&&!Bt(e))s=e;else for(const n in e)e.hasOwnProperty(n)&&(s[n]=U(e[n]));else return e;return s}var ve=e=>Array.isArray(e)?e.filter(Boolean):[],M=e=>e===void 0,c=(e,s,t)=>{if(!s||!B(e))return t;const i=ve(s.split(/[,[\].]+?/)).reduce((n,l)=>j(n)?n:n[l],e);return M(i)||i===e?M(e[s])?t:e[s]:i},Q=e=>typeof e=="boolean",je=e=>/^\w*$/.test(e),vt=e=>ve(e.replace(/["|']|\]/g,"").split(/\.|\[/)),E=(e,s,t)=>{let i=-1;const n=je(s)?[s]:vt(s),l=n.length,d=l-1;for(;++i<l;){const y=n[i];let m=t;if(i!==d){const x=e[y];m=B(x)||Array.isArray(x)?x:isNaN(+n[i+1])?{}:[]}if(y==="__proto__"||y==="constructor"||y==="prototype")return;e[y]=m,e=e[y]}};const Ve={BLUR:"blur",FOCUS_OUT:"focusout",CHANGE:"change"},Z={onBlur:"onBlur",onChange:"onChange",onSubmit:"onSubmit",onTouched:"onTouched",all:"all"},ie={max:"max",min:"min",maxLength:"maxLength",minLength:"minLength",pattern:"pattern",required:"required",validate:"validate"},bt=b.createContext(null),we=()=>b.useContext(bt),ar=e=>{const{children:s,...t}=e;return b.createElement(bt.Provider,{value:t},s)};var mt=(e,s,t,i=!0)=>{const n={defaultValues:s._defaultValues};for(const l in e)Object.defineProperty(n,l,{get:()=>{const d=l;return s._proxyFormState[d]!==Z.all&&(s._proxyFormState[d]=!i||Z.all),t&&(t[d]=!0),e[d]}});return n};const $e=typeof window<"u"?it.useLayoutEffect:it.useEffect;function Pt(e){const s=we(),{control:t=s.control,disabled:i,name:n,exact:l}=e||{},[d,y]=b.useState(t._formState),m=b.useRef({isDirty:!1,isLoading:!1,dirtyFields:!1,touchedFields:!1,validatingFields:!1,isValidating:!1,isValid:!1,errors:!1});return $e(()=>t._subscribe({name:n,formState:m.current,exact:l,callback:x=>{!i&&y({...t._formState,...x})}}),[n,i,l]),b.useEffect(()=>{m.current.isValid&&t._setValid(!0)},[t]),b.useMemo(()=>mt(d,t,m.current,!1),[d,t])}var se=e=>typeof e=="string",Ft=(e,s,t,i,n)=>se(e)?(i&&s.watch.add(e),c(t,e,n)):Array.isArray(e)?e.map(l=>(i&&s.watch.add(l),c(t,l))):(i&&(s.watchAll=!0),t);function Wt(e){const s=we(),{control:t=s.control,name:i,defaultValue:n,disabled:l,exact:d}=e||{},y=b.useRef(n),[m,x]=b.useState(t._getWatch(i,y.current));return $e(()=>t._subscribe({name:i,formState:{values:!0},exact:d,callback:k=>!l&&x(Ft(i,t._names,k.values||t._formValues,!1,y.current))}),[i,t,l,d]),b.useEffect(()=>t._removeUnmounted()),m}function qt(e){const s=we(),{name:t,disabled:i,control:n=s.control,shouldUnregister:l}=e,d=ht(n._names.array,t),y=Wt({control:n,name:t,defaultValue:c(n._formValues,t,c(n._defaultValues,t,e.defaultValue)),exact:!0}),m=Pt({control:n,name:t,exact:!0}),x=b.useRef(e),k=b.useRef(n.register(t,{...e.rules,value:y,...Q(e.disabled)?{disabled:e.disabled}:{}})),C=b.useMemo(()=>Object.defineProperties({},{invalid:{enumerable:!0,get:()=>!!c(m.errors,t)},isDirty:{enumerable:!0,get:()=>!!c(m.dirtyFields,t)},isTouched:{enumerable:!0,get:()=>!!c(m.touchedFields,t)},isValidating:{enumerable:!0,get:()=>!!c(m.validatingFields,t)},error:{enumerable:!0,get:()=>c(m.errors,t)}}),[m,t]),F=b.useCallback(T=>k.current.onChange({target:{value:gt(T),name:t},type:Ve.CHANGE}),[t]),R=b.useCallback(()=>k.current.onBlur({target:{value:c(n._formValues,t),name:t},type:Ve.BLUR}),[t,n._formValues]),z=b.useCallback(T=>{const ee=c(n._fields,t);ee&&T&&(ee._f.ref={focus:()=>T.focus&&T.focus(),select:()=>T.select&&T.select(),setCustomValidity:V=>T.setCustomValidity(V),reportValidity:()=>T.reportValidity()})},[n._fields,t]),L=b.useMemo(()=>({name:t,value:y,...Q(i)||m.disabled?{disabled:m.disabled||i}:{},onChange:F,onBlur:R,ref:z}),[t,i,m.disabled,F,R,z,y]);return b.useEffect(()=>{const T=n._options.shouldUnregister||l;n.register(t,{...x.current.rules,...Q(x.current.disabled)?{disabled:x.current.disabled}:{}});const ee=(V,G)=>{const W=c(n._fields,V);W&&W._f&&(W._f.mount=G)};if(ee(t,!0),T){const V=U(c(n._options.defaultValues,t));E(n._defaultValues,t,V),M(c(n._formValues,t))&&E(n._formValues,t,V)}return!d&&n.register(t),()=>{(d?T&&!n._state.action:T)?n.unregister(t):ee(t,!1)}},[t,n,d,l]),b.useEffect(()=>{n._setDisabledField({disabled:i,name:t})},[i,t,n]),b.useMemo(()=>({field:L,formState:m,fieldState:C}),[L,m,C])}const nr=e=>e.render(qt(e));var Ht=(e,s,t,i,n)=>s?{...t[e],types:{...t[e]&&t[e].types?t[e].types:{},[i]:n||!0}}:{},K=e=>Array.isArray(e)?e:[e],at=()=>{let e=[];return{get observers(){return e},next:n=>{for(const l of e)l.next&&l.next(n)},subscribe:n=>(e.push(n),{unsubscribe:()=>{e=e.filter(l=>l!==n)}}),unsubscribe:()=>{e=[]}}},Pe=e=>j(e)||!_t(e);function le(e,s){if(Pe(e)||Pe(s))return e===s;if(ue(e)&&ue(s))return e.getTime()===s.getTime();const t=Object.keys(e),i=Object.keys(s);if(t.length!==i.length)return!1;for(const n of t){const l=e[n];if(!i.includes(n))return!1;if(n!=="ref"){const d=s[n];if(ue(l)&&ue(d)||B(l)&&B(d)||Array.isArray(l)&&Array.isArray(d)?!le(l,d):l!==d)return!1}}return!0}var H=e=>B(e)&&!Object.keys(e).length,Ke=e=>e.type==="file",te=e=>typeof e=="function",Ae=e=>{if(!He)return!1;const s=e?e.ownerDocument:0;return e instanceof(s&&s.defaultView?s.defaultView.HTMLElement:HTMLElement)},Vt=e=>e.type==="select-multiple",ze=e=>e.type==="radio",jt=e=>ze(e)||he(e),Oe=e=>Ae(e)&&e.isConnected;function $t(e,s){const t=s.slice(0,-1).length;let i=0;for(;i<t;)e=M(e)?i++:e[s[i++]];return e}function Kt(e){for(const s in e)if(e.hasOwnProperty(s)&&!M(e[s]))return!1;return!0}function P(e,s){const t=Array.isArray(s)?s:je(s)?[s]:vt(s),i=t.length===1?e:$t(e,t),n=t.length-1,l=t[n];return i&&delete i[l],n!==0&&(B(i)&&H(i)||Array.isArray(i)&&Kt(i))&&P(e,t.slice(0,-1)),e}var At=e=>{for(const s in e)if(te(e[s]))return!0;return!1};function xe(e,s={}){const t=Array.isArray(e);if(B(e)||t)for(const i in e)Array.isArray(e[i])||B(e[i])&&!At(e[i])?(s[i]=Array.isArray(e[i])?[]:{},xe(e[i],s[i])):j(e[i])||(s[i]=!0);return s}function xt(e,s,t){const i=Array.isArray(e);if(B(e)||i)for(const n in e)Array.isArray(e[n])||B(e[n])&&!At(e[n])?M(s)||Pe(t[n])?t[n]=Array.isArray(e[n])?xe(e[n],[]):{...xe(e[n])}:xt(e[n],j(s)?{}:s[n],t[n]):t[n]=!le(e[n],s[n]);return t}var _e=(e,s)=>xt(e,s,xe(s));const nt={value:!1,isValid:!1},lt={value:!0,isValid:!0};var St=e=>{if(Array.isArray(e)){if(e.length>1){const s=e.filter(t=>t&&t.checked&&!t.disabled).map(t=>t.value);return{value:s,isValid:!!s.length}}return e[0].checked&&!e[0].disabled?e[0].attributes&&!M(e[0].attributes.value)?M(e[0].value)||e[0].value===""?lt:{value:e[0].value,isValid:!0}:lt:nt}return nt},wt=(e,{valueAsNumber:s,valueAsDate:t,setValueAs:i})=>M(e)?e:s?e===""?NaN:e&&+e:t&&se(e)?new Date(e):i?i(e):e;const ut={isValid:!1,value:null};var kt=e=>Array.isArray(e)?e.reduce((s,t)=>t&&t.checked&&!t.disabled?{isValid:!0,value:t.value}:s,ut):ut;function ot(e){const s=e.ref;return Ke(s)?s.files:ze(s)?kt(e.refs).value:Vt(s)?[...s.selectedOptions].map(({value:t})=>t):he(s)?St(e.refs).value:wt(M(s.value)?e.ref.value:s.value,e)}var zt=(e,s,t,i)=>{const n={};for(const l of e){const d=c(s,l);d&&E(n,l,d._f)}return{criteriaMode:t,names:[...e],fields:n,shouldUseNativeValidation:i}},Se=e=>e instanceof RegExp,ge=e=>M(e)?e:Se(e)?e.source:B(e)?Se(e.value)?e.value.source:e.value:e,de=e=>({isOnSubmit:!e||e===Z.onSubmit,isOnBlur:e===Z.onBlur,isOnChange:e===Z.onChange,isOnAll:e===Z.all,isOnTouch:e===Z.onTouched});const ct="AsyncFunction";var Gt=e=>!!e&&!!e.validate&&!!(te(e.validate)&&e.validate.constructor.name===ct||B(e.validate)&&Object.values(e.validate).find(s=>s.constructor.name===ct)),Yt=e=>e.mount&&(e.required||e.min||e.max||e.maxLength||e.minLength||e.pattern||e.validate),We=(e,s,t)=>!t&&(s.watchAll||s.watch.has(e)||[...s.watch].some(i=>e.startsWith(i)&&/^\.\w+/.test(e.slice(i.length))));const fe=(e,s,t,i)=>{for(const n of t||Object.keys(e)){const l=c(e,n);if(l){const{_f:d,...y}=l;if(d){if(d.refs&&d.refs[0]&&s(d.refs[0],n)&&!i)return!0;if(d.ref&&s(d.ref,d.name)&&!i)return!0;if(fe(y,s))break}else if(B(y)&&fe(y,s))break}}};function dt(e,s,t){const i=c(e,t);if(i||je(t))return{error:i,name:t};const n=t.split(".");for(;n.length;){const l=n.join("."),d=c(s,l),y=c(e,l);if(d&&!Array.isArray(d)&&t!==l)return{name:t};if(y&&y.type)return{name:l,error:y};if(y&&y.root&&y.root.type)return{name:`${l}.root`,error:y.root};n.pop()}return{name:t}}var Jt=(e,s,t,i)=>{t(e);const{name:n,...l}=e;return H(l)||Object.keys(l).length>=Object.keys(s).length||Object.keys(l).find(d=>s[d]===(!i||Z.all))},Qt=(e,s,t)=>!e||!s||e===s||K(e).some(i=>i&&(t?i===s:i.startsWith(s)||s.startsWith(i))),Xt=(e,s,t,i,n)=>n.isOnAll?!1:!t&&n.isOnTouch?!(s||e):(t?i.isOnBlur:n.isOnBlur)?!e:(t?i.isOnChange:n.isOnChange)?e:!0,Zt=(e,s)=>!ve(c(e,s)).length&&P(e,s),Dt=(e,s,t)=>{const i=K(c(e,t));return E(i,"root",s[t]),E(e,t,i),e},Fe=e=>se(e);function ft(e,s,t="validate"){if(Fe(e)||Array.isArray(e)&&e.every(Fe)||Q(e)&&!e)return{type:t,message:Fe(e)?e:"",ref:s}}var ce=e=>B(e)&&!Se(e)?e:{value:e,message:""},qe=async(e,s,t,i,n,l)=>{const{ref:d,refs:y,required:m,maxLength:x,minLength:k,min:C,max:F,pattern:R,validate:z,name:L,valueAsNumber:T,mount:ee}=e._f,V=c(t,L);if(!ee||s.has(L))return{};const G=y?y[0]:d,W=w=>{n&&G.reportValidity&&(G.setCustomValidity(Q(w)?"":w||""),G.reportValidity())},N={},v=ze(d),g=he(d),A=v||g,S=(T||Ke(d))&&M(d.value)&&M(V)||Ae(d)&&d.value===""||V===""||Array.isArray(V)&&!V.length,Y=Ht.bind(null,L,i,N),J=(w,p,I,q=ie.maxLength,$=ie.minLength)=>{const re=w?p:I;N[L]={type:w?q:$,message:re,ref:d,...Y(w?q:$,re)}};if(l?!Array.isArray(V)||!V.length:m&&(!A&&(S||j(V))||Q(V)&&!V||g&&!St(y).isValid||v&&!kt(y).isValid)){const{value:w,message:p}=Fe(m)?{value:!!m,message:m}:ce(m);if(w&&(N[L]={type:ie.required,message:p,ref:G,...Y(ie.required,p)},!i))return W(p),N}if(!S&&(!j(C)||!j(F))){let w,p;const I=ce(F),q=ce(C);if(!j(V)&&!isNaN(V)){const $=d.valueAsNumber||V&&+V;j(I.value)||(w=$>I.value),j(q.value)||(p=$<q.value)}else{const $=d.valueAsDate||new Date(V),re=be=>new Date(new Date().toDateString()+" "+be),ye=d.type=="time",oe=d.type=="week";se(I.value)&&V&&(w=ye?re(V)>re(I.value):oe?V>I.value:$>new Date(I.value)),se(q.value)&&V&&(p=ye?re(V)<re(q.value):oe?V<q.value:$<new Date(q.value))}if((w||p)&&(J(!!w,I.message,q.message,ie.max,ie.min),!i))return W(N[L].message),N}if((x||k)&&!S&&(se(V)||l&&Array.isArray(V))){const w=ce(x),p=ce(k),I=!j(w.value)&&V.length>+w.value,q=!j(p.value)&&V.length<+p.value;if((I||q)&&(J(I,w.message,p.message),!i))return W(N[L].message),N}if(R&&!S&&se(V)){const{value:w,message:p}=ce(R);if(Se(w)&&!V.match(w)&&(N[L]={type:ie.pattern,message:p,ref:d,...Y(ie.pattern,p)},!i))return W(p),N}if(z){if(te(z)){const w=await z(V,t),p=ft(w,G);if(p&&(N[L]={...p,...Y(ie.validate,p.message)},!i))return W(p.message),N}else if(B(z)){let w={};for(const p in z){if(!H(w)&&!i)break;const I=ft(await z[p](V,t),G,p);I&&(w={...I,...Y(p,I.message)},W(I.message),i&&(N[L]=w))}if(!H(w)&&(N[L]={ref:G,...w},!i))return N}}return W(!0),N};const er={mode:Z.onSubmit,reValidateMode:Z.onChange,shouldFocusError:!0};function tr(e={}){let s={...er,...e},t={submitCount:0,isDirty:!1,isReady:!1,isLoading:te(s.defaultValues),isValidating:!1,isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,touchedFields:{},dirtyFields:{},validatingFields:{},errors:s.errors||{},disabled:s.disabled||!1};const i={};let n=B(s.defaultValues)||B(s.values)?U(s.defaultValues||s.values)||{}:{},l=s.shouldUnregister?{}:U(n),d={action:!1,mount:!1,watch:!1},y={mount:new Set,disabled:new Set,unMount:new Set,array:new Set,watch:new Set},m,x=0;const k={isDirty:!1,dirtyFields:!1,validatingFields:!1,touchedFields:!1,isValidating:!1,isValid:!1,errors:!1};let C={...k};const F={array:at(),state:at()},R=s.criteriaMode===Z.all,z=r=>a=>{clearTimeout(x),x=setTimeout(r,a)},L=async r=>{if(!s.disabled&&(k.isValid||C.isValid||r)){const a=s.resolver?H((await g()).errors):await S(i,!0);a!==t.isValid&&F.state.next({isValid:a})}},T=(r,a)=>{!s.disabled&&(k.isValidating||k.validatingFields||C.isValidating||C.validatingFields)&&((r||Array.from(y.mount)).forEach(u=>{u&&(a?E(t.validatingFields,u,a):P(t.validatingFields,u))}),F.state.next({validatingFields:t.validatingFields,isValidating:!H(t.validatingFields)}))},ee=(r,a=[],u,_,f=!0,o=!0)=>{if(_&&u&&!s.disabled){if(d.action=!0,o&&Array.isArray(c(i,r))){const h=u(c(i,r),_.argA,_.argB);f&&E(i,r,h)}if(o&&Array.isArray(c(t.errors,r))){const h=u(c(t.errors,r),_.argA,_.argB);f&&E(t.errors,r,h),Zt(t.errors,r)}if((k.touchedFields||C.touchedFields)&&o&&Array.isArray(c(t.touchedFields,r))){const h=u(c(t.touchedFields,r),_.argA,_.argB);f&&E(t.touchedFields,r,h)}(k.dirtyFields||C.dirtyFields)&&(t.dirtyFields=_e(n,l)),F.state.next({name:r,isDirty:J(r,a),dirtyFields:t.dirtyFields,errors:t.errors,isValid:t.isValid})}else E(l,r,a)},V=(r,a)=>{E(t.errors,r,a),F.state.next({errors:t.errors})},G=r=>{t.errors=r,F.state.next({errors:t.errors,isValid:!1})},W=(r,a,u,_)=>{const f=c(i,r);if(f){const o=c(l,r,M(u)?c(n,r):u);M(o)||_&&_.defaultChecked||a?E(l,r,a?o:ot(f._f)):I(r,o),d.mount&&L()}},N=(r,a,u,_,f)=>{let o=!1,h=!1;const D={name:r};if(!s.disabled){if(!u||_){(k.isDirty||C.isDirty)&&(h=t.isDirty,t.isDirty=D.isDirty=J(),o=h!==D.isDirty);const O=le(c(n,r),a);h=!!c(t.dirtyFields,r),O?P(t.dirtyFields,r):E(t.dirtyFields,r,!0),D.dirtyFields=t.dirtyFields,o=o||(k.dirtyFields||C.dirtyFields)&&h!==!O}if(u){const O=c(t.touchedFields,r);O||(E(t.touchedFields,r,u),D.touchedFields=t.touchedFields,o=o||(k.touchedFields||C.touchedFields)&&O!==u)}o&&f&&F.state.next(D)}return o?D:{}},v=(r,a,u,_)=>{const f=c(t.errors,r),o=(k.isValid||C.isValid)&&Q(a)&&t.isValid!==a;if(s.delayError&&u?(m=z(()=>V(r,u)),m(s.delayError)):(clearTimeout(x),m=null,u?E(t.errors,r,u):P(t.errors,r)),(u?!le(f,u):f)||!H(_)||o){const h={..._,...o&&Q(a)?{isValid:a}:{},errors:t.errors,name:r};t={...t,...h},F.state.next(h)}},g=async r=>{T(r,!0);const a=await s.resolver(l,s.context,zt(r||y.mount,i,s.criteriaMode,s.shouldUseNativeValidation));return T(r),a},A=async r=>{const{errors:a}=await g(r);if(r)for(const u of r){const _=c(a,u);_?E(t.errors,u,_):P(t.errors,u)}else t.errors=a;return a},S=async(r,a,u={valid:!0})=>{for(const _ in r){const f=r[_];if(f){const{_f:o,...h}=f;if(o){const D=y.array.has(o.name),O=f._f&&Gt(f._f);O&&k.validatingFields&&T([_],!0);const X=await qe(f,y.disabled,l,R,s.shouldUseNativeValidation&&!a,D);if(O&&k.validatingFields&&T([_]),X[o.name]&&(u.valid=!1,a))break;!a&&(c(X,o.name)?D?Dt(t.errors,X,o.name):E(t.errors,o.name,X[o.name]):P(t.errors,o.name))}!H(h)&&await S(h,a,u)}}return u.valid},Y=()=>{for(const r of y.unMount){const a=c(i,r);a&&(a._f.refs?a._f.refs.every(u=>!Oe(u)):!Oe(a._f.ref))&&ke(r)}y.unMount=new Set},J=(r,a)=>!s.disabled&&(r&&a&&E(l,r,a),!le(be(),n)),w=(r,a,u)=>Ft(r,y,{...d.mount?l:M(a)?n:se(r)?{[r]:a}:a},u,a),p=r=>ve(c(d.mount?l:n,r,s.shouldUnregister?c(n,r,[]):[])),I=(r,a,u={})=>{const _=c(i,r);let f=a;if(_){const o=_._f;o&&(!o.disabled&&E(l,r,wt(a,o)),f=Ae(o.ref)&&j(a)?"":a,Vt(o.ref)?[...o.ref.options].forEach(h=>h.selected=f.includes(h.value)):o.refs?he(o.ref)?o.refs.forEach(h=>{(!h.defaultChecked||!h.disabled)&&(Array.isArray(f)?h.checked=!!f.find(D=>D===h.value):h.checked=f===h.value||!!f)}):o.refs.forEach(h=>h.checked=h.value===f):Ke(o.ref)?o.ref.value="":(o.ref.value=f,o.ref.type||F.state.next({name:r,values:U(l)})))}(u.shouldDirty||u.shouldTouch)&&N(r,f,u.shouldTouch,u.shouldDirty,!0),u.shouldValidate&&oe(r)},q=(r,a,u)=>{for(const _ in a){if(!a.hasOwnProperty(_))return;const f=a[_],o=r+"."+_,h=c(i,o);(y.array.has(r)||B(f)||h&&!h._f)&&!ue(f)?q(o,f,u):I(o,f,u)}},$=(r,a,u={})=>{const _=c(i,r),f=y.array.has(r),o=U(a);E(l,r,o),f?(F.array.next({name:r,values:U(l)}),(k.isDirty||k.dirtyFields||C.isDirty||C.dirtyFields)&&u.shouldDirty&&F.state.next({name:r,dirtyFields:_e(n,l),isDirty:J(r,o)})):_&&!_._f&&!j(o)?q(r,o,u):I(r,o,u),We(r,y)&&F.state.next({...t}),F.state.next({name:d.mount?r:void 0,values:U(l)})},re=async r=>{d.mount=!0;const a=r.target;let u=a.name,_=!0;const f=c(i,u),o=O=>{_=Number.isNaN(O)||ue(O)&&isNaN(O.getTime())||le(O,c(l,u,O))},h=de(s.mode),D=de(s.reValidateMode);if(f){let O,X;const me=a.type?ot(f._f):gt(r),ae=r.type===Ve.BLUR||r.type===Ve.FOCUS_OUT,Tt=!Yt(f._f)&&!s.resolver&&!c(t.errors,u)&&!f._f.deps||Xt(ae,c(t.touchedFields,u),t.isSubmitted,D,h),pe=We(u,y,ae);E(l,u,me),ae?(f._f.onBlur&&f._f.onBlur(r),m&&m(0)):f._f.onChange&&f._f.onChange(r);const Ce=N(u,me,ae),Ut=!H(Ce)||pe;if(!ae&&F.state.next({name:u,type:r.type,values:U(l)}),Tt)return(k.isValid||C.isValid)&&(s.mode==="onBlur"?ae&&L():ae||L()),Ut&&F.state.next({name:u,...pe?{}:Ce});if(!ae&&pe&&F.state.next({...t}),s.resolver){const{errors:rt}=await g([u]);if(o(me),_){const Nt=dt(t.errors,i,u),st=dt(rt,i,Nt.name||u);O=st.error,u=st.name,X=H(rt)}}else T([u],!0),O=(await qe(f,y.disabled,l,R,s.shouldUseNativeValidation))[u],T([u]),o(me),_&&(O?X=!1:(k.isValid||C.isValid)&&(X=await S(i,!0)));_&&(f._f.deps&&oe(f._f.deps),v(u,X,O,Ce))}},ye=(r,a)=>{if(c(t.errors,a)&&r.focus)return r.focus(),1},oe=async(r,a={})=>{let u,_;const f=K(r);if(s.resolver){const o=await A(M(r)?r:f);u=H(o),_=r?!f.some(h=>c(o,h)):u}else r?(_=(await Promise.all(f.map(async o=>{const h=c(i,o);return await S(h&&h._f?{[o]:h}:h)}))).every(Boolean),!(!_&&!t.isValid)&&L()):_=u=await S(i);return F.state.next({...!se(r)||(k.isValid||C.isValid)&&u!==t.isValid?{}:{name:r},...s.resolver||!r?{isValid:u}:{},errors:t.errors}),a.shouldFocus&&!_&&fe(i,ye,r?f:y.mount),_},be=r=>{const a={...d.mount?l:n};return M(r)?a:se(r)?c(a,r):r.map(u=>c(a,u))},Ge=(r,a)=>({invalid:!!c((a||t).errors,r),isDirty:!!c((a||t).dirtyFields,r),error:c((a||t).errors,r),isValidating:!!c(t.validatingFields,r),isTouched:!!c((a||t).touchedFields,r)}),Et=r=>{r&&K(r).forEach(a=>P(t.errors,a)),F.state.next({errors:r?t.errors:{}})},Ye=(r,a,u)=>{const _=(c(i,r,{_f:{}})._f||{}).ref,f=c(t.errors,r)||{},{ref:o,message:h,type:D,...O}=f;E(t.errors,r,{...O,...a,ref:_}),F.state.next({name:r,errors:t.errors,isValid:!1}),u&&u.shouldFocus&&_&&_.focus&&_.focus()},pt=(r,a)=>te(r)?F.state.subscribe({next:u=>r(w(void 0,a),u)}):w(r,a,!0),Je=r=>F.state.subscribe({next:a=>{Qt(r.name,a.name,r.exact)&&Jt(a,r.formState||k,Lt,r.reRenderRoot)&&r.callback({values:{...l},...t,...a})}}).unsubscribe,Ct=r=>(d.mount=!0,C={...C,...r.formState},Je({...r,formState:C})),ke=(r,a={})=>{for(const u of r?K(r):y.mount)y.mount.delete(u),y.array.delete(u),a.keepValue||(P(i,u),P(l,u)),!a.keepError&&P(t.errors,u),!a.keepDirty&&P(t.dirtyFields,u),!a.keepTouched&&P(t.touchedFields,u),!a.keepIsValidating&&P(t.validatingFields,u),!s.shouldUnregister&&!a.keepDefaultValue&&P(n,u);F.state.next({values:U(l)}),F.state.next({...t,...a.keepDirty?{isDirty:J()}:{}}),!a.keepIsValid&&L()},Qe=({disabled:r,name:a})=>{(Q(r)&&d.mount||r||y.disabled.has(a))&&(r?y.disabled.add(a):y.disabled.delete(a))},De=(r,a={})=>{let u=c(i,r);const _=Q(a.disabled)||Q(s.disabled);return E(i,r,{...u||{},_f:{...u&&u._f?u._f:{ref:{name:r}},name:r,mount:!0,...a}}),y.mount.add(r),u?Qe({disabled:Q(a.disabled)?a.disabled:s.disabled,name:r}):W(r,!0,a.value),{..._?{disabled:a.disabled||s.disabled}:{},...s.progressive?{required:!!a.required,min:ge(a.min),max:ge(a.max),minLength:ge(a.minLength),maxLength:ge(a.maxLength),pattern:ge(a.pattern)}:{},name:r,onChange:re,onBlur:re,ref:f=>{if(f){De(r,a),u=c(i,r);const o=M(f.value)&&f.querySelectorAll&&f.querySelectorAll("input,select,textarea")[0]||f,h=jt(o),D=u._f.refs||[];if(h?D.find(O=>O===o):o===u._f.ref)return;E(i,r,{_f:{...u._f,...h?{refs:[...D.filter(Oe),o,...Array.isArray(c(n,r))?[{}]:[]],ref:{type:o.type,name:r}}:{ref:o}}}),W(r,!1,void 0,o)}else u=c(i,r,{}),u._f&&(u._f.mount=!1),(s.shouldUnregister||a.shouldUnregister)&&!(ht(y.array,r)&&d.action)&&y.unMount.add(r)}}},Ee=()=>s.shouldFocusError&&fe(i,ye,y.mount),Ot=r=>{Q(r)&&(F.state.next({disabled:r}),fe(i,(a,u)=>{const _=c(i,u);_&&(a.disabled=_._f.disabled||r,Array.isArray(_._f.refs)&&_._f.refs.forEach(f=>{f.disabled=_._f.disabled||r}))},0,!1))},Xe=(r,a)=>async u=>{let _;u&&(u.preventDefault&&u.preventDefault(),u.persist&&u.persist());let f=U(l);if(F.state.next({isSubmitting:!0}),s.resolver){const{errors:o,values:h}=await g();t.errors=o,f=h}else await S(i);if(y.disabled.size)for(const o of y.disabled)E(f,o,void 0);if(P(t.errors,"root"),H(t.errors)){F.state.next({errors:{}});try{await r(f,u)}catch(o){_=o}}else a&&await a({...t.errors},u),Ee(),setTimeout(Ee);if(F.state.next({isSubmitted:!0,isSubmitting:!1,isSubmitSuccessful:H(t.errors)&&!_,submitCount:t.submitCount+1,errors:t.errors}),_)throw _},Rt=(r,a={})=>{c(i,r)&&(M(a.defaultValue)?$(r,U(c(n,r))):($(r,a.defaultValue),E(n,r,U(a.defaultValue))),a.keepTouched||P(t.touchedFields,r),a.keepDirty||(P(t.dirtyFields,r),t.isDirty=a.defaultValue?J(r,U(c(n,r))):J()),a.keepError||(P(t.errors,r),k.isValid&&L()),F.state.next({...t}))},Ze=(r,a={})=>{const u=r?U(r):n,_=U(u),f=H(r),o=f?n:_;if(a.keepDefaultValues||(n=u),!a.keepValues){if(a.keepDirtyValues){const h=new Set([...y.mount,...Object.keys(_e(n,l))]);for(const D of Array.from(h))c(t.dirtyFields,D)?E(o,D,c(l,D)):$(D,c(o,D))}else{if(He&&M(r))for(const h of y.mount){const D=c(i,h);if(D&&D._f){const O=Array.isArray(D._f.refs)?D._f.refs[0]:D._f.ref;if(Ae(O)){const X=O.closest("form");if(X){X.reset();break}}}}for(const h of y.mount)$(h,c(o,h))}l=U(o),F.array.next({values:{...o}}),F.state.next({values:{...o}})}y={mount:a.keepDirtyValues?y.mount:new Set,unMount:new Set,array:new Set,disabled:new Set,watch:new Set,watchAll:!1,focus:""},d.mount=!k.isValid||!!a.keepIsValid||!!a.keepDirtyValues,d.watch=!!s.shouldUnregister,F.state.next({submitCount:a.keepSubmitCount?t.submitCount:0,isDirty:f?!1:a.keepDirty?t.isDirty:!!(a.keepDefaultValues&&!le(r,n)),isSubmitted:a.keepIsSubmitted?t.isSubmitted:!1,dirtyFields:f?{}:a.keepDirtyValues?a.keepDefaultValues&&l?_e(n,l):t.dirtyFields:a.keepDefaultValues&&r?_e(n,r):a.keepDirty?t.dirtyFields:{},touchedFields:a.keepTouched?t.touchedFields:{},errors:a.keepErrors?t.errors:{},isSubmitSuccessful:a.keepIsSubmitSuccessful?t.isSubmitSuccessful:!1,isSubmitting:!1})},et=(r,a)=>Ze(te(r)?r(l):r,a),Mt=(r,a={})=>{const u=c(i,r),_=u&&u._f;if(_){const f=_.refs?_.refs[0]:_.ref;f.focus&&(f.focus(),a.shouldSelect&&te(f.select)&&f.select())}},Lt=r=>{t={...t,...r}},tt={control:{register:De,unregister:ke,getFieldState:Ge,handleSubmit:Xe,setError:Ye,_subscribe:Je,_runSchema:g,_focusError:Ee,_getWatch:w,_getDirty:J,_setValid:L,_setFieldArray:ee,_setDisabledField:Qe,_setErrors:G,_getFieldArray:p,_reset:Ze,_resetDefaultValues:()=>te(s.defaultValues)&&s.defaultValues().then(r=>{et(r,s.resetOptions),F.state.next({isLoading:!1})}),_removeUnmounted:Y,_disableForm:Ot,_subjects:F,_proxyFormState:k,get _fields(){return i},get _formValues(){return l},get _state(){return d},set _state(r){d=r},get _defaultValues(){return n},get _names(){return y},set _names(r){y=r},get _formState(){return t},get _options(){return s},set _options(r){s={...s,...r}}},subscribe:Ct,trigger:oe,register:De,handleSubmit:Xe,watch:pt,setValue:$,getValues:be,reset:et,resetField:Rt,clearErrors:Et,unregister:ke,setError:Ye,setFocus:Mt,getFieldState:Ge};return{...tt,formControl:tt}}var ne=()=>{const e=typeof performance>"u"?Date.now():performance.now()*1e3;return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,s=>{const t=(Math.random()*16+e)%16|0;return(s=="x"?t:t&3|8).toString(16)})},Re=(e,s,t={})=>t.shouldFocus||M(t.shouldFocus)?t.focusName||`${e}.${M(t.focusIndex)?s:t.focusIndex}.`:"",Me=(e,s)=>[...e,...K(s)],Le=e=>Array.isArray(e)?e.map(()=>{}):void 0;function Te(e,s,t){return[...e.slice(0,s),...K(t),...e.slice(s)]}var Ue=(e,s,t)=>Array.isArray(e)?(M(e[t])&&(e[t]=void 0),e.splice(t,0,e.splice(s,1)[0]),e):[],Ne=(e,s)=>[...K(s),...K(e)];function rr(e,s){let t=0;const i=[...e];for(const n of s)i.splice(n-t,1),t++;return ve(i).length?i:[]}var Ie=(e,s)=>M(s)?[]:rr(e,K(s).sort((t,i)=>t-i)),Be=(e,s,t)=>{[e[s],e[t]]=[e[t],e[s]]},yt=(e,s,t)=>(e[s]=t,e);function lr(e){const s=we(),{control:t=s.control,name:i,keyName:n="id",shouldUnregister:l,rules:d}=e,[y,m]=b.useState(t._getFieldArray(i)),x=b.useRef(t._getFieldArray(i).map(ne)),k=b.useRef(y),C=b.useRef(i),F=b.useRef(!1);C.current=i,k.current=y,t._names.array.add(i),d&&t.register(i,d),b.useEffect(()=>t._subjects.array.subscribe({next:({values:v,name:g})=>{if(g===C.current||!g){const A=c(v,C.current);Array.isArray(A)&&(m(A),x.current=A.map(ne))}}}).unsubscribe,[t]);const R=b.useCallback(v=>{F.current=!0,t._setFieldArray(i,v)},[t,i]),z=(v,g)=>{const A=K(U(v)),S=Me(t._getFieldArray(i),A);t._names.focus=Re(i,S.length-1,g),x.current=Me(x.current,A.map(ne)),R(S),m(S),t._setFieldArray(i,S,Me,{argA:Le(v)})},L=(v,g)=>{const A=K(U(v)),S=Ne(t._getFieldArray(i),A);t._names.focus=Re(i,0,g),x.current=Ne(x.current,A.map(ne)),R(S),m(S),t._setFieldArray(i,S,Ne,{argA:Le(v)})},T=v=>{const g=Ie(t._getFieldArray(i),v);x.current=Ie(x.current,v),R(g),m(g),!Array.isArray(c(t._fields,i))&&E(t._fields,i,void 0),t._setFieldArray(i,g,Ie,{argA:v})},ee=(v,g,A)=>{const S=K(U(g)),Y=Te(t._getFieldArray(i),v,S);t._names.focus=Re(i,v,A),x.current=Te(x.current,v,S.map(ne)),R(Y),m(Y),t._setFieldArray(i,Y,Te,{argA:v,argB:Le(g)})},V=(v,g)=>{const A=t._getFieldArray(i);Be(A,v,g),Be(x.current,v,g),R(A),m(A),t._setFieldArray(i,A,Be,{argA:v,argB:g},!1)},G=(v,g)=>{const A=t._getFieldArray(i);Ue(A,v,g),Ue(x.current,v,g),R(A),m(A),t._setFieldArray(i,A,Ue,{argA:v,argB:g},!1)},W=(v,g)=>{const A=U(g),S=yt(t._getFieldArray(i),v,A);x.current=[...S].map((Y,J)=>!Y||J===v?ne():x.current[J]),R(S),m([...S]),t._setFieldArray(i,S,yt,{argA:v,argB:A},!0,!1)},N=v=>{const g=K(U(v));x.current=g.map(ne),R([...g]),m([...g]),t._setFieldArray(i,[...g],A=>A,{},!0,!1)};return b.useEffect(()=>{if(t._state.action=!1,We(i,t._names)&&t._subjects.state.next({...t._formState}),F.current&&(!de(t._options.mode).isOnSubmit||t._formState.isSubmitted)&&!de(t._options.reValidateMode).isOnSubmit)if(t._options.resolver)t._runSchema([i]).then(v=>{const g=c(v.errors,i),A=c(t._formState.errors,i);(A?!g&&A.type||g&&(A.type!==g.type||A.message!==g.message):g&&g.type)&&(g?E(t._formState.errors,i,g):P(t._formState.errors,i),t._subjects.state.next({errors:t._formState.errors}))});else{const v=c(t._fields,i);v&&v._f&&!(de(t._options.reValidateMode).isOnSubmit&&de(t._options.mode).isOnSubmit)&&qe(v,t._names.disabled,t._formValues,t._options.criteriaMode===Z.all,t._options.shouldUseNativeValidation,!0).then(g=>!H(g)&&t._subjects.state.next({errors:Dt(t._formState.errors,g,i)}))}t._subjects.state.next({name:i,values:U(t._formValues)}),t._names.focus&&fe(t._fields,(v,g)=>{if(t._names.focus&&g.startsWith(t._names.focus)&&v.focus)return v.focus(),1}),t._names.focus="",t._setValid(),F.current=!1},[y,i,t]),b.useEffect(()=>(!c(t._formValues,i)&&t._setFieldArray(i),()=>{const v=(g,A)=>{const S=c(t._fields,g);S&&S._f&&(S._f.mount=A)};t._options.shouldUnregister||l?t.unregister(i):v(i,!1)}),[i,t,n,l]),{swap:b.useCallback(V,[R,i,t]),move:b.useCallback(G,[R,i,t]),prepend:b.useCallback(L,[R,i,t]),append:b.useCallback(z,[R,i,t]),remove:b.useCallback(T,[R,i,t]),insert:b.useCallback(ee,[R,i,t]),update:b.useCallback(W,[R,i,t]),replace:b.useCallback(N,[R,i,t]),fields:b.useMemo(()=>y.map((v,g)=>({...v,[n]:x.current[g]||ne()})),[y,n])}}function ur(e={}){const s=b.useRef(void 0),t=b.useRef(void 0),[i,n]=b.useState({isDirty:!1,isValidating:!1,isLoading:te(e.defaultValues),isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,submitCount:0,dirtyFields:{},touchedFields:{},validatingFields:{},errors:e.errors||{},disabled:e.disabled||!1,isReady:!1,defaultValues:te(e.defaultValues)?void 0:e.defaultValues});s.current||(s.current={...e.formControl?e.formControl:tr(e),formState:i},e.formControl&&e.defaultValues&&!te(e.defaultValues)&&e.formControl.reset(e.defaultValues,e.resetOptions));const l=s.current.control;return l._options=e,$e(()=>{const d=l._subscribe({formState:l._proxyFormState,callback:()=>n({...l._formState}),reRenderRoot:!0});return n(y=>({...y,isReady:!0})),l._formState.isReady=!0,d},[l]),b.useEffect(()=>l._disableForm(e.disabled),[l,e.disabled]),b.useEffect(()=>{e.mode&&(l._options.mode=e.mode),e.reValidateMode&&(l._options.reValidateMode=e.reValidateMode)},[l,e.mode,e.reValidateMode]),b.useEffect(()=>{e.errors&&(l._setErrors(e.errors),l._focusError())},[l,e.errors]),b.useEffect(()=>{e.shouldUnregister&&l._subjects.state.next({values:l._getWatch()})},[l,e.shouldUnregister]),b.useEffect(()=>{if(l._proxyFormState.isDirty){const d=l._getDirty();d!==i.isDirty&&l._subjects.state.next({isDirty:d})}},[l,i.isDirty]),b.useEffect(()=>{e.values&&!le(e.values,t.current)?(l._reset(e.values,l._options.resetOptions),t.current=e.values,n(d=>({...d}))):l._resetDefaultValues()},[l,e.values]),b.useEffect(()=>{l._state.mount||(l._setValid(),l._state.mount=!0),l._state.watch&&(l._state.watch=!1,l._subjects.state.next({...l._formState})),l._removeUnmounted()}),s.current.formState=mt(i,l),s.current}export{nr as C,ar as F,ur as a,Wt as b,lr as c,Ht as d,c as g,E as s,we as u};
