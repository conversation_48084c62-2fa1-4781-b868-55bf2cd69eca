
// Copyright 2012 Google Inc. All rights reserved.
 
(function(){

var data = {
"resource": {
  "version":"1",
  
  "macros":[{"function":"__e"},{"function":"__c","vtp_value":"google.com.vn"},{"function":"__c","vtp_value":0}],
  "tags":[{"function":"__ogt_1p_data_v2","priority":13,"vtp_isAutoEnabled":true,"vtp_autoCollectExclusionSelectors":["list",["map","exclusionSelector",""]],"vtp_isEnabled":true,"vtp_manualEmailEnabled":false,"vtp_cityValue":"","vtp_autoEmailEnabled":true,"vtp_postalCodeValue":"","vtp_lastNameValue":"","vtp_phoneValue":"","vtp_autoPhoneEnabled":false,"vtp_emailValue":"","vtp_firstNameValue":"","vtp_streetValue":"","vtp_autoAddressEnabled":false,"vtp_regionValue":"","vtp_countryValue":"","vtp_isAutoCollectPiiEnabledFlag":false,"tag_id":4},{"function":"__ccd_ga_first","priority":12,"vtp_instanceDestinationId":"G-SMNVEQYP6G","tag_id":18},{"function":"__set_product_settings","priority":11,"vtp_instanceDestinationId":"G-SMNVEQYP6G","vtp_foreignTldMacroResult":["macro",1],"vtp_isChinaVipRegionMacroResult":["macro",2],"tag_id":17},{"function":"__ccd_ga_regscope","priority":10,"vtp_settingsTable":["list",["map","redactFieldGroup","DEVICE_AND_GEO","disallowAllRegions",false,"disallowedRegions",""],["map","redactFieldGroup","GOOGLE_SIGNALS","disallowAllRegions",true,"disallowedRegions",""]],"vtp_instanceDestinationId":"G-SMNVEQYP6G","tag_id":16},{"function":"__ccd_em_download","priority":9,"vtp_includeParams":true,"vtp_instanceDestinationId":"G-SMNVEQYP6G","tag_id":15},{"function":"__ccd_em_form","priority":8,"vtp_includeParams":true,"vtp_instanceDestinationId":"G-SMNVEQYP6G","tag_id":14},{"function":"__ccd_em_outbound_click","priority":7,"vtp_includeParams":true,"vtp_instanceDestinationId":"G-SMNVEQYP6G","tag_id":13},{"function":"__ccd_em_page_view","priority":6,"vtp_historyEvents":true,"vtp_includeParams":true,"vtp_instanceDestinationId":"G-SMNVEQYP6G","tag_id":12},{"function":"__ccd_em_scroll","priority":5,"vtp_includeParams":true,"vtp_instanceDestinationId":"G-SMNVEQYP6G","tag_id":11},{"function":"__ccd_em_site_search","priority":4,"vtp_searchQueryParams":"q,s,search,query,keyword","vtp_includeParams":true,"vtp_instanceDestinationId":"G-SMNVEQYP6G","tag_id":10},{"function":"__ccd_em_video","priority":3,"vtp_includeParams":true,"vtp_instanceDestinationId":"G-SMNVEQYP6G","tag_id":9},{"function":"__ccd_conversion_marking","priority":2,"vtp_conversionRules":["list",["map","matchingRules","{\"type\":5,\"args\":[{\"stringValue\":\"purchase\"},{\"contextValue\":{\"namespaceType\":1,\"keyParts\":[\"eventName\"]}}]}"]],"vtp_instanceDestinationId":"G-SMNVEQYP6G","tag_id":8},{"function":"__ccd_auto_redact","priority":1,"vtp_redactEmail":true,"vtp_instanceDestinationId":"G-SMNVEQYP6G","tag_id":7},{"function":"__gct","vtp_trackingId":"G-SMNVEQYP6G","vtp_sessionDuration":0,"tag_id":1},{"function":"__ccd_ga_last","priority":0,"vtp_instanceDestinationId":"G-SMNVEQYP6G","tag_id":6}],
  "predicates":[{"function":"_eq","arg0":["macro",0],"arg1":"gtm.js"},{"function":"_eq","arg0":["macro",0],"arg1":"gtm.init"}],
  "rules":[[["if",0],["add",13]],[["if",1],["add",0,14,12,11,10,9,8,7,6,5,4,3,2,1]]]
},
"runtime":[ [50,"__c",[46,"a"],[36,[17,[15,"a"],"value"]]]
 ,[50,"__ccd_auto_redact",[46,"a"],[50,"u",[46,"aI"],[36,[2,[15,"aI"],"replace",[7,[15,"t"],"\\$1"]]]],[50,"v",[46,"aI"],[52,"aJ",[30,["c",[15,"aI"]],[15,"aI"]]],[52,"aK",[7]],[65,"aL",[2,[15,"aJ"],"split",[7,""]],[46,[53,[52,"aM",[7,["u",[15,"aL"]]]],[52,"aN",["d",[15,"aL"]]],[22,[12,[15,"aN"],[45]],[46,[53,[36,["d",["u",[15,"aI"]]]]]]],[22,[21,[15,"aN"],[15,"aL"]],[46,[53,[2,[15,"aM"],"push",[7,[15,"aN"]]],[22,[21,[15,"aL"],[2,[15,"aL"],"toLowerCase",[7]]],[46,[53,[2,[15,"aM"],"push",[7,["d",[2,[15,"aL"],"toLowerCase",[7]]]]]]],[46,[22,[21,[15,"aL"],[2,[15,"aL"],"toUpperCase",[7]]],[46,[53,[2,[15,"aM"],"push",[7,["d",[2,[15,"aL"],"toUpperCase",[7]]]]]]]]]]]]],[22,[18,[17,[15,"aM"],"length"],1],[46,[53,[2,[15,"aK"],"push",[7,[0,[0,"(?:",[2,[15,"aM"],"join",[7,"|"]]],")"]]]]],[46,[53,[2,[15,"aK"],"push",[7,[16,[15,"aM"],0]]]]]]]]],[36,[2,[15,"aK"],"join",[7,""]]]],[50,"w",[46,"aI","aJ","aK"],[52,"aL",["y",[15,"aI"],[15,"aK"]]],[22,[28,[15,"aL"]],[46,[36,[15,"aI"]]]],[22,[28,[17,[15,"aL"],"search"]],[46,[36,[15,"aI"]]]],[41,"aM"],[3,"aM",[17,[15,"aL"],"search"]],[65,"aN",[15,"aJ"],[46,[53,[52,"aO",[7,["u",[15,"aN"]],["v",[15,"aN"]]]],[65,"aP",[15,"aO"],[46,[53,[52,"aQ",[30,[16,[15,"s"],[15,"aP"]],[43,[15,"s"],[15,"aP"],["b",[0,[0,"([?&]",[15,"aP"]],"=)([^&]*)"],"gi"]]]],[3,"aM",[2,[15,"aM"],"replace",[7,[15,"aQ"],[0,"$1",[15,"q"]]]]]]]]]]],[22,[20,[15,"aM"],[17,[15,"aL"],"search"]],[46,[36,[15,"aI"]]]],[22,[20,[16,[15,"aM"],0],"&"],[46,[3,"aM",[2,[15,"aM"],"substring",[7,1]]]]],[22,[21,[16,[15,"aM"],0],"?"],[46,[3,"aM",[0,"?",[15,"aM"]]]]],[22,[20,[15,"aM"],"?"],[46,[3,"aM",""]]],[43,[15,"aL"],"search",[15,"aM"]],[36,["z",[15,"aL"],[15,"aK"]]]],[50,"y",[46,"aI","aJ"],[22,[20,[15,"aJ"],[17,[15,"r"],"PATH"]],[46,[53,[3,"aI",[0,[15,"x"],[15,"aI"]]]]]],[36,["f",[15,"aI"]]]],[50,"z",[46,"aI","aJ"],[41,"aK"],[3,"aK",""],[22,[20,[15,"aJ"],[17,[15,"r"],"URL"]],[46,[53,[41,"aL"],[3,"aL",""],[22,[30,[17,[15,"aI"],"username"],[17,[15,"aI"],"password"]],[46,[53,[3,"aL",[0,[15,"aL"],[0,[0,[0,[17,[15,"aI"],"username"],[39,[17,[15,"aI"],"password"],":",""]],[17,[15,"aI"],"password"]],"@"]]]]]],[3,"aK",[0,[0,[0,[17,[15,"aI"],"protocol"],"//"],[15,"aL"]],[17,[15,"aI"],"host"]]]]]],[36,[0,[0,[0,[15,"aK"],[17,[15,"aI"],"pathname"]],[17,[15,"aI"],"search"]],[17,[15,"aI"],"hash"]]]],[50,"aA",[46,"aI","aJ"],[41,"aK"],[3,"aK",[2,[15,"aI"],"replace",[7,[15,"m"],[15,"q"]]]],[22,[30,[20,[15,"aJ"],[17,[15,"r"],"URL"]],[20,[15,"aJ"],[17,[15,"r"],"PATH"]]],[46,[53,[52,"aL",["y",[15,"aK"],[15,"aJ"]]],[22,[20,[15,"aL"],[44]],[46,[36,[15,"aK"]]]],[52,"aM",[17,[15,"aL"],"search"]],[52,"aN",[2,[15,"aM"],"replace",[7,[15,"n"],[15,"q"]]]],[22,[20,[15,"aM"],[15,"aN"]],[46,[36,[15,"aK"]]]],[43,[15,"aL"],"search",[15,"aN"]],[3,"aK",["z",[15,"aL"],[15,"aJ"]]]]]],[36,[15,"aK"]]],[50,"aB",[46,"aI"],[22,[20,[15,"aI"],[15,"p"]],[46,[53,[36,[17,[15,"r"],"PATH"]]]],[46,[22,[21,[2,[15,"o"],"indexOf",[7,[15,"aI"]]],[27,1]],[46,[53,[36,[17,[15,"r"],"URL"]]]],[46,[53,[36,[17,[15,"r"],"TEXT"]]]]]]]],[50,"aC",[46,"aI","aJ"],[41,"aK"],[3,"aK",false],[52,"aL",["e",[15,"aI"]]],[38,[15,"aL"],[46,"string","array","object"],[46,[5,[46,[52,"aM",["aA",[15,"aI"],[15,"aJ"]]],[22,[21,[15,"aI"],[15,"aM"]],[46,[53,[36,[15,"aM"]]]]],[4]]],[5,[46,[53,[41,"aN"],[3,"aN",0],[63,[7,"aN"],[23,[15,"aN"],[17,[15,"aI"],"length"]],[33,[15,"aN"],[3,"aN",[0,[15,"aN"],1]]],[46,[53,[52,"aO",["aC",[16,[15,"aI"],[15,"aN"]],[17,[15,"r"],"TEXT"]]],[22,[21,[15,"aO"],[44]],[46,[53,[43,[15,"aI"],[15,"aN"],[15,"aO"]],[3,"aK",true]]]]]]]],[4]]],[5,[46,[54,"aN",[15,"aI"],[46,[53,[52,"aO",["aC",[16,[15,"aI"],[15,"aN"]],[17,[15,"r"],"TEXT"]]],[22,[21,[15,"aO"],[44]],[46,[53,[43,[15,"aI"],[15,"aN"],[15,"aO"]],[3,"aK",true]]]]]]],[4]]]]],[36,[39,[15,"aK"],[15,"aI"],[44]]]],[50,"aH",[46,"aI","aJ"],[52,"aK",[30,[2,[15,"aI"],"getMetadata",[7,[17,[15,"h"],"V"]]],[7]]],[22,[20,[2,[15,"aK"],"indexOf",[7,[15,"aJ"]]],[27,1]],[46,[53,[2,[15,"aK"],"push",[7,[15,"aJ"]]]]]],[2,[15,"aI"],"setMetadata",[7,[17,[15,"h"],"V"],[15,"aK"]]]],[52,"b",["require","internal.createRegex"]],[52,"c",["require","decodeUriComponent"]],[52,"d",["require","encodeUriComponent"]],[52,"e",["require","getType"]],[52,"f",["require","parseUrl"]],[52,"g",["require","internal.registerCcdCallback"]],[52,"h",[15,"__module_metadataSchema"]],[52,"i",[17,[15,"a"],"instanceDestinationId"]],[52,"j",[17,[15,"a"],"redactEmail"]],[52,"k",[17,[15,"a"],"redactQueryParams"]],[52,"l",[39,[15,"k"],[2,[15,"k"],"split",[7,","]],[7]]],[22,[1,[28,[17,[15,"l"],"length"]],[28,[15,"j"]]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[52,"m",["b","[A-Z0-9._%+-]+@[A-Z0-9.-]+\\.[A-Z]{2,}","gi"]],[52,"n",["b",[0,"([A-Z0-9._-]|%25|%2B)+%40[A-Z0-9.-]","+\\.[A-Z]{2,}"],"gi"]],[52,"o",[7,"page_location","page_referrer","page_path","link_url","video_url","form_destination"]],[52,"p","page_path"],[52,"q","(redacted)"],[52,"r",[8,"TEXT",0,"URL",1,"PATH",2]],[52,"s",[8]],[52,"t",["b","([\\\\^$.|?*+(){}]|\\[|\\[)","g"]],[52,"x","http://."],[52,"aD",15],[52,"aE",16],[52,"aF",23],[52,"aG",24],["g",[15,"i"],[51,"",[7,"aI"],[22,[15,"j"],[46,[53,[52,"aJ",[2,[15,"aI"],"getHitKeys",[7]]],[65,"aK",[15,"aJ"],[46,[53,[22,[20,[15,"aK"],"_sst_parameters"],[46,[6]]],[52,"aL",[2,[15,"aI"],"getHitData",[7,[15,"aK"]]]],[22,[28,[15,"aL"]],[46,[6]]],[52,"aM",["aB",[15,"aK"]]],[52,"aN",["aC",[15,"aL"],[15,"aM"]]],[22,[21,[15,"aN"],[44]],[46,[53,[2,[15,"aI"],"setHitData",[7,[15,"aK"],[15,"aN"]]],["aH",[15,"aI"],[39,[2,[15,"aI"],"getMetadata",[7,[17,[15,"h"],"BA"]]],[15,"aF"],[15,"aD"]]]]]]]]]]]],[22,[17,[15,"l"],"length"],[46,[53,[65,"aJ",[15,"o"],[46,[53,[52,"aK",[2,[15,"aI"],"getHitData",[7,[15,"aJ"]]]],[22,[28,[15,"aK"]],[46,[6]]],[52,"aL",[39,[20,[15,"aJ"],[15,"p"]],[17,[15,"r"],"PATH"],[17,[15,"r"],"URL"]]],[52,"aM",["w",[15,"aK"],[15,"l"],[15,"aL"]]],[22,[21,[15,"aM"],[15,"aK"]],[46,[53,[2,[15,"aI"],"setHitData",[7,[15,"aJ"],[15,"aM"]]],["aH",[15,"aI"],[39,[2,[15,"aI"],"getMetadata",[7,[17,[15,"h"],"BA"]]],[15,"aG"],[15,"aE"]]]]]]]]]]]]]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ccd_conversion_marking",[46,"a"],[22,[30,[28,[17,[15,"a"],"conversionRules"]],[20,[17,[17,[15,"a"],"conversionRules"],"length"],0]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[52,"b",["require","internal.copyPreHit"]],[52,"c",["require","internal.evaluateBooleanExpression"]],[52,"d",["require","internal.registerCcdCallback"]],[52,"e",[15,"__module_metadataSchema"]],[52,"f","first_visit"],[52,"g","session_start"],[41,"h"],[41,"i"],["d",[17,[15,"a"],"instanceDestinationId"],[51,"",[7,"j"],[52,"k",[8,"preHit",[15,"j"]]],[65,"l",[17,[15,"a"],"conversionRules"],[46,[53,[22,["c",[17,[15,"l"],"matchingRules"],[15,"k"]],[46,[53,[2,[15,"j"],"setMetadata",[7,[17,[15,"e"],"AJ"],true]],[4]]]]]]],[22,[2,[15,"j"],"getMetadata",[7,[17,[15,"e"],"AN"]]],[46,[53,[22,[28,[15,"h"]],[46,[53,[52,"l",["b",[15,"j"],[8,"omitHitData",true,"omitMetadata",true]]],[2,[15,"l"],"setEventName",[7,[15,"f"]]],[3,"h",[8,"preHit",[15,"l"]]]]]],[65,"l",[17,[15,"a"],"conversionRules"],[46,[53,[22,["c",[17,[15,"l"],"matchingRules"],[15,"h"]],[46,[53,[2,[15,"j"],"setMetadata",[7,[17,[15,"e"],"AO"],true]],[4]]]]]]]]]],[22,[2,[15,"j"],"getMetadata",[7,[17,[15,"e"],"AX"]]],[46,[53,[22,[28,[15,"i"]],[46,[53,[52,"l",["b",[15,"j"],[8,"omitHitData",true,"omitMetadata",true]]],[2,[15,"l"],"setEventName",[7,[15,"g"]]],[3,"i",[8,"preHit",[15,"l"]]]]]],[65,"l",[17,[15,"a"],"conversionRules"],[46,[53,[22,["c",[17,[15,"l"],"matchingRules"],[15,"i"]],[46,[53,[2,[15,"j"],"setMetadata",[7,[17,[15,"e"],"AY"],true]],[4]]]]]]]]]]]],[2,[15,"a"],"gtmOnSuccess",[7]],[36]]
 ,[50,"__ccd_em_download",[46,"a"],[50,"q",[46,"w"],[36,[1,[15,"w"],[21,[2,[2,[15,"w"],"toLowerCase",[7]],"match",[7,[15,"p"]]],[45]]]]],[50,"r",[46,"w"],[52,"x",[2,[17,[15,"w"],"pathname"],"split",[7,"."]]],[52,"y",[39,[18,[17,[15,"x"],"length"],1],[16,[15,"x"],[37,[17,[15,"x"],"length"],1]],""]],[36,[16,[2,[15,"y"],"split",[7,"/"]],0]]],[50,"s",[46,"w"],[36,[39,[12,[2,[17,[15,"w"],"pathname"],"substring",[7,0,1]],"/"],[17,[15,"w"],"pathname"],[0,"/",[17,[15,"w"],"pathname"]]]]],[50,"t",[46,"w"],[41,"x"],[3,"x",""],[22,[1,[15,"w"],[17,[15,"w"],"href"]],[46,[53,[41,"y"],[3,"y",[2,[17,[15,"w"],"href"],"indexOf",[7,"#"]]],[3,"x",[39,[23,[15,"y"],0],[17,[15,"w"],"href"],[2,[17,[15,"w"],"href"],"substring",[7,0,[15,"y"]]]]]]]],[36,[15,"x"]]],[50,"v",[46,"w"],[52,"x",[8]],[43,[15,"x"],[15,"i"],true],[43,[15,"x"],[15,"e"],true],[43,[15,"w"],"eventMetadata",[15,"x"]]],[52,"b",["require","internal.getProductSettingsParameter"]],[52,"c",["require","templateStorage"]],[52,"d",[15,"__module_ccdEmDownloadActivity"]],[52,"e","speculative"],[52,"f","ae_block_downloads"],[52,"g","file_download"],[52,"h","isRegistered"],[52,"i","em_event"],[52,"j",[17,[15,"a"],"instanceDestinationId"]],[22,["b",[15,"j"],[15,"f"]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[2,[15,"d"],"A",[7,[15,"j"],[17,[15,"a"],"includeParams"]]],[22,[2,[15,"c"],"getItem",[7,[15,"h"]]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[52,"k",["require","internal.addDataLayerEventListener"]],[52,"l",["require","internal.enableAutoEventOnLinkClick"]],[52,"m",["require","internal.getDestinationIds"]],[52,"n",["require","parseUrl"]],[52,"o",["require","internal.sendGtagEvent"]],[52,"p",[0,"^(pdf|xlsx?|docx?|txt|rtf|csv|exe|key|pp(s|t|tx)|7z|pkg|rar|gz|zip|avi|","mov|mp4|mpe?g|wmv|midi?|mp3|wav|wma)$"]],[52,"u",["l",[8,"checkValidation",true]]],[22,[28,[15,"u"]],[46,[53,[2,[15,"a"],"gtmOnFailure",[7]],[36]]]],[2,[15,"c"],"setItem",[7,[15,"h"],true]],["k","gtm.linkClick",[51,"",[7,"w","x"],["x"],[52,"y",[8,"eventId",[16,[15,"w"],"gtm.uniqueEventId"],"deferrable",true]],[52,"z",[16,[15,"w"],"gtm.elementUrl"]],[52,"aA",["n",[15,"z"]]],[22,[28,[15,"aA"]],[46,[36]]],[52,"aB",["r",[15,"aA"]]],[22,[28,["q",[15,"aB"]]],[46,[53,[36]]]],[52,"aC",[8,"link_id",[16,[15,"w"],"gtm.elementId"],"link_url",["t",[15,"aA"]],"link_text",[16,[15,"w"],"gtm.elementText"],"file_name",["s",[15,"aA"]],"file_extension",[15,"aB"]]],["v",[15,"y"]],["o",["m"],[15,"g"],[15,"aC"],[15,"y"]]],[15,"u"]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ccd_em_form",[46,"a"],[50,"t",[46,"aA"],[52,"aB",[30,[16,[15,"aA"],[15,"m"]],[8]]],[43,[15,"aB"],"event_usage",[7,8]],[43,[15,"aA"],[15,"m"],[15,"aB"]]],[50,"u",[46,"aA","aB"],[52,"aC",[30,[16,[15,"aA"],[15,"m"]],[8]]],[43,[15,"aC"],[15,"l"],true],[43,[15,"aC"],[15,"g"],true],[22,[16,[15,"aB"],"gtm.formCanceled"],[46,[53,[43,[15,"aC"],[15,"n"],true]]]],[43,[15,"aA"],[15,"m"],[15,"aC"]]],[50,"v",[46,"aA","aB","aC"],[52,"aD",[2,["r"],"filter",[7,[51,"",[7,"aF"],[36,[20,[2,[15,"aF"],"indexOf",[7,"AW-"]],0]]]]]],[22,[18,[17,[15,"aD"],"length"],0],[46,[53,["s",[15,"aD"],[15,"aA"],[15,"aB"],[15,"aC"]]]]],[52,"aE",[2,["r"],"filter",[7,[51,"",[7,"aF"],[36,[21,[2,[15,"aF"],"indexOf",[7,"AW-"]],0]]]]]],[22,[18,[17,[15,"aE"],"length"],0],[46,[53,[43,[15,"aC"],"deferrable",true],["s",[15,"aE"],[15,"aA"],[15,"aB"],[15,"aC"]]]]]],[52,"b",["require","internal.isFeatureEnabled"]],[52,"c",[15,"__module_featureFlags"]],[52,"d",["require","internal.getProductSettingsParameter"]],[52,"e",["require","templateStorage"]],[52,"f",[15,"__module_ccdEmFormActivity"]],[52,"g","speculative"],[52,"h","ae_block_form"],[52,"i","form_submit"],[52,"j","form_start"],[52,"k","isRegistered"],[52,"l","em_event"],[52,"m","eventMetadata"],[52,"n","form_event_canceled"],[52,"o",[17,[15,"a"],"instanceDestinationId"]],[22,["d",[15,"o"],[15,"h"]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[2,[15,"f"],"A",[7,[17,[15,"a"],"instanceDestinationId"],[17,[15,"a"],"skipValidation"],[17,[15,"a"],"includeParams"]]],[22,[2,[15,"e"],"getItem",[7,[15,"k"]]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[2,[15,"e"],"setItem",[7,[15,"k"],true]],[52,"p",["require","internal.addFormInteractionListener"]],[52,"q",["require","internal.addFormSubmitListener"]],[52,"r",["require","internal.getDestinationIds"]],[52,"s",["require","internal.sendGtagEvent"]],[52,"w",[8]],[52,"x",[51,"",[7,"aA","aB"],[22,[15,"aB"],[46,["aB"]]],[52,"aC",[16,[15,"aA"],"gtm.elementId"]],[22,[16,[15,"w"],[15,"aC"]],[46,[36]]],[43,[15,"w"],[15,"aC"],true],[52,"aD",[8,"form_id",[15,"aC"],"form_name",[16,[15,"aA"],"gtm.interactedFormName"],"form_destination",[16,[15,"aA"],"gtm.elementUrl"],"form_length",[16,[15,"aA"],"gtm.interactedFormLength"],"first_field_id",[16,[15,"aA"],"gtm.interactedFormFieldId"],"first_field_name",[16,[15,"aA"],"gtm.interactedFormFieldName"],"first_field_type",[16,[15,"aA"],"gtm.interactedFormFieldType"],"first_field_position",[16,[15,"aA"],"gtm.interactedFormFieldPosition"]]],[52,"aE",[8,"eventId",[17,[15,"a"],"gtmEventId"]]],["t",[15,"aE"]],["u",[15,"aE"],[15,"aA"]],["v",[15,"j"],[15,"aD"],[15,"aE"]]]],[52,"y",["b",[17,[15,"c"],"DH"]]],[52,"z",[51,"",[7,"aA","aB"],["x",[15,"aA"],[44]],[52,"aC",[8,"form_id",[16,[15,"aA"],"gtm.elementId"],"form_name",[16,[15,"aA"],"gtm.interactedFormName"],"form_destination",[16,[15,"aA"],"gtm.elementUrl"],"form_length",[16,[15,"aA"],"gtm.interactedFormLength"],"form_submit_text",[39,[15,"y"],[16,[15,"aA"],"gtm.formSubmitElementText"],[16,[15,"aA"],"gtm.formSubmitButtonText"]]]],[43,[15,"aC"],"event_callback",[15,"aB"]],[52,"aD",[8,"eventId",[17,[15,"a"],"gtmEventId"]]],["t",[15,"aD"]],["u",[15,"aD"],[15,"aA"]],["v",[15,"i"],[15,"aC"],[15,"aD"]]]],[22,[15,"y"],[46,[53,[52,"aA",["require","internal.addDataLayerEventListener"]],[52,"aB",["require","internal.enableAutoEventOnFormSubmit"]],[52,"aC",["require","internal.enableAutoEventOnFormInteraction"]],[52,"aD",["aC"]],[22,[28,[15,"aD"]],[46,[53,[2,[15,"a"],"gtmOnFailure",[7]],[36]]]],["aA","gtm.formInteract",[15,"x"],[15,"aD"]],[52,"aE",["aB",[8,"checkValidation",false,"waitForTags",false]]],[22,[28,[15,"aE"]],[46,[53,[2,[15,"a"],"gtmOnFailure",[7]],[36]]]],["aA","gtm.formSubmit",[15,"z"],[15,"aE"]]]],[46,[53,["p",[15,"x"]],["q",[15,"z"],[8,"waitForCallbacks",false,"checkValidation",false]]]]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ccd_em_outbound_click",[46,"a"],[50,"r",[46,"x"],[22,[28,[15,"x"]],[46,[36,[44]]]],[41,"y"],[3,"y",""],[22,[1,[15,"x"],[17,[15,"x"],"href"]],[46,[53,[41,"z"],[3,"z",[2,[17,[15,"x"],"href"],"indexOf",[7,"#"]]],[3,"y",[39,[23,[15,"z"],0],[17,[15,"x"],"href"],[2,[17,[15,"x"],"href"],"substring",[7,0,[15,"z"]]]]]]]],[36,[15,"y"]]],[50,"s",[46,"x"],[22,[28,[15,"x"]],[46,[36,[44]]]],[41,"y"],[3,"y",[17,[15,"x"],"hostname"]],[52,"z",[2,[15,"y"],"match",[7,"^www\\d*\\."]]],[22,[1,[15,"z"],[16,[15,"z"],0]],[46,[3,"y",[2,[15,"y"],"substring",[7,[17,[16,[15,"z"],0],"length"]]]]]],[36,[15,"y"]]],[50,"t",[46,"x"],[22,[28,[15,"x"]],[46,[36,false]]],[52,"y",[2,[17,[15,"x"],"hostname"],"toLowerCase",[7]]],[22,[28,[15,"y"]],[46,[53,[36,false]]]],[41,"z"],[3,"z",[2,["s",["p",["o"]]],"toLowerCase",[7]]],[41,"aA"],[3,"aA",[37,[17,[15,"y"],"length"],[17,[15,"z"],"length"]]],[22,[1,[18,[15,"aA"],0],[29,[2,[15,"z"],"charAt",[7,0]],"."]],[46,[53,[32,[15,"aA"],[3,"aA",[37,[15,"aA"],1]]],[3,"z",[0,".",[15,"z"]]]]]],[22,[1,[19,[15,"aA"],0],[12,[2,[15,"y"],"indexOf",[7,[15,"z"],[15,"aA"]]],[15,"aA"]]],[46,[53,[36,false]]]],[36,true]],[50,"w",[46,"x"],[52,"y",[8]],[43,[15,"y"],[15,"i"],true],[43,[15,"y"],[15,"e"],true],[43,[15,"x"],"eventMetadata",[15,"y"]]],[52,"b",["require","internal.getProductSettingsParameter"]],[52,"c",["require","templateStorage"]],[52,"d",[15,"__module_ccdEmOutboundClickActivity"]],[52,"e","speculative"],[52,"f","ae_block_outbound_click"],[52,"g","click"],[52,"h","isRegistered"],[52,"i","em_event"],[52,"j",[17,[15,"a"],"instanceDestinationId"]],[22,["b",[15,"j"],[15,"f"]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[2,[15,"d"],"A",[7,[15,"j"],[17,[15,"a"],"includeParams"]]],[22,[2,[15,"c"],"getItem",[7,[15,"h"]]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[52,"k",["require","internal.addDataLayerEventListener"]],[52,"l",["require","internal.enableAutoEventOnLinkClick"]],[52,"m",["require","internal.getDestinationIds"]],[52,"n",["require","internal.getRemoteConfigParameter"]],[52,"o",["require","getUrl"]],[52,"p",["require","parseUrl"]],[52,"q",["require","internal.sendGtagEvent"]],[52,"u",["n",[15,"j"],"cross_domain_conditions"]],[52,"v",["l",[8,"affiliateDomains",[15,"u"],"checkValidation",true,"waitForTags",false]]],[22,[28,[15,"v"]],[46,[53,[2,[15,"a"],"gtmOnFailure",[7]],[36]]]],[2,[15,"c"],"setItem",[7,[15,"h"],true]],["k","gtm.linkClick",[51,"",[7,"x","y"],[52,"z",["p",[16,[15,"x"],"gtm.elementUrl"]]],[22,[28,["t",[15,"z"]]],[46,[53,["y"],[36]]]],[52,"aA",[8,"link_id",[16,[15,"x"],"gtm.elementId"],"link_classes",[16,[15,"x"],"gtm.elementClasses"],"link_url",["r",[15,"z"]],"link_domain",["s",[15,"z"]],"outbound",true]],[43,[15,"aA"],"event_callback",[15,"y"]],[52,"aB",[8,"eventId",[16,[15,"x"],"gtm.uniqueEventId"],"deferrable",true]],["w",[15,"aB"]],["q",["m"],[15,"g"],[15,"aA"],[15,"aB"]]],[15,"v"]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ccd_em_page_view",[46,"a"],[50,"q",[46,"r"],[52,"s",[8]],[43,[15,"s"],[17,[15,"f"],"P"],true],[43,[15,"s"],[17,[15,"f"],"BV"],true],[43,[15,"r"],"eventMetadata",[15,"s"]]],[22,[28,[17,[15,"a"],"historyEvents"]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[52,"b",["require","internal.getProductSettingsParameter"]],[52,"c",["require","internal.setRemoteConfigParameter"]],[52,"d",["require","templateStorage"]],[52,"e",[15,"__module_ccdEmPageViewActivity"]],[52,"f",[15,"__module_metadataSchema"]],[52,"g","ae_block_history"],[52,"h","page_view"],[52,"i","isRegistered"],[52,"j",[17,[15,"a"],"instanceDestinationId"]],[22,["b",[15,"j"],[15,"g"]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[2,[15,"e"],"A",[7,[15,"j"]]],[22,[2,[15,"d"],"getItem",[7,[15,"i"]]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[52,"k",["require","internal.addDataLayerEventListener"]],[52,"l",["require","internal.enableAutoEventOnHistoryChange"]],[52,"m",["require","internal.getDestinationIds"]],[52,"n",["require","internal.sendGtagEvent"]],[52,"o",[8,"interval",1000,"useV2EventName",true]],[52,"p",["l",[15,"o"]]],[22,[28,[15,"p"]],[46,[53,[2,[15,"a"],"gtmOnFailure",[7]],[36]]]],[2,[15,"d"],"setItem",[7,[15,"i"],true]],["k","gtm.historyChange-v2",[51,"",[7,"r","s"],["s"],[52,"t",[16,[15,"r"],"gtm.oldUrl"]],[22,[20,[16,[15,"r"],"gtm.newUrl"],[15,"t"]],[46,[36]]],[52,"u",[16,[15,"r"],"gtm.historyChangeSource"]],[22,[1,[1,[21,[15,"u"],"pushState"],[21,[15,"u"],"popstate"]],[21,[15,"u"],"replaceState"]],[46,[53,[36]]]],[52,"v",[8]],[22,[17,[15,"a"],"includeParams"],[46,[53,[43,[15,"v"],"page_location",[16,[15,"r"],"gtm.newUrl"]],[43,[15,"v"],"page_referrer",[15,"t"]]]]],[52,"w",[8,"eventId",[16,[15,"r"],"gtm.uniqueEventId"]]],[22,[21,[17,[15,"a"],"deferPageView"],false],[46,[53,[43,[15,"w"],"deferrable",true]]]],["q",[15,"w"]],["n",["m"],[15,"h"],[15,"v"],[15,"w"]]],[15,"p"]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ccd_em_scroll",[46,"a"],[50,"p",[46,"q"],[52,"r",[8]],[43,[15,"r"],[15,"i"],true],[43,[15,"r"],[15,"e"],true],[43,[15,"q"],"eventMetadata",[15,"r"]]],[52,"b",["require","internal.getProductSettingsParameter"]],[52,"c",["require","templateStorage"]],[52,"d",[15,"__module_ccdEmScrollActivity"]],[52,"e","speculative"],[52,"f","ae_block_scroll"],[52,"g","scroll"],[52,"h","isRegistered"],[52,"i","em_event"],[52,"j",[17,[15,"a"],"instanceDestinationId"]],[22,["b",[15,"j"],[15,"f"]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[2,[15,"d"],"A",[7,[15,"j"],[17,[15,"a"],"includeParams"]]],[22,[2,[15,"c"],"getItem",[7,[15,"h"]]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[52,"k",["require","internal.addDataLayerEventListener"]],[52,"l",["require","internal.enableAutoEventOnScroll"]],[52,"m",["require","internal.getDestinationIds"]],[52,"n",["require","internal.sendGtagEvent"]],[52,"o",["l",[8,"verticalThresholdUnits","PERCENT","verticalThresholds",90]]],[22,[28,[15,"o"]],[46,[53,[2,[15,"a"],"gtmOnFailure",[7]],[36]]]],[2,[15,"c"],"setItem",[7,[15,"h"],true]],["k","gtm.scrollDepth",[51,"",[7,"q","r"],["r"],[52,"s",[8,"eventId",[16,[15,"q"],"gtm.uniqueEventId"],"deferrable",true]],[52,"t",[8,"percent_scrolled",[16,[15,"q"],"gtm.scrollThreshold"]]],["p",[15,"s"]],["n",["m"],[15,"g"],[15,"t"],[15,"s"]]],[15,"o"]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ccd_em_site_search",[46,"a"],[52,"b",["require","getQueryParameters"]],[52,"c",["require","internal.sendGtagEvent"]],[52,"d",["require","getContainerVersion"]],[52,"e",[15,"__module_ccdEmSiteSearchActivity"]],[52,"f",[2,[15,"e"],"A",[7,[17,[15,"a"],"searchQueryParams"],[15,"b"]]]],[52,"g",[30,[17,[15,"a"],"instanceDestinationId"],[17,["d"],"containerId"]]],[52,"h",[8,"deferrable",true,"eventId",[17,[15,"a"],"gtmEventId"],"eventMetadata",[8,"em_event",true]]],[22,[15,"f"],[46,[53,[52,"i",[39,[28,[28,[17,[15,"a"],"includeParams"]]],[2,[15,"e"],"B",[7,[15,"f"],[17,[15,"a"],"additionalQueryParams"],[15,"b"]]],[8]]],["c",[15,"g"],"view_search_results",[15,"i"],[15,"h"]]]]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ccd_em_video",[46,"a"],[50,"r",[46,"s"],[52,"t",[8]],[43,[15,"t"],[15,"k"],true],[43,[15,"t"],[15,"e"],true],[43,[15,"s"],"eventMetadata",[15,"t"]]],[52,"b",["require","internal.getProductSettingsParameter"]],[52,"c",["require","templateStorage"]],[52,"d",[15,"__module_ccdEmVideoActivity"]],[52,"e","speculative"],[52,"f","ae_block_video"],[52,"g","video_start"],[52,"h","video_progress"],[52,"i","video_complete"],[52,"j","isRegistered"],[52,"k","em_event"],[52,"l",[17,[15,"a"],"instanceDestinationId"]],[22,["b",[15,"l"],[15,"f"]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[2,[15,"d"],"A",[7,[15,"l"],[17,[15,"a"],"includeParams"]]],[22,[2,[15,"c"],"getItem",[7,[15,"j"]]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[52,"m",["require","internal.addDataLayerEventListener"]],[52,"n",["require","internal.enableAutoEventOnYouTubeActivity"]],[52,"o",["require","internal.getDestinationIds"]],[52,"p",["require","internal.sendGtagEvent"]],[52,"q",["n",[8,"captureComplete",true,"captureStart",true,"progressThresholdsPercent",[7,10,25,50,75]]]],[22,[28,[15,"q"]],[46,[53,[2,[15,"a"],"gtmOnFailure",[7]],[36]]]],[2,[15,"c"],"setItem",[7,[15,"j"],true]],["m","gtm.video",[51,"",[7,"s","t"],["t"],[52,"u",[16,[15,"s"],"gtm.videoStatus"]],[41,"v"],[22,[20,[15,"u"],"start"],[46,[53,[3,"v",[15,"g"]]]],[46,[22,[20,[15,"u"],"progress"],[46,[53,[3,"v",[15,"h"]]]],[46,[22,[20,[15,"u"],"complete"],[46,[53,[3,"v",[15,"i"]]]],[46,[53,[36]]]]]]]],[52,"w",[8,"video_current_time",[16,[15,"s"],"gtm.videoCurrentTime"],"video_duration",[16,[15,"s"],"gtm.videoDuration"],"video_percent",[16,[15,"s"],"gtm.videoPercent"],"video_provider",[16,[15,"s"],"gtm.videoProvider"],"video_title",[16,[15,"s"],"gtm.videoTitle"],"video_url",[16,[15,"s"],"gtm.videoUrl"],"visible",[16,[15,"s"],"gtm.videoVisible"]]],[52,"x",[8,"eventId",[16,[15,"s"],"gtm.uniqueEventId"],"deferrable",true]],["r",[15,"x"]],["p",["o"],[15,"v"],[15,"w"],[15,"x"]]],[15,"q"]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ccd_ga_first",[46,"a"],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ccd_ga_last",[46,"a"],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ccd_ga_regscope",[46,"a"],[52,"b",[15,"__module_ccdGaRegionScopedSettings"]],[52,"c",[2,[15,"b"],"B",[7,[15,"a"]]]],[2,[15,"b"],"A",[7,[15,"a"],[15,"c"]]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__e",[46,"a"],[36,[13,[41,"$0"],[3,"$0",["require","internal.getEventData"]],["$0","event"]]]]
 ,[50,"__ogt_1p_data_v2",[46,"a"],[50,"q",[46,"v","w"],[52,"x",[7]],[52,"y",[2,[15,"b"],"keys",[7,[15,"v"]]]],[65,"z",[15,"y"],[46,[53,[52,"aA",[30,[16,[15,"v"],[15,"z"]],[7]]],[52,"aB",[39,[18,[17,[15,"aA"],"length"],0],"1","0"]],[52,"aC",[39,["r",[15,"w"],[15,"z"]],"1","0"]],[2,[15,"x"],"push",[7,[0,[0,[0,[16,[15,"p"],[15,"z"]],"-"],[15,"aB"]],[15,"aC"]]]]]]],[36,[2,[15,"x"],"join",[7,"~"]]]],[50,"r",[46,"v","w"],[22,[28,[15,"v"]],[46,[53,[36,false]]]],[38,[15,"w"],[46,"email","phone_number","first_name","last_name","street","city","region","postal_code","country"],[46,[5,[46,[36,[28,[28,[16,[15,"v"],"email"]]]]]],[5,[46,[36,[28,[28,[16,[15,"v"],"phone_number"]]]]]],[5,[46]],[5,[46]],[5,[46]],[5,[46]],[5,[46]],[5,[46]],[5,[46,[36,["s",[15,"v"],[15,"w"]]]]],[9,[46,[36,false]]]]]],[50,"s",[46,"v","w"],[36,[1,[28,[28,[16,[15,"v"],"address"]]],[28,[28,[16,[16,[15,"v"],"address"],[15,"w"]]]]]]],[50,"t",[46,"v","w","x","y"],[22,[20,[16,[15,"w"],"type"],[15,"x"]],[46,[53,[22,[28,[15,"v"]],[46,[53,[3,"v",[8]]]]],[22,[28,[16,[15,"v"],[15,"x"]]],[46,[53,[43,[15,"v"],[15,"x"],[16,[15,"w"],"userData"]],[52,"z",[8,"mode","a"]],[22,[16,[15,"w"],"tagName"],[46,[53,[43,[15,"z"],"location",[16,[15,"w"],"tagName"]]]]],[22,[16,[15,"w"],"querySelector"],[46,[53,[43,[15,"z"],"selector",[16,[15,"w"],"querySelector"]]]]],[43,[15,"y"],[15,"x"],[15,"z"]]]]]]]],[36,[15,"v"]]],[50,"u",[46,"v","w","x"],[22,[28,[16,[15,"a"],[15,"x"]]],[46,[36]]],[43,[15,"v"],[15,"w"],[8,"value",[16,[15,"a"],[15,"x"]]]]],[22,[28,[17,[15,"a"],"isEnabled"]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[52,"b",["require","Object"]],[52,"c",["require","internal.isFeatureEnabled"]],[52,"d",[15,"__module_featureFlags"]],[52,"e",["require","internal.getDestinationIds"]],[52,"f",["require","internal.getProductSettingsParameter"]],[52,"g",["require","internal.detectUserProvidedData"]],[52,"h",["require","queryPermission"]],[52,"i",["require","internal.setRemoteConfigParameter"]],[52,"j",["require","internal.registerCcdCallback"]],[52,"k",[15,"__module_metadataSchema"]],[52,"l","_z"],[52,"m",["c",[17,[15,"d"],"DW"]]],[52,"n",[30,["e"],[7]]],[52,"o",[8,"enable_code",true]],[52,"p",[8,"email","1","phone_number","2","first_name","3","last_name","4","country","5","postal_code","6","street","7","city","8","region","9"]],[22,[17,[15,"a"],"isAutoEnabled"],[46,[53,[52,"v",[7]],[22,[1,[17,[15,"a"],"autoCollectExclusionSelectors"],[17,[17,[15,"a"],"autoCollectExclusionSelectors"],"length"]],[46,[53,[53,[41,"y"],[3,"y",0],[63,[7,"y"],[23,[15,"y"],[17,[17,[15,"a"],"autoCollectExclusionSelectors"],"length"]],[33,[15,"y"],[3,"y",[0,[15,"y"],1]]],[46,[53,[52,"z",[17,[16,[17,[15,"a"],"autoCollectExclusionSelectors"],[15,"y"]],"exclusionSelector"]],[22,[15,"z"],[46,[53,[2,[15,"v"],"push",[7,[15,"z"]]]]]]]]]]]]],[52,"w",[30,["c",[17,[15,"d"],"X"]],[17,[15,"a"],"isAutoCollectPiiEnabledFlag"]]],[52,"x",[39,[17,[15,"a"],"isAutoCollectPiiEnabledFlag"],[17,[15,"a"],"autoEmailEnabled"],true]],[43,[15,"o"],"auto_detect",[8,"email",[15,"x"],"phone",[1,[15,"w"],[17,[15,"a"],"autoPhoneEnabled"]],"address",[1,[15,"w"],[17,[15,"a"],"autoAddressEnabled"]],"exclude_element_selectors",[15,"v"]]]]]],[22,[17,[15,"a"],"isManualEnabled"],[46,[53,[52,"v",[8]],[22,[17,[15,"a"],"manualEmailEnabled"],[46,[53,["u",[15,"v"],"email","emailValue"]]]],[22,[17,[15,"a"],"manualPhoneEnabled"],[46,[53,["u",[15,"v"],"phone","phoneValue"]]]],[22,[17,[15,"a"],"manualAddressEnabled"],[46,[53,[52,"w",[8]],["u",[15,"w"],"first_name","firstNameValue"],["u",[15,"w"],"last_name","lastNameValue"],["u",[15,"w"],"street","streetValue"],["u",[15,"w"],"city","cityValue"],["u",[15,"w"],"region","regionValue"],["u",[15,"w"],"country","countryValue"],["u",[15,"w"],"postal_code","postalCodeValue"],[43,[15,"v"],"name_and_address",[7,[15,"w"]]]]]],[43,[15,"o"],"selectors",[15,"v"]]]]],[65,"v",[15,"n"],[46,[53,["i",[15,"v"],"user_data_settings",[15,"o"]],[52,"w",[16,[15,"o"],"auto_detect"]],[22,[28,[15,"w"]],[46,[53,[6]]]],[52,"x",[51,"",[7,"y"],[52,"z",[2,[15,"y"],"getMetadata",[7,[17,[15,"k"],"CC"]]]],[22,[15,"z"],[46,[53,[36,[15,"z"]]]]],[52,"aA",[1,["c",[17,[15,"d"],"CK"]],[20,[2,[15,"v"],"indexOf",[7,"G-"]],0]]],[41,"aB"],[22,["h","detect_user_provided_data","auto"],[46,[53,[3,"aB",["g",[8,"excludeElementSelectors",[16,[15,"w"],"exclude_element_selectors"],"fieldFilters",[8,"email",[16,[15,"w"],"email"],"phone",[16,[15,"w"],"phone"],"address",[16,[15,"w"],"address"]],"performDataLayerSearch",[15,"aA"]]]]]]],[52,"aC",[1,[15,"aB"],[16,[15,"aB"],"elements"]]],[52,"aD",[8]],[52,"aE",[8]],[22,[1,[15,"aC"],[18,[17,[15,"aC"],"length"],0]],[46,[53,[41,"aF"],[41,"aG"],[3,"aG",[8]],[53,[41,"aH"],[3,"aH",0],[63,[7,"aH"],[23,[15,"aH"],[17,[15,"aC"],"length"]],[33,[15,"aH"],[3,"aH",[0,[15,"aH"],1]]],[46,[53,[52,"aI",[16,[15,"aC"],[15,"aH"]]],["t",[15,"aD"],[15,"aI"],"email",[15,"aE"]],[22,["c",[17,[15,"d"],"Y"]],[46,[53,["t",[15,"aD"],[15,"aI"],"phone_number",[15,"aE"]],[3,"aF",["t",[15,"aF"],[15,"aI"],"first_name",[15,"aG"]]],[3,"aF",["t",[15,"aF"],[15,"aI"],"last_name",[15,"aG"]]],[3,"aF",["t",[15,"aF"],[15,"aI"],"country",[15,"aG"]]],[3,"aF",["t",[15,"aF"],[15,"aI"],"postal_code",[15,"aG"]]]]]]]]]],[22,[1,[15,"aF"],[28,[16,[15,"aD"],"address"]]],[46,[53,[43,[15,"aD"],"address",[15,"aF"]],[22,[15,"m"],[46,[53,[43,[16,[15,"aD"],"address"],"_tag_metadata",[15,"aG"]]]]]]]]]]],[22,[15,"aA"],[46,[53,[52,"aF",[1,[15,"aB"],[16,[15,"aB"],"dataLayerSearchResults"]]],[22,[15,"aF"],[46,[53,[52,"aG",["q",[15,"aF"],[15,"aD"]]],[22,[15,"aG"],[46,[53,[2,[15,"y"],"setHitData",[7,[15,"l"],[15,"aG"]]]]]]]]]]]],[22,[15,"m"],[46,[53,[22,[30,[16,[15,"aD"],"email"],[16,[15,"aD"],"phone_number"]],[46,[53,[43,[15,"aD"],"_tag_metadata",[15,"aE"]]]]]]]],[2,[15,"y"],"setMetadata",[7,[17,[15,"k"],"CC"],[15,"aD"]]],[36,[15,"aD"]]]],["j",[15,"v"],[51,"",[7,"y"],[2,[15,"y"],"setMetadata",[7,[17,[15,"k"],"CD"],[15,"x"]]]]]]]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__set_product_settings",[46,"a"],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[52,"__module_gtagSchema",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[52,"b","ad_personalization"],[52,"c","ad_storage"],[52,"d","ad_user_data"],[52,"e","consent_updated"],[52,"f","app_remove"],[52,"g","app_store_refund"],[52,"h","app_store_subscription_cancel"],[52,"i","app_store_subscription_convert"],[52,"j","app_store_subscription_renew"],[52,"k","purchase"],[52,"l","first_open"],[52,"m","first_visit"],[52,"n","gtag.config"],[52,"o","in_app_purchase"],[52,"p","page_view"],[52,"q","session_start"],[52,"r","user_engagement"],[52,"s","ads_data_redaction"],[52,"t","allow_ad_personalization_signals"],[52,"u","allow_custom_scripts"],[52,"v","allow_direct_google_requests"],[52,"w","allow_enhanced_conversions"],[52,"x","allow_google_signals"],[52,"y","allow_interest_groups"],[52,"z","auid"],[52,"aA","aw_remarketing"],[52,"aB","aw_remarketing_only"],[52,"aC","discount"],[52,"aD","aw_feed_country"],[52,"aE","aw_feed_language"],[52,"aF","items"],[52,"aG","aw_merchant_id"],[52,"aH","aw_basket_type"],[52,"aI","client_id"],[52,"aJ","conversion_cookie_prefix"],[52,"aK","conversion_id"],[52,"aL","conversion_linker"],[52,"aM","conversion_api"],[52,"aN","cookie_deprecation"],[52,"aO","cookie_expires"],[52,"aP","cookie_prefix"],[52,"aQ","cookie_update"],[52,"aR","country"],[52,"aS","currency"],[52,"aT","customer_buyer_stage"],[52,"aU","customer_lifetime_value"],[52,"aV","customer_loyalty"],[52,"aW","customer_ltv_bucket"],[52,"aX","debug_mode"],[52,"aY","developer_id"],[52,"aZ","shipping"],[52,"bA","engagement_time_msec"],[52,"bB","estimated_delivery_date"],[52,"bC","event_developer_id_string"],[52,"bD","event"],[52,"bE","event_timeout"],[52,"bF","first_party_collection"],[52,"bG","match_id"],[52,"bH","gdpr_applies"],[52,"bI","google_analysis_params"],[52,"bJ","_google_ng"],[52,"bK","gpp_sid"],[52,"bL","gpp_string"],[52,"bM","gsa_experiment_id"],[52,"bN","gtag_event_feature_usage"],[52,"bO","iframe_state"],[52,"bP","ignore_referrer"],[52,"bQ","is_passthrough"],[52,"bR","_lps"],[52,"bS","language"],[52,"bT","merchant_feed_label"],[52,"bU","merchant_feed_language"],[52,"bV","merchant_id"],[52,"bW","new_customer"],[52,"bX","page_hostname"],[52,"bY","page_path"],[52,"bZ","page_referrer"],[52,"cA","page_title"],[52,"cB","_platinum_request_status"],[52,"cC","quantity"],[52,"cD","restricted_data_processing"],[52,"cE","screen_resolution"],[52,"cF","send_page_view"],[52,"cG","server_container_url"],[52,"cH","session_duration"],[52,"cI","session_engaged_time"],[52,"cJ","session_id"],[52,"cK","_shared_user_id"],[52,"cL","delivery_postal_code"],[52,"cM","topmost_url"],[52,"cN","transaction_id"],[52,"cO","transport_url"],[52,"cP","update"],[52,"cQ","_user_agent_architecture"],[52,"cR","_user_agent_bitness"],[52,"cS","_user_agent_full_version_list"],[52,"cT","_user_agent_mobile"],[52,"cU","_user_agent_model"],[52,"cV","_user_agent_platform"],[52,"cW","_user_agent_platform_version"],[52,"cX","_user_agent_wow64"],[52,"cY","user_data"],[52,"cZ","user_data_auto_latency"],[52,"dA","user_data_auto_meta"],[52,"dB","user_data_auto_multi"],[52,"dC","user_data_auto_selectors"],[52,"dD","user_data_auto_status"],[52,"dE","user_data_mode"],[52,"dF","user_id"],[52,"dG","user_properties"],[52,"dH","us_privacy_string"],[52,"dI","value"],[52,"dJ","_fpm_parameters"],[52,"dK","_host_name"],[52,"dL","_in_page_command"],[52,"dM","non_personalized_ads"],[52,"dN","conversion_label"],[52,"dO","page_location"],[52,"dP","global_developer_id_string"],[52,"dQ","tc_privacy_string"],[36,[8,"A",[15,"b"],"B",[15,"c"],"C",[15,"d"],"F",[15,"e"],"H",[15,"f"],"I",[15,"g"],"J",[15,"h"],"K",[15,"i"],"L",[15,"j"],"X",[15,"k"],"AC",[15,"l"],"AD",[15,"m"],"AE",[15,"n"],"AG",[15,"o"],"AH",[15,"p"],"AJ",[15,"q"],"AN",[15,"r"],"AX",[15,"s"],"BE",[15,"t"],"BF",[15,"u"],"BG",[15,"v"],"BI",[15,"w"],"BJ",[15,"x"],"BK",[15,"y"],"BP",[15,"z"],"BR",[15,"aA"],"BS",[15,"aB"],"BT",[15,"aC"],"BU",[15,"aD"],"BV",[15,"aE"],"BW",[15,"aF"],"BX",[15,"aG"],"BY",[15,"aH"],"CG",[15,"aI"],"CL",[15,"aJ"],"CM",[15,"aK"],"JS",[15,"dN"],"CN",[15,"aL"],"CP",[15,"aM"],"CQ",[15,"aN"],"CS",[15,"aO"],"CW",[15,"aP"],"CX",[15,"aQ"],"CY",[15,"aR"],"CZ",[15,"aS"],"DA",[15,"aT"],"DB",[15,"aU"],"DC",[15,"aV"],"DD",[15,"aW"],"DH",[15,"aX"],"DI",[15,"aY"],"DU",[15,"aZ"],"DW",[15,"bA"],"EA",[15,"bB"],"EE",[15,"bC"],"EG",[15,"bD"],"EI",[15,"bE"],"EN",[15,"bF"],"EY",[15,"bG"],"FI",[15,"bH"],"JU",[15,"dP"],"FM",[15,"bI"],"FN",[15,"bJ"],"FQ",[15,"bK"],"FR",[15,"bL"],"FT",[15,"bM"],"FU",[15,"bN"],"FW",[15,"bO"],"FX",[15,"bP"],"GC",[15,"bQ"],"GD",[15,"bR"],"GE",[15,"bS"],"GL",[15,"bT"],"GM",[15,"bU"],"GN",[15,"bV"],"GR",[15,"bW"],"GU",[15,"bX"],"JT",[15,"dO"],"GV",[15,"bY"],"GW",[15,"bZ"],"GX",[15,"cA"],"HF",[15,"cB"],"HH",[15,"cC"],"HL",[15,"cD"],"HP",[15,"cE"],"HS",[15,"cF"],"HU",[15,"cG"],"HV",[15,"cH"],"HX",[15,"cI"],"HY",[15,"cJ"],"IA",[15,"cK"],"IB",[15,"cL"],"JV",[15,"dQ"],"IG",[15,"cM"],"IJ",[15,"cN"],"IK",[15,"cO"],"IM",[15,"cP"],"IP",[15,"cQ"],"IQ",[15,"cR"],"IR",[15,"cS"],"IS",[15,"cT"],"IT",[15,"cU"],"IU",[15,"cV"],"IV",[15,"cW"],"IW",[15,"cX"],"IX",[15,"cY"],"IY",[15,"cZ"],"IZ",[15,"dA"],"JA",[15,"dB"],"JB",[15,"dC"],"JC",[15,"dD"],"JD",[15,"dE"],"JF",[15,"dF"],"JG",[15,"dG"],"JI",[15,"dH"],"JJ",[15,"dI"],"JL",[15,"dJ"],"JM",[15,"dK"],"JN",[15,"dL"],"JQ",[15,"dM"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_featureFlags",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[52,"b",30],[52,"c",32],[52,"d",33],[52,"e",34],[52,"f",40],[52,"g",42],[52,"h",43],[52,"i",44],[52,"j",45],[52,"k",46],[52,"l",47],[52,"m",56],[52,"n",113],[52,"o",129],[52,"p",142],[52,"q",156],[52,"r",168],[52,"s",174],[52,"t",178],[52,"u",188],[52,"v",212],[36,[8,"DP",[15,"r"],"W",[15,"b"],"X",[15,"c"],"Y",[15,"d"],"Z",[15,"e"],"AF",[15,"f"],"AH",[15,"g"],"AI",[15,"h"],"AJ",[15,"i"],"AK",[15,"j"],"AL",[15,"k"],"AM",[15,"l"],"EA",[15,"u"],"AR",[15,"m"],"DT",[15,"s"],"DW",[15,"t"],"BX",[15,"n"],"CK",[15,"o"],"CX",[15,"p"],"EQ",[15,"v"],"DH",[15,"q"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_metadataSchema",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[52,"b","accept_by_default"],[52,"c","add_tag_timing"],[52,"d","consent_state"],[52,"e","consent_updated"],[52,"f","conversion_linker_enabled"],[52,"g","cookie_options"],[52,"h","em_event"],[52,"i","event_start_timestamp_ms"],[52,"j","event_usage"],[52,"k","ga4_collection_subdomain"],[52,"l","hit_type"],[52,"m","hit_type_override"],[52,"n","is_conversion"],[52,"o","is_external_event"],[52,"p","is_first_visit"],[52,"q","is_first_visit_conversion"],[52,"r","is_fpm_encryption"],[52,"s","is_fpm_split"],[52,"t","is_gcp_conversion"],[52,"u","is_google_signals_allowed"],[52,"v","is_server_side_destination"],[52,"w","is_session_start"],[52,"x","is_session_start_conversion"],[52,"y","is_sgtm_ga_ads_conversion_study_control_group"],[52,"z","is_sgtm_prehit"],[52,"aA","is_split_conversion"],[52,"aB","is_syn"],[52,"aC","prehit_for_retry"],[52,"aD","redact_ads_data"],[52,"aE","redact_click_ids"],[52,"aF","send_ccm_parallel_ping"],[52,"aG","send_user_data_hit"],[52,"aH","speculative"],[52,"aI","syn_or_mod"],[52,"aJ","transient_ecsid"],[52,"aK","transmission_type"],[52,"aL","user_data"],[52,"aM","user_data_from_automatic"],[52,"aN","user_data_from_automatic_getter"],[52,"aO","user_data_from_code"],[52,"aP","user_data_from_manual"],[36,[8,"A",[15,"b"],"B",[15,"c"],"H",[15,"d"],"I",[15,"e"],"J",[15,"f"],"K",[15,"g"],"P",[15,"h"],"U",[15,"i"],"V",[15,"j"],"AD",[15,"k"],"AF",[15,"l"],"AG",[15,"m"],"AJ",[15,"n"],"AL",[15,"o"],"AN",[15,"p"],"AO",[15,"q"],"AQ",[15,"r"],"AR",[15,"s"],"AS",[15,"t"],"AT",[15,"u"],"AW",[15,"v"],"AX",[15,"w"],"AY",[15,"x"],"AZ",[15,"y"],"BA",[15,"z"],"BC",[15,"aA"],"BD",[15,"aB"],"BI",[15,"aC"],"BL",[15,"aD"],"BM",[15,"aE"],"BO",[15,"aF"],"BT",[15,"aG"],"BV",[15,"aH"],"BY",[15,"aI"],"BZ",[15,"aJ"],"CA",[15,"aK"],"CB",[15,"aL"],"CC",[15,"aM"],"CD",[15,"aN"],"CE",[15,"aO"],"CF",[15,"aP"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_ccdEmSiteSearchActivity",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"b",[46,"d","e"],[52,"f",[2,[30,[15,"d"],""],"split",[7,","]]],[53,[41,"g"],[3,"g",0],[63,[7,"g"],[23,[15,"g"],[17,[15,"f"],"length"]],[33,[15,"g"],[3,"g",[0,[15,"g"],1]]],[46,[53,[52,"h",["e",[2,[16,[15,"f"],[15,"g"]],"trim",[7]]]],[22,[21,[15,"h"],[44]],[46,[53,[36,[15,"h"]]]]]]]]]],[50,"c",[46,"d","e","f"],[52,"g",[8,"search_term",[15,"d"]]],[52,"h",[2,[30,[15,"e"],""],"split",[7,","]]],[53,[41,"i"],[3,"i",0],[63,[7,"i"],[23,[15,"i"],[17,[15,"h"],"length"]],[33,[15,"i"],[3,"i",[0,[15,"i"],1]]],[46,[53,[52,"j",[2,[16,[15,"h"],[15,"i"]],"trim",[7]]],[52,"k",["f",[15,"j"]]],[22,[21,[15,"k"],[44]],[46,[53,[43,[15,"g"],[0,"q_",[15,"j"]],[15,"k"]]]]]]]]],[36,[15,"g"]]],[36,[8,"B",[15,"c"],"A",[15,"b"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_activities",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"b",[46,"c","d"],[36,[39,[15,"d"],["d",[15,"c"]],[15,"c"]]]],[36,[8,"A",[15,"b"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_ccdEmDownloadActivity",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"g",[46,"h","i"],["c",[15,"h"],[51,"",[7,"j"],[22,[30,[21,[2,[15,"j"],"getEventName",[7]],[15,"f"]],[28,[2,[15,"j"],"getMetadata",[7,[17,[15,"d"],"P"]]]]],[46,[53,[36]]]],[22,["b",[15,"h"],[15,"e"]],[46,[53,[2,[15,"j"],"abort",[7]],[36]]]],[2,[15,"j"],"setMetadata",[7,[17,[15,"d"],"BV"],false]],[22,[28,[15,"i"]],[46,[53,[2,[15,"j"],"setHitData",[7,"link_id",[44]]],[2,[15,"j"],"setHitData",[7,"link_url",[44]]],[2,[15,"j"],"setHitData",[7,"link_text",[44]]],[2,[15,"j"],"setHitData",[7,"file_name",[44]]],[2,[15,"j"],"setHitData",[7,"file_extension",[44]]]]]]]]],[52,"b",["require","internal.getProductSettingsParameter"]],[52,"c",["require","internal.registerCcdCallback"]],[52,"d",[15,"__module_metadataSchema"]],[52,"e","ae_block_downloads"],[52,"f","file_download"],[36,[8,"A",[15,"g"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_ccdEmFormActivity",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"i",[46,"j","k","l"],[22,[20,[15,"k"],[44]],[46,[53,[3,"k",[20,[2,[15,"j"],"indexOf",[7,"AW-"]],0]]]]],["c",[15,"j"],[51,"",[7,"m"],[52,"n",[2,[15,"m"],"getEventName",[7]]],[52,"o",[30,[20,[15,"n"],[15,"g"]],[20,[15,"n"],[15,"f"]]]],[22,[30,[28,[15,"o"]],[28,[2,[15,"m"],"getMetadata",[7,[17,[15,"d"],"P"]]]]],[46,[53,[36]]]],[22,["b",[15,"j"],[15,"e"]],[46,[53,[2,[15,"m"],"abort",[7]],[36]]]],[22,[1,[28,[15,"k"]],[2,[15,"m"],"getMetadata",[7,[15,"h"]]]],[46,[53,[2,[15,"m"],"abort",[7]],[36]]]],[2,[15,"m"],"setMetadata",[7,[17,[15,"d"],"BV"],false]],[22,[28,[15,"l"]],[46,[53,[2,[15,"m"],"setHitData",[7,"form_id",[44]]],[2,[15,"m"],"setHitData",[7,"form_name",[44]]],[2,[15,"m"],"setHitData",[7,"form_destination",[44]]],[2,[15,"m"],"setHitData",[7,"form_length",[44]]],[22,[20,[15,"n"],[15,"f"]],[46,[53,[2,[15,"m"],"setHitData",[7,"form_submit_text",[44]]]]],[46,[22,[20,[15,"n"],[15,"g"]],[46,[53,[2,[15,"m"],"setHitData",[7,"first_field_id",[44]]],[2,[15,"m"],"setHitData",[7,"first_field_name",[44]]],[2,[15,"m"],"setHitData",[7,"first_field_type",[44]]],[2,[15,"m"],"setHitData",[7,"first_field_position",[44]]]]]]]]]]]]]],[52,"b",["require","internal.getProductSettingsParameter"]],[52,"c",["require","internal.registerCcdCallback"]],[52,"d",[15,"__module_metadataSchema"]],[52,"e","ae_block_form"],[52,"f","form_submit"],[52,"g","form_start"],[52,"h","form_event_canceled"],[36,[8,"A",[15,"i"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_ccdEmOutboundClickActivity",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"g",[46,"h","i"],["c",[15,"h"],[51,"",[7,"j"],[22,[30,[21,[2,[15,"j"],"getEventName",[7]],[15,"f"]],[28,[2,[15,"j"],"getMetadata",[7,[17,[15,"d"],"P"]]]]],[46,[53,[36]]]],[22,["b",[15,"h"],[15,"e"]],[46,[53,[2,[15,"j"],"abort",[7]],[36]]]],[2,[15,"j"],"setMetadata",[7,[17,[15,"d"],"BV"],false]],[22,[28,[15,"i"]],[46,[53,[2,[15,"j"],"setHitData",[7,"link_id",[44]]],[2,[15,"j"],"setHitData",[7,"link_classes",[44]]],[2,[15,"j"],"setHitData",[7,"link_url",[44]]],[2,[15,"j"],"setHitData",[7,"link_domain",[44]]],[2,[15,"j"],"setHitData",[7,"outbound",[44]]]]]]]]],[52,"b",["require","internal.getProductSettingsParameter"]],[52,"c",["require","internal.registerCcdCallback"]],[52,"d",[15,"__module_metadataSchema"]],[52,"e","ae_block_outbound_click"],[52,"f","click"],[36,[8,"A",[15,"g"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_ccdEmPageViewActivity",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"i",[46,"j"],["c",[15,"j"],[51,"",[7,"k"],[22,[30,[21,[2,[15,"k"],"getEventName",[7]],[15,"h"]],[28,[2,[15,"k"],"getMetadata",[7,[17,[15,"e"],"P"]]]]],[46,[53,[36]]]],[22,["b",[15,"j"],[15,"g"]],[46,[53,[2,[15,"k"],"abort",[7]],[36]]]],[22,[28,[2,[15,"k"],"getMetadata",[7,[17,[15,"e"],"BA"]]]],[46,[53,["d",[15,"j"],[17,[15,"f"],"GW"],[2,[15,"k"],"getHitData",[7,[17,[15,"f"],"GW"]]]]]]],[2,[15,"k"],"setMetadata",[7,[17,[15,"e"],"BV"],false]]]]],[52,"b",["require","internal.getProductSettingsParameter"]],[52,"c",["require","internal.registerCcdCallback"]],[52,"d",["require","internal.setRemoteConfigParameter"]],[52,"e",[15,"__module_metadataSchema"]],[52,"f",[15,"__module_gtagSchema"]],[52,"g","ae_block_history"],[52,"h",[17,[15,"f"],"AH"]],[36,[8,"A",[15,"i"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_ccdEmScrollActivity",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"g",[46,"h","i"],["c",[15,"h"],[51,"",[7,"j"],[22,[30,[21,[2,[15,"j"],"getEventName",[7]],[15,"f"]],[28,[2,[15,"j"],"getMetadata",[7,[17,[15,"d"],"P"]]]]],[46,[53,[36]]]],[22,["b",[15,"h"],[15,"e"]],[46,[53,[2,[15,"j"],"abort",[7]],[36]]]],[2,[15,"j"],"setMetadata",[7,[17,[15,"d"],"BV"],false]],[22,[28,[15,"i"]],[46,[53,[2,[15,"j"],"setHitData",[7,"percent_scrolled",[44]]]]]]]]],[52,"b",["require","internal.getProductSettingsParameter"]],[52,"c",["require","internal.registerCcdCallback"]],[52,"d",[15,"__module_metadataSchema"]],[52,"e","ae_block_scroll"],[52,"f","scroll"],[36,[8,"A",[15,"g"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_ccdEmVideoActivity",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"i",[46,"j","k"],["c",[15,"j"],[51,"",[7,"l"],[52,"m",[2,[15,"l"],"getEventName",[7]]],[52,"n",[30,[30,[20,[15,"m"],[15,"f"]],[20,[15,"m"],[15,"g"]]],[20,[15,"m"],[15,"h"]]]],[22,[30,[28,[15,"n"]],[28,[2,[15,"l"],"getMetadata",[7,[17,[15,"d"],"P"]]]]],[46,[53,[36]]]],[22,["b",[15,"j"],[15,"e"]],[46,[53,[2,[15,"l"],"abort",[7]],[36]]]],[2,[15,"l"],"setMetadata",[7,[17,[15,"d"],"BV"],false]],[22,[28,[15,"k"]],[46,[53,[2,[15,"l"],"setHitData",[7,"video_current_time",[44]]],[2,[15,"l"],"setHitData",[7,"video_duration",[44]]],[2,[15,"l"],"setHitData",[7,"video_percent",[44]]],[2,[15,"l"],"setHitData",[7,"video_provider",[44]]],[2,[15,"l"],"setHitData",[7,"video_title",[44]]],[2,[15,"l"],"setHitData",[7,"video_url",[44]]],[2,[15,"l"],"setHitData",[7,"visible",[44]]]]]]]]],[52,"b",["require","internal.getProductSettingsParameter"]],[52,"c",["require","internal.registerCcdCallback"]],[52,"d",[15,"__module_metadataSchema"]],[52,"e","ae_block_video"],[52,"f","video_start"],[52,"g","video_progress"],[52,"h","video_complete"],[36,[8,"A",[15,"i"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_ccdGaRegionScopedSettings",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"j",[46,"m","n","o"],[50,"t",[46,"v"],[52,"w",[16,[15,"i"],[15,"v"]]],[22,[28,[15,"w"]],[46,[36]]],[53,[41,"x"],[3,"x",0],[63,[7,"x"],[23,[15,"x"],[17,[15,"w"],"length"]],[33,[15,"x"],[3,"x",[0,[15,"x"],1]]],[46,[53,[52,"y",[16,[15,"w"],[15,"x"]]],["q",[15,"p"],[17,[15,"y"],"name"],[17,[15,"y"],"value"]]]]]]],[50,"u",[46,"v"],[22,[30,[28,[15,"r"]],[21,[17,[15,"r"],"length"],2]],[46,[53,[36,false]]]],[41,"w"],[3,"w",[16,[15,"v"],[15,"s"]]],[22,[20,[15,"w"],[44]],[46,[53,[3,"w",[16,[15,"v"],[15,"r"]]]]]],[36,[28,[28,[15,"w"]]]]],[22,[28,[15,"n"]],[46,[36]]],[52,"p",[30,[17,[15,"m"],"instanceDestinationId"],[17,["c"],"containerId"]]],[52,"q",["h",[15,"f"],[15,"o"]]],[52,"r",[13,[41,"$0"],[3,"$0",["h",[15,"d"],[15,"o"]]],["$0"]]],[52,"s",[13,[41,"$0"],[3,"$0",["h",[15,"e"],[15,"o"]]],["$0"]]],[53,[41,"v"],[3,"v",0],[63,[7,"v"],[23,[15,"v"],[17,[15,"n"],"length"]],[33,[15,"v"],[3,"v",[0,[15,"v"],1]]],[46,[53,[52,"w",[16,[15,"n"],[15,"v"]]],[22,[30,[17,[15,"w"],"disallowAllRegions"],["u",[17,[15,"w"],"disallowedRegions"]]],[46,[53,["t",[17,[15,"w"],"redactFieldGroup"]]]]]]]]]],[50,"k",[46,"m"],[52,"n",[8]],[22,[28,[15,"m"]],[46,[36,[15,"n"]]]],[52,"o",[2,[15,"m"],"split",[7,","]]],[53,[41,"p"],[3,"p",0],[63,[7,"p"],[23,[15,"p"],[17,[15,"o"],"length"]],[33,[15,"p"],[3,"p",[0,[15,"p"],1]]],[46,[53,[52,"q",[2,[16,[15,"o"],[15,"p"]],"trim",[7]]],[22,[28,[15,"q"]],[46,[6]]],[52,"r",[2,[15,"q"],"split",[7,"-"]]],[52,"s",[16,[15,"r"],0]],[52,"t",[39,[20,[17,[15,"r"],"length"],2],[15,"q"],[44]]],[22,[30,[28,[15,"s"]],[21,[17,[15,"s"],"length"],2]],[46,[53,[6]]]],[22,[1,[21,[15,"t"],[44]],[30,[23,[17,[15,"t"],"length"],4],[18,[17,[15,"t"],"length"],6]]],[46,[53,[6]]]],[43,[15,"n"],[15,"q"],true]]]]],[36,[15,"n"]]],[50,"l",[46,"m"],[22,[28,[17,[15,"m"],"settingsTable"]],[46,[36,[7]]]],[52,"n",[8]],[53,[41,"o"],[3,"o",0],[63,[7,"o"],[23,[15,"o"],[17,[17,[15,"m"],"settingsTable"],"length"]],[33,[15,"o"],[3,"o",[0,[15,"o"],1]]],[46,[53,[52,"p",[16,[17,[15,"m"],"settingsTable"],[15,"o"]]],[52,"q",[17,[15,"p"],"redactFieldGroup"]],[22,[28,[16,[15,"i"],[15,"q"]]],[46,[6]]],[43,[15,"n"],[15,"q"],[8,"redactFieldGroup",[15,"q"],"disallowAllRegions",false,"disallowedRegions",[8]]],[52,"r",[16,[15,"n"],[15,"q"]]],[22,[17,[15,"p"],"disallowAllRegions"],[46,[53,[43,[15,"r"],"disallowAllRegions",true],[6]]]],[43,[15,"r"],"disallowedRegions",["k",[17,[15,"p"],"disallowedRegions"]]]]]]],[36,[2,[15,"b"],"values",[7,[15,"n"]]]]],[52,"b",["require","Object"]],[52,"c",["require","getContainerVersion"]],[52,"d",["require","internal.getCountryCode"]],[52,"e",["require","internal.getRegionCode"]],[52,"f",["require","internal.setRemoteConfigParameter"]],[52,"g",[15,"__module_activities"]],[52,"h",[17,[15,"g"],"A"]],[52,"i",[8,"GOOGLE_SIGNALS",[7,[8,"name","allow_google_signals","value",false]],"DEVICE_AND_GEO",[7,[8,"name","geo_granularity","value",true],[8,"name","redact_device_info","value",true]]]],[36,[8,"A",[15,"j"],"B",[15,"l"]]]],[36,["a"]]]],["$0"]]]
 
]
,"entities":{
"__c":{"2":true,"5":true}
,
"__ccd_auto_redact":{"2":true,"5":true}
,
"__ccd_conversion_marking":{"2":true,"5":true}
,
"__ccd_em_download":{"2":true,"5":true}
,
"__ccd_em_form":{"2":true,"5":true}
,
"__ccd_em_outbound_click":{"2":true,"5":true}
,
"__ccd_em_page_view":{"2":true,"5":true}
,
"__ccd_em_scroll":{"2":true,"5":true}
,
"__ccd_em_site_search":{"2":true,"5":true}
,
"__ccd_em_video":{"2":true,"5":true}
,
"__ccd_ga_first":{"2":true,"5":true}
,
"__ccd_ga_last":{"2":true,"5":true}
,
"__ccd_ga_regscope":{"2":true,"5":true}
,
"__e":{"2":true,"5":true}
,
"__ogt_1p_data_v2":{"2":true,"5":true}
,
"__set_product_settings":{"2":true,"5":true}


}
,"blob":{"1":"1","10":"G-SMNVEQYP6G|GT-M3LVV29P","11":true,"14":"57n0","15":"0","16":"ChAI8JyXxAYQ8p7kwdr81OdHEiUAhzAODJpg3mqHcCAZSTFDG/LIy1nV7Ob7oIaBh3dfIoTboYgFGgIVxg==","17":"","19":"dataLayer","2":true,"20":"","21":"www.googletagmanager.com","22":"eyIwIjoiVk4iLCIxIjoiVk4tMjEiLCIyIjpmYWxzZSwiMyI6Imdvb2dsZS5jb20udm4iLCI0IjoiIiwiNSI6dHJ1ZSwiNiI6ZmFsc2UsIjciOiJhZF9zdG9yYWdlfGFuYWx5dGljc19zdG9yYWdlfGFkX3VzZXJfZGF0YXxhZF9wZXJzb25hbGl6YXRpb24ifQ","23":"google.tagmanager.debugui2.queue","24":"tagassistant.google.com","27":0.005,"3":"www.googletagmanager.com","30":"VN","31":"VN-21","32":true,"34":"G-SMNVEQYP6G","35":"G","36":"https://adservice.google.com/pagead/regclk","37":"__TAGGY_INSTALLED","38":"cct.google","39":"googTaggyReferrer","40":"https://cct.google/taggy/agent.js","41":"google.tagmanager.ta.prodqueue","42":0.01,"43":"{\"keys\":[{\"hpkePublicKey\":{\"params\":{\"aead\":\"AES_128_GCM\",\"kdf\":\"HKDF_SHA256\",\"kem\":\"DHKEM_P256_HKDF_SHA256\"},\"publicKey\":\"BLmFt6UXBhRmCroatpW1SXiUGX8nlIzsjWuo/35QAO+zaS+otiG5QcR9nM1Cps71ya2tmVIsN5veaAal7MHFLEs=\",\"version\":0},\"id\":\"aec78412-6373-47d5-ac96-9fcee93fb999\"},{\"hpkePublicKey\":{\"params\":{\"aead\":\"AES_128_GCM\",\"kdf\":\"HKDF_SHA256\",\"kem\":\"DHKEM_P256_HKDF_SHA256\"},\"publicKey\":\"BC/FqS2VfJxJt+KUoa5szFzBglEsbyx+I9x123cX99SEO7P1N7hO6AIp93nTAdi/z2DFSAto+EqKKdcuaTb9W0s=\",\"version\":0},\"id\":\"a8322124-3ea2-4d88-b25b-86e2f0112cae\"},{\"hpkePublicKey\":{\"params\":{\"aead\":\"AES_128_GCM\",\"kdf\":\"HKDF_SHA256\",\"kem\":\"DHKEM_P256_HKDF_SHA256\"},\"publicKey\":\"BKfFh+mfP+VYN5VmB9shcyG0A1lRYz8Xzw3WGLlsKlBmFEaKsavgS+aJLQV57OOtxcD75yF5XPI4JCpAEVT6aZE=\",\"version\":0},\"id\":\"69d58b45-d2bb-4a7f-9952-57e6e8373ee3\"},{\"hpkePublicKey\":{\"params\":{\"aead\":\"AES_128_GCM\",\"kdf\":\"HKDF_SHA256\",\"kem\":\"DHKEM_P256_HKDF_SHA256\"},\"publicKey\":\"BGKg2rrDYEGZYBnoJcCvLOBw40XwX02uo+UmyosodkDpDhfJRS/gnmzpZxgdB0K64JD4BNvJP8lOXmDgfjDJnr0=\",\"version\":0},\"id\":\"1cfcadd3-649d-4616-a730-b7cbb203d3b2\"},{\"hpkePublicKey\":{\"params\":{\"aead\":\"AES_128_GCM\",\"kdf\":\"HKDF_SHA256\",\"kem\":\"DHKEM_P256_HKDF_SHA256\"},\"publicKey\":\"BIj0YjU0Id8OOxdy8oAkpsYU3WUMzeTX3IB3zolk/AGHi8e4L1Wndgs+eEljcMtqAzqNrV2PUboMi62U86LWEtA=\",\"version\":0},\"id\":\"12ffea68-4f40-48ea-9714-010853b2215c\"}]}","44":"101509157~103116026~103200004~103233427~104684208~104684211","5":"G-SMNVEQYP6G","6":"204592928","8":"res_ts:1737613465593938,srv_cl:786632200,ds:live,cv:1","9":"G-SMNVEQYP6G"}
,"permissions":{
"__c":{}
,
"__ccd_auto_redact":{}
,
"__ccd_conversion_marking":{}
,
"__ccd_em_download":{"listen_data_layer":{"accessType":"specific","allowedEvents":["gtm.linkClick"]},"access_template_storage":{},"detect_link_click_events":{"allowWaitForTags":""}}
,
"__ccd_em_form":{"access_template_storage":{},"listen_data_layer":{"accessType":"specific","allowedEvents":["gtm.formInteract","gtm.formSubmit"]},"detect_form_submit_events":{"allowWaitForTags":""},"detect_form_interaction_events":{}}
,
"__ccd_em_outbound_click":{"get_url":{"urlParts":"any","queriesAllowed":"any"},"listen_data_layer":{"accessType":"specific","allowedEvents":["gtm.linkClick"]},"access_template_storage":{},"detect_link_click_events":{"allowWaitForTags":""}}
,
"__ccd_em_page_view":{"listen_data_layer":{"accessType":"specific","allowedEvents":["gtm.historyChange-v2"]},"access_template_storage":{},"detect_history_change_events":{}}
,
"__ccd_em_scroll":{"listen_data_layer":{"accessType":"specific","allowedEvents":["gtm.scrollDepth"]},"access_template_storage":{},"detect_scroll_events":{}}
,
"__ccd_em_site_search":{"get_url":{"urlParts":"any","queriesAllowed":"any"},"read_container_data":{}}
,
"__ccd_em_video":{"listen_data_layer":{"accessType":"specific","allowedEvents":["gtm.video"]},"access_template_storage":{},"detect_youtube_activity_events":{"allowFixMissingJavaScriptApi":false}}
,
"__ccd_ga_first":{}
,
"__ccd_ga_last":{}
,
"__ccd_ga_regscope":{"read_container_data":{}}
,
"__e":{"read_event_data":{"eventDataAccess":"specific","keyPatterns":["event"]}}
,
"__ogt_1p_data_v2":{"detect_user_provided_data":{"limitDataSources":true,"allowAutoDataSources":true,"allowManualDataSources":false,"allowCodeDataSources":false}}
,
"__set_product_settings":{}


}



,"security_groups":{
"google":[
"__c"
,
"__ccd_auto_redact"
,
"__ccd_conversion_marking"
,
"__ccd_em_download"
,
"__ccd_em_form"
,
"__ccd_em_outbound_click"
,
"__ccd_em_page_view"
,
"__ccd_em_scroll"
,
"__ccd_em_site_search"
,
"__ccd_em_video"
,
"__ccd_ga_first"
,
"__ccd_ga_last"
,
"__ccd_ga_regscope"
,
"__e"
,
"__ogt_1p_data_v2"
,
"__set_product_settings"

]


}



};




var k,aa=function(a){var b=0;return function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}}},ba=typeof Object.defineProperties=="function"?Object.defineProperty:function(a,b,c){if(a==Array.prototype||a==Object.prototype)return a;a[b]=c.value;return a},ca=function(a){for(var b=["object"==typeof globalThis&&globalThis,a,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global],c=0;c<b.length;++c){var d=b[c];if(d&&d.Math==Math)return d}throw Error("Cannot find global object");
},ea=ca(this),fa=typeof Symbol==="function"&&typeof Symbol("x")==="symbol",ha={},la={},ma=function(a,b,c){if(!c||a!=null){var d=la[b];if(d==null)return a[b];var e=a[d];return e!==void 0?e:a[b]}},na=function(a,b,c){if(b)a:{var d=a.split("."),e=d.length===1,f=d[0],g;!e&&f in ha?g=ha:g=ea;for(var h=0;h<d.length-1;h++){var m=d[h];if(!(m in g))break a;g=g[m]}var n=d[d.length-1],p=fa&&c==="es6"?g[n]:null,q=b(p);if(q!=null)if(e)ba(ha,n,{configurable:!0,writable:!0,value:q});else if(q!==p){if(la[n]===void 0){var r=
Math.random()*1E9>>>0;la[n]=fa?ea.Symbol(n):"$jscp$"+r+"$"+n}ba(g,la[n],{configurable:!0,writable:!0,value:q})}}};na("Symbol",function(a){if(a)return a;var b=function(f,g){this.C=f;ba(this,"description",{configurable:!0,writable:!0,value:g})};b.prototype.toString=function(){return this.C};var c="jscomp_symbol_"+(Math.random()*1E9>>>0)+"_",d=0,e=function(f){if(this instanceof e)throw new TypeError("Symbol is not a constructor");return new b(c+(f||"")+"_"+d++,f)};return e},"es6");
var oa=typeof Object.create=="function"?Object.create:function(a){var b=function(){};b.prototype=a;return new b},pa;if(fa&&typeof Object.setPrototypeOf=="function")pa=Object.setPrototypeOf;else{var qa;a:{var ra={a:!0},ta={};try{ta.__proto__=ra;qa=ta.a;break a}catch(a){}qa=!1}pa=qa?function(a,b){a.__proto__=b;if(a.__proto__!==b)throw new TypeError(a+" is not extensible");return a}:null}
var ua=pa,va=function(a,b){a.prototype=oa(b.prototype);a.prototype.constructor=a;if(ua)ua(a,b);else for(var c in b)if(c!="prototype")if(Object.defineProperties){var d=Object.getOwnPropertyDescriptor(b,c);d&&Object.defineProperty(a,c,d)}else a[c]=b[c];a.Fq=b.prototype},l=function(a){var b=typeof ha.Symbol!="undefined"&&ha.Symbol.iterator&&a[ha.Symbol.iterator];if(b)return b.call(a);if(typeof a.length=="number")return{next:aa(a)};throw Error(String(a)+" is not an iterable or ArrayLike");},xa=function(a){for(var b,
c=[];!(b=a.next()).done;)c.push(b.value);return c},ya=function(a){return a instanceof Array?a:xa(l(a))},Aa=function(a){return za(a,a)},za=function(a,b){a.raw=b;Object.freeze&&(Object.freeze(a),Object.freeze(b));return a},Ba=fa&&typeof ma(Object,"assign")=="function"?ma(Object,"assign"):function(a,b){for(var c=1;c<arguments.length;c++){var d=arguments[c];if(d)for(var e in d)Object.prototype.hasOwnProperty.call(d,e)&&(a[e]=d[e])}return a};na("Object.assign",function(a){return a||Ba},"es6");
var Ca=function(){for(var a=Number(this),b=[],c=a;c<arguments.length;c++)b[c-a]=arguments[c];return b};/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
var Da=this||self,Ea=function(a,b){function c(){}c.prototype=b.prototype;a.Fq=b.prototype;a.prototype=new c;a.prototype.constructor=a;a.Fr=function(d,e,f){for(var g=Array(arguments.length-2),h=2;h<arguments.length;h++)g[h-2]=arguments[h];return b.prototype[e].apply(d,g)}};var Fa=function(a,b){this.type=a;this.data=b};var Ga=function(){this.map={};this.C={}};Ga.prototype.get=function(a){return this.map["dust."+a]};Ga.prototype.set=function(a,b){var c="dust."+a;this.C.hasOwnProperty(c)||(this.map[c]=b)};Ga.prototype.has=function(a){return this.map.hasOwnProperty("dust."+a)};Ga.prototype.remove=function(a){var b="dust."+a;this.C.hasOwnProperty(b)||delete this.map[b]};
var Ha=function(a,b){var c=[],d;for(d in a.map)if(a.map.hasOwnProperty(d)){var e=d.substring(5);switch(b){case 1:c.push(e);break;case 2:c.push(a.map[d]);break;case 3:c.push([e,a.map[d]])}}return c};Ga.prototype.Aa=function(){return Ha(this,1)};Ga.prototype.Ac=function(){return Ha(this,2)};Ga.prototype.ac=function(){return Ha(this,3)};var Ia=function(){};Ia.prototype.reset=function(){};var Ja=function(a,b){this.P=a;this.parent=b;this.N=this.C=void 0;this.Db=!1;this.H=function(c,d,e){return c.apply(d,e)};this.values=new Ga};Ja.prototype.add=function(a,b){Ka(this,a,b,!1)};Ja.prototype.oh=function(a,b){Ka(this,a,b,!0)};var Ka=function(a,b,c,d){if(!a.Db)if(d){var e=a.values;e.set(b,c);e.C["dust."+b]=!0}else a.values.set(b,c)};k=Ja.prototype;k.set=function(a,b){this.Db||(!this.values.has(a)&&this.parent&&this.parent.has(a)?this.parent.set(a,b):this.values.set(a,b))};
k.get=function(a){return this.values.has(a)?this.values.get(a):this.parent?this.parent.get(a):void 0};k.has=function(a){return!!this.values.has(a)||!(!this.parent||!this.parent.has(a))};k.ub=function(){var a=new Ja(this.P,this);this.C&&a.Ob(this.C);a.Vc(this.H);a.Od(this.N);return a};k.Fd=function(){return this.P};k.Ob=function(a){this.C=a};k.bm=function(){return this.C};k.Vc=function(a){this.H=a};k.cj=function(){return this.H};k.Wa=function(){this.Db=!0};k.Od=function(a){this.N=a};k.wb=function(){return this.N};var La=function(a,b){this.fa=a;this.parent=b;this.P=this.H=void 0;this.Db=!1;this.N=function(c,d,e){return c.apply(d,e)};this.C=new Map;this.R=new Set};La.prototype.add=function(a,b){Ma(this,a,b,!1)};La.prototype.oh=function(a,b){Ma(this,a,b,!0)};var Ma=function(a,b,c,d){a.Db||a.R.has(b)||(d&&a.R.add(b),a.C.set(b,c))};k=La.prototype;k.set=function(a,b){this.Db||(!this.C.has(a)&&this.parent&&this.parent.has(a)?this.parent.set(a,b):this.R.has(a)||this.C.set(a,b))};
k.get=function(a){return this.C.has(a)?this.C.get(a):this.parent?this.parent.get(a):void 0};k.has=function(a){return!!this.C.has(a)||!(!this.parent||!this.parent.has(a))};k.ub=function(){var a=new La(this.fa,this);this.H&&a.Ob(this.H);a.Vc(this.N);a.Od(this.P);return a};k.Fd=function(){return this.fa};k.Ob=function(a){this.H=a};k.bm=function(){return this.H};k.Vc=function(a){this.N=a};k.cj=function(){return this.N};k.Wa=function(){this.Db=!0};k.Od=function(a){this.P=a};k.wb=function(){return this.P};var Na=function(a,b,c){var d;d=Error.call(this,a.message);this.message=d.message;"stack"in d&&(this.stack=d.stack);this.qm=a;this.Tl=c===void 0?!1:c;this.debugInfo=[];this.C=b};va(Na,Error);var Oa=function(a){return a instanceof Na?a:new Na(a,void 0,!0)};var Pa=[],Qa={};function Ra(a){return Pa[a]===void 0?!1:Pa[a]};var Sa=new Map;function Ta(a,b){for(var c,d=l(b),e=d.next();!e.done&&!(c=Ua(a,e.value),c instanceof Fa);e=d.next());return c}
function Ua(a,b){try{if(Ra(16)){var c=b[0],d=b.slice(1),e=String(c),f=Sa.has(e)?Sa.get(e):a.get(e);if(!f||typeof f.invoke!=="function")throw Oa(Error("Attempting to execute non-function "+b[0]+"."));return f.apply(a,d)}var g=l(b),h=g.next().value,m=xa(g),n=a.get(String(h));if(!n||typeof n.invoke!=="function")throw Oa(Error("Attempting to execute non-function "+b[0]+"."));return n.invoke.apply(n,[a].concat(ya(m)))}catch(q){var p=a.bm();p&&p(q,b.context?{id:b[0],line:b.context.line}:null);throw q;}}
;var Xa=function(){this.H=new Ia;this.C=Ra(16)?new La(this.H):new Ja(this.H)};k=Xa.prototype;k.Fd=function(){return this.H};k.Ob=function(a){this.C.Ob(a)};k.Vc=function(a){this.C.Vc(a)};k.execute=function(a){return this.Dj([a].concat(ya(Ca.apply(1,arguments))))};k.Dj=function(){for(var a,b=l(Ca.apply(0,arguments)),c=b.next();!c.done;c=b.next())a=Ua(this.C,c.value);return a};
k.ho=function(a){var b=Ca.apply(1,arguments),c=this.C.ub();c.Od(a);for(var d,e=l(b),f=e.next();!f.done;f=e.next())d=Ua(c,f.value);return d};k.Wa=function(){this.C.Wa()};var Ya=function(){this.Ea=!1;this.aa=new Ga};k=Ya.prototype;k.get=function(a){return this.aa.get(a)};k.set=function(a,b){this.Ea||this.aa.set(a,b)};k.has=function(a){return this.aa.has(a)};k.remove=function(a){this.Ea||this.aa.remove(a)};k.Aa=function(){return this.aa.Aa()};k.Ac=function(){return this.aa.Ac()};k.ac=function(){return this.aa.ac()};k.Wa=function(){this.Ea=!0};k.Db=function(){return this.Ea};function Za(){for(var a=$a,b={},c=0;c<a.length;++c)b[a[c]]=c;return b}function ab(){var a="ABCDEFGHIJKLMNOPQRSTUVWXYZ";a+=a.toLowerCase()+"0123456789-_";return a+"."}var $a,bb;function cb(a){$a=$a||ab();bb=bb||Za();for(var b=[],c=0;c<a.length;c+=3){var d=c+1<a.length,e=c+2<a.length,f=a.charCodeAt(c),g=d?a.charCodeAt(c+1):0,h=e?a.charCodeAt(c+2):0,m=f>>2,n=(f&3)<<4|g>>4,p=(g&15)<<2|h>>6,q=h&63;e||(q=64,d||(p=64));b.push($a[m],$a[n],$a[p],$a[q])}return b.join("")}
function db(a){function b(m){for(;d<a.length;){var n=a.charAt(d++),p=bb[n];if(p!=null)return p;if(!/^[\s\xa0]*$/.test(n))throw Error("Unknown base64 encoding at char: "+n);}return m}$a=$a||ab();bb=bb||Za();for(var c="",d=0;;){var e=b(-1),f=b(0),g=b(64),h=b(64);if(h===64&&e===-1)return c;c+=String.fromCharCode(e<<2|f>>4);g!==64&&(c+=String.fromCharCode(f<<4&240|g>>2),h!==64&&(c+=String.fromCharCode(g<<6&192|h)))}};var eb={};function fb(a,b){eb[a]=eb[a]||[];eb[a][b]=!0}function gb(){eb.GTAG_EVENT_FEATURE_CHANNEL=hb}function ib(a){var b=eb[a];if(!b||b.length===0)return"";for(var c=[],d=0,e=0;e<b.length;e++)e%8===0&&e>0&&(c.push(String.fromCharCode(d)),d=0),b[e]&&(d|=1<<e%8);d>0&&c.push(String.fromCharCode(d));return cb(c.join("")).replace(/\.+$/,"")}function jb(){for(var a=[],b=eb.fdr||[],c=0;c<b.length;c++)b[c]&&a.push(c);return a.length>0?a:void 0};function kb(){}function lb(a){return typeof a==="function"}function mb(a){return typeof a==="string"}function nb(a){return typeof a==="number"&&!isNaN(a)}function ob(a){return Array.isArray(a)?a:[a]}function pb(a,b){if(a&&Array.isArray(a))for(var c=0;c<a.length;c++)if(a[c]&&b(a[c]))return a[c]}function qb(a,b){if(!nb(a)||!nb(b)||a>b)a=0,b=2147483647;return Math.floor(Math.random()*(b-a+1)+a)}
function rb(a,b){for(var c=new sb,d=0;d<a.length;d++)c.set(a[d],!0);for(var e=0;e<b.length;e++)if(c.get(b[e]))return!0;return!1}function tb(a,b){for(var c in a)Object.prototype.hasOwnProperty.call(a,c)&&b(c,a[c])}function ub(a){return!!a&&(Object.prototype.toString.call(a)==="[object Arguments]"||Object.prototype.hasOwnProperty.call(a,"callee"))}function vb(a){return Math.round(Number(a))||0}function wb(a){return"false"===String(a).toLowerCase()?!1:!!a}
function xb(a){var b=[];if(Array.isArray(a))for(var c=0;c<a.length;c++)b.push(String(a[c]));return b}function yb(a){return a?a.replace(/^\s+|\s+$/g,""):""}function zb(){return new Date(Date.now())}function Ab(){return zb().getTime()}var sb=function(){this.prefix="gtm.";this.values={}};sb.prototype.set=function(a,b){this.values[this.prefix+a]=b};sb.prototype.get=function(a){return this.values[this.prefix+a]};sb.prototype.contains=function(a){return this.get(a)!==void 0};
function Bb(a,b,c){return a&&a.hasOwnProperty(b)?a[b]:c}function Cb(a){var b=a;return function(){if(b){var c=b;b=void 0;try{c()}catch(d){}}}}function Eb(a,b){for(var c in b)b.hasOwnProperty(c)&&(a[c]=b[c])}function Fb(a,b){for(var c=[],d=0;d<a.length;d++)c.push(a[d]),c.push.apply(c,b[a[d]]||[]);return c}function Gb(a,b){return a.length>=b.length&&a.substring(0,b.length)===b}
function Hb(a,b,c){c=c||[];for(var d=a,e=0;e<b.length-1;e++){if(!d.hasOwnProperty(b[e]))return;d=d[b[e]];if(c.indexOf(d)>=0)return}return d}function Ib(a,b){for(var c={},d=c,e=a.split("."),f=0;f<e.length-1;f++)d=d[e[f]]={};d[e[e.length-1]]=b;return c}var Jb=/^\w{1,9}$/;function Kb(a,b){a=a||{};b=b||",";var c=[];tb(a,function(d,e){Jb.test(d)&&e&&c.push(d)});return c.join(b)}function Lb(a,b){function c(){e&&++d===b&&(e(),e=null,c.done=!0)}var d=0,e=a;c.done=!1;return c}
function Mb(a){if(!a)return a;var b=a;try{b=decodeURIComponent(a)}catch(d){}var c=b.split(",");return c.length===2&&c[0]===c[1]?c[0]:a}
function Nb(a,b,c){function d(n){var p=n.split("=")[0];if(a.indexOf(p)<0)return n;if(c!==void 0)return p+"="+c}function e(n){return n.split("&").map(d).filter(function(p){return p!==void 0}).join("&")}var f=b.href.split(/[?#]/)[0],g=b.search,h=b.hash;g[0]==="?"&&(g=g.substring(1));h[0]==="#"&&(h=h.substring(1));g=e(g);h=e(h);g!==""&&(g="?"+g);h!==""&&(h="#"+h);var m=""+f+g+h;m[m.length-1]==="/"&&(m=m.substring(0,m.length-1));return m}
function Ob(a){for(var b=0;b<3;++b)try{var c=decodeURIComponent(a).replace(/\+/g," ");if(c===a)break;a=c}catch(d){return""}return a}function Pb(){var a=x.crypto||x.msCrypto;if(a&&a.getRandomValues)try{var b=new Uint8Array(25);a.getRandomValues(b);return btoa(String.fromCharCode.apply(String,ya(b))).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")}catch(c){}};/*

 Copyright Google LLC
 SPDX-License-Identifier: Apache-2.0
*/
var Qb=globalThis.trustedTypes,Rb;function Sb(){var a=null;if(!Qb)return a;try{var b=function(c){return c};a=Qb.createPolicy("goog#html",{createHTML:b,createScript:b,createScriptURL:b})}catch(c){}return a}function Tb(){Rb===void 0&&(Rb=Sb());return Rb};var Ub=function(a){this.C=a};Ub.prototype.toString=function(){return this.C+""};function Wb(a){var b=a,c=Tb(),d=c?c.createScriptURL(b):b;return new Ub(d)}function Xb(a){if(a instanceof Ub)return a.C;throw Error("");};var Yb=Aa([""]),Zb=za(["\x00"],["\\0"]),$b=za(["\n"],["\\n"]),ac=za(["\x00"],["\\u0000"]);function cc(a){return a.toString().indexOf("`")===-1}cc(function(a){return a(Yb)})||cc(function(a){return a(Zb)})||cc(function(a){return a($b)})||cc(function(a){return a(ac)});var dc=function(a){this.C=a};dc.prototype.toString=function(){return this.C};var ec=function(a){this.Up=a};function fc(a){return new ec(function(b){return b.substr(0,a.length+1).toLowerCase()===a+":"})}var hc=[fc("data"),fc("http"),fc("https"),fc("mailto"),fc("ftp"),new ec(function(a){return/^[^:]*([/?#]|$)/.test(a)})];function ic(a){var b;b=b===void 0?hc:b;if(a instanceof dc)return a;for(var c=0;c<b.length;++c){var d=b[c];if(d instanceof ec&&d.Up(a))return new dc(a)}}var jc=/^\s*(?!javascript:)(?:[\w+.-]+:|[^:/?#]*(?:[/?#]|$))/i;
function kc(a){var b;if(a instanceof dc)if(a instanceof dc)b=a.C;else throw Error("");else b=jc.test(a)?a:void 0;return b};function lc(a,b){var c=kc(b);c!==void 0&&(a.action=c)};function mc(a,b){throw Error(b===void 0?"unexpected value "+a+"!":b);};var nc=function(a){this.C=a};nc.prototype.toString=function(){return this.C+""};var pc=function(){this.C=oc[0].toLowerCase()};pc.prototype.toString=function(){return this.C};function qc(a,b){var c=[new pc];if(c.length===0)throw Error("");var d=c.map(function(f){var g;if(f instanceof pc)g=f.C;else throw Error("");return g}),e=b.toLowerCase();if(d.every(function(f){return e.indexOf(f)!==0}))throw Error('Attribute "'+b+'" does not match any of the allowed prefixes.');a.setAttribute(b,"true")};var rc=Array.prototype.indexOf?function(a,b){return Array.prototype.indexOf.call(a,b,void 0)}:function(a,b){if(typeof a==="string")return typeof b!=="string"||b.length!=1?-1:a.indexOf(b,0);for(var c=0;c<a.length;c++)if(c in a&&a[c]===b)return c;return-1};"ARTICLE SECTION NAV ASIDE H1 H2 H3 H4 H5 H6 HEADER FOOTER ADDRESS P HR PRE BLOCKQUOTE OL UL LH LI DL DT DD FIGURE FIGCAPTION MAIN DIV EM STRONG SMALL S CITE Q DFN ABBR RUBY RB RT RTC RP DATA TIME CODE VAR SAMP KBD SUB SUP I B U MARK BDI BDO SPAN BR WBR NOBR INS DEL PICTURE PARAM TRACK MAP TABLE CAPTION COLGROUP COL TBODY THEAD TFOOT TR TD TH SELECT DATALIST OPTGROUP OPTION OUTPUT PROGRESS METER FIELDSET LEGEND DETAILS SUMMARY MENU DIALOG SLOT CANVAS FONT CENTER ACRONYM BASEFONT BIG DIR HGROUP STRIKE TT".split(" ").concat(["BUTTON",
"INPUT"]);function sc(a){return a===null?"null":a===void 0?"undefined":a};var x=window,tc=window.history,A=document,uc=navigator;function vc(){var a;try{a=uc.serviceWorker}catch(b){return}return a}var wc=A.currentScript,xc=wc&&wc.src;function yc(a,b){var c=x,d=c[a];c[a]=d===void 0?b:d;return c[a]}function zc(a){return(uc.userAgent||"").indexOf(a)!==-1}function Ac(){return zc("Firefox")||zc("FxiOS")}function Bc(){return(zc("GSA")||zc("GoogleApp"))&&(zc("iPhone")||zc("iPad"))}function Cc(){return zc("Edg/")||zc("EdgA/")||zc("EdgiOS/")}
var Dc={async:1,nonce:1,onerror:1,onload:1,src:1,type:1},Ec={onload:1,src:1,width:1,height:1,style:1};function Fc(a,b,c){b&&tb(b,function(d,e){d=d.toLowerCase();c.hasOwnProperty(d)||a.setAttribute(d,e)})}
function Gc(a,b,c,d,e){var f=A.createElement("script");Fc(f,d,Dc);f.type="text/javascript";f.async=d&&d.async===!1?!1:!0;var g;g=Wb(sc(a));f.src=Xb(g);var h,m=f.ownerDocument;m=m===void 0?document:m;var n,p,q=(p=(n=m).querySelector)==null?void 0:p.call(n,"script[nonce]");(h=q==null?"":q.nonce||q.getAttribute("nonce")||"")&&f.setAttribute("nonce",h);b&&(f.onload=b);c&&(f.onerror=c);if(e)e.appendChild(f);else{var r=A.getElementsByTagName("script")[0]||A.body||A.head;r.parentNode.insertBefore(f,r)}return f}
function Hc(){if(xc){var a=xc.toLowerCase();if(a.indexOf("https://")===0)return 2;if(a.indexOf("http://")===0)return 3}return 1}function Ic(a,b,c,d,e,f){f=f===void 0?!0:f;var g=e,h=!1;g||(g=A.createElement("iframe"),h=!0);Fc(g,c,Ec);d&&tb(d,function(n,p){g.dataset[n]=p});f&&(g.height="0",g.width="0",g.style.display="none",g.style.visibility="hidden");a!==void 0&&(g.src=a);if(h){var m=A.body&&A.body.lastChild||A.body||A.head;m.parentNode.insertBefore(g,m)}b&&(g.onload=b);return g}
function Jc(a,b,c,d){return Kc(a,b,c,d)}function Lc(a,b,c,d){a.addEventListener&&a.addEventListener(b,c,!!d)}function Mc(a,b,c){a.removeEventListener&&a.removeEventListener(b,c,!1)}function Nc(a){x.setTimeout(a,0)}function Oc(a,b){return a&&b&&a.attributes&&a.attributes[b]?a.attributes[b].value:null}function Pc(a){var b=a.innerText||a.textContent||"";b&&b!==" "&&(b=b.replace(/^[\s\xa0]+/g,""),b=b.replace(/[\s\xa0]+$/g,""));b&&(b=b.replace(/(\xa0+|\s{2,}|\n|\r\t)/g," "));return b}
function Qc(a){var b=A.createElement("div"),c=b,d,e=sc("A<div>"+a+"</div>"),f=Tb(),g=f?f.createHTML(e):e;d=new nc(g);if(c.nodeType===1&&/^(script|style)$/i.test(c.tagName))throw Error("");var h;if(d instanceof nc)h=d.C;else throw Error("");c.innerHTML=h;b=b.lastChild;for(var m=[];b&&b.firstChild;)m.push(b.removeChild(b.firstChild));return m}
function Rc(a,b,c){c=c||100;for(var d={},e=0;e<b.length;e++)d[b[e]]=!0;for(var f=a,g=0;f&&g<=c;g++){if(d[String(f.tagName).toLowerCase()])return f;f=f.parentElement}return null}function Sc(a,b,c){var d;try{d=uc.sendBeacon&&uc.sendBeacon(a)}catch(e){fb("TAGGING",15)}d?b==null||b():Kc(a,b,c)}function Tc(a,b){try{return uc.sendBeacon(a,b)}catch(c){fb("TAGGING",15)}return!1}var Uc={cache:"no-store",credentials:"include",keepalive:!0,method:"POST",mode:"no-cors",redirect:"follow"};
function Vc(a,b,c,d,e){if(Wc()){var f=ma(Object,"assign").call(Object,{},Uc);b&&(f.body=b);c&&(c.attributionReporting&&(f.attributionReporting=c.attributionReporting),c.browsingTopics&&(f.browsingTopics=c.browsingTopics),c.credentials&&(f.credentials=c.credentials),c.mode&&(f.mode=c.mode),c.method&&(f.method=c.method));try{var g=x.fetch(a,f);if(g)return g.then(function(m){m&&(m.ok||m.status===0)?d==null||d():e==null||e()}).catch(function(){e==null||e()}),!0}catch(m){}}if(c&&c.Eh)return e==null||e(),
!1;if(b){var h=Tc(a,b);h?d==null||d():e==null||e();return h}Xc(a,d,e);return!0}function Wc(){return typeof x.fetch==="function"}function Yc(a,b){var c=a[b];c&&typeof c.animVal==="string"&&(c=c.animVal);return c}function Zc(){var a=x.performance;if(a&&lb(a.now))return a.now()}
function $c(){var a,b=x.performance;if(b&&b.getEntriesByType)try{var c=b.getEntriesByType("navigation");c&&c.length>0&&(a=c[0].type)}catch(d){return"e"}if(!a)return"u";switch(a){case "navigate":return"n";case "back_forward":return"h";case "reload":return"r";case "prerender":return"p";default:return"x"}}function ad(){return x.performance||void 0}function bd(){var a=x.webPixelsManager;return a?a.createShopifyExtend!==void 0:!1}
var Kc=function(a,b,c,d){var e=new Image(1,1);Fc(e,d,{});e.onload=function(){e.onload=null;b&&b()};e.onerror=function(){e.onerror=null;c&&c()};e.src=a;return e},Xc=Sc;function cd(a,b){return this.evaluate(a)&&this.evaluate(b)}function dd(a,b){return this.evaluate(a)===this.evaluate(b)}function ed(a,b){return this.evaluate(a)||this.evaluate(b)}function fd(a,b){var c=this.evaluate(a),d=this.evaluate(b);return String(c).indexOf(String(d))>-1}function gd(a,b){var c=String(this.evaluate(a)),d=String(this.evaluate(b));return c.substring(0,d.length)===d}
function hd(a,b){var c=this.evaluate(a),d=this.evaluate(b);switch(c){case "pageLocation":var e=x.location.href;d instanceof Ya&&d.get("stripProtocol")&&(e=e.replace(/^https?:\/\//,""));return e}};/*
 jQuery (c) 2005, 2012 jQuery Foundation, Inc. jquery.org/license.
*/
var id=/\[object (Boolean|Number|String|Function|Array|Date|RegExp)\]/,jd=function(a){if(a==null)return String(a);var b=id.exec(Object.prototype.toString.call(Object(a)));return b?b[1].toLowerCase():"object"},kd=function(a,b){return Object.prototype.hasOwnProperty.call(Object(a),b)},ld=function(a){if(!a||jd(a)!="object"||a.nodeType||a==a.window)return!1;try{if(a.constructor&&!kd(a,"constructor")&&!kd(a.constructor.prototype,"isPrototypeOf"))return!1}catch(c){return!1}for(var b in a);return b===void 0||
kd(a,b)},md=function(a,b){var c=b||(jd(a)=="array"?[]:{}),d;for(d in a)if(kd(a,d)){var e=a[d];jd(e)=="array"?(jd(c[d])!="array"&&(c[d]=[]),c[d]=md(e,c[d])):ld(e)?(ld(c[d])||(c[d]={}),c[d]=md(e,c[d])):c[d]=e}return c};function nd(a){if(a==void 0||Array.isArray(a)||ld(a))return!0;switch(typeof a){case "boolean":case "number":case "string":case "function":return!0}return!1}function od(a){return typeof a==="number"&&a>=0&&isFinite(a)&&a%1===0||typeof a==="string"&&a[0]!=="-"&&a===""+parseInt(a)};var pd=function(a){a=a===void 0?[]:a;this.aa=new Ga;this.values=[];this.Ea=!1;for(var b in a)a.hasOwnProperty(b)&&(od(b)?this.values[Number(b)]=a[Number(b)]:this.aa.set(b,a[b]))};k=pd.prototype;k.toString=function(a){if(a&&a.indexOf(this)>=0)return"";for(var b=[],c=0;c<this.values.length;c++){var d=this.values[c];d===null||d===void 0?b.push(""):d instanceof pd?(a=a||[],a.push(this),b.push(d.toString(a)),a.pop()):b.push(String(d))}return b.join(",")};
k.set=function(a,b){if(!this.Ea)if(a==="length"){if(!od(b))throw Oa(Error("RangeError: Length property must be a valid integer."));this.values.length=Number(b)}else od(a)?this.values[Number(a)]=b:this.aa.set(a,b)};k.get=function(a){return a==="length"?this.length():od(a)?this.values[Number(a)]:this.aa.get(a)};k.length=function(){return this.values.length};k.Aa=function(){for(var a=this.aa.Aa(),b=0;b<this.values.length;b++)this.values.hasOwnProperty(b)&&a.push(String(b));return a};
k.Ac=function(){for(var a=this.aa.Ac(),b=0;b<this.values.length;b++)this.values.hasOwnProperty(b)&&a.push(this.values[b]);return a};k.ac=function(){for(var a=this.aa.ac(),b=0;b<this.values.length;b++)this.values.hasOwnProperty(b)&&a.push([String(b),this.values[b]]);return a};k.remove=function(a){od(a)?delete this.values[Number(a)]:this.Ea||this.aa.remove(a)};k.pop=function(){return this.values.pop()};
k.push=function(){var a=Ca.apply(0,arguments);return Ra(17)&&arguments.length===1?this.values.push(arguments[0]):this.values.push.apply(this.values,ya(a))};k.shift=function(){return this.values.shift()};k.splice=function(a,b){var c=Ca.apply(2,arguments);return b===void 0&&c.length===0?new pd(this.values.splice(a)):new pd(this.values.splice.apply(this.values,[a,b||0].concat(ya(c))))};k.unshift=function(){return this.values.unshift.apply(this.values,ya(Ca.apply(0,arguments)))};
k.has=function(a){return od(a)&&this.values.hasOwnProperty(a)||this.aa.has(a)};k.Wa=function(){this.Ea=!0;Object.freeze(this.values)};k.Db=function(){return this.Ea};function qd(a){for(var b=[],c=0;c<a.length();c++)a.has(c)&&(b[c]=a.get(c));return b};var rd=function(a,b){this.functionName=a;this.Sc=b;this.aa=new Ga;this.Ea=!1};k=rd.prototype;k.toString=function(){return this.functionName};k.getName=function(){return this.functionName};k.getKeys=function(){return new pd(this.Aa())};k.invoke=function(a){var b=Ca.apply(1,arguments);return Ra(18)?this.Sc.apply(new sd(this,a),b):this.Sc.call.apply(this.Sc,[new sd(this,a)].concat(ya(b)))};k.apply=function(a,b){return this.Sc.apply(new sd(this,a),b)};
k.Mb=function(a){var b=Ca.apply(1,arguments);try{return this.invoke.apply(this,[a].concat(ya(b)))}catch(c){}};k.get=function(a){return this.aa.get(a)};k.set=function(a,b){this.Ea||this.aa.set(a,b)};k.has=function(a){return this.aa.has(a)};k.remove=function(a){this.Ea||this.aa.remove(a)};k.Aa=function(){return this.aa.Aa()};k.Ac=function(){return this.aa.Ac()};k.ac=function(){return this.aa.ac()};k.Wa=function(){this.Ea=!0};k.Db=function(){return this.Ea};var td=function(a,b){rd.call(this,a,b)};
va(td,rd);var ud=function(a,b){rd.call(this,a,b)};va(ud,rd);var sd=function(a,b){this.Sc=a;this.K=b};sd.prototype.evaluate=function(a){var b=this.K;return Array.isArray(a)?Ua(b,a):a};sd.prototype.getName=function(){return this.Sc.getName()};sd.prototype.Fd=function(){return this.K.Fd()};var vd=function(){this.map=new Map};vd.prototype.set=function(a,b){this.map.set(a,b)};vd.prototype.get=function(a){return this.map.get(a)};var wd=function(){this.keys=[];this.values=[]};wd.prototype.set=function(a,b){this.keys.push(a);this.values.push(b)};wd.prototype.get=function(a){var b=this.keys.indexOf(a);if(b>-1)return this.values[b]};function xd(){try{return Map?new vd:new wd}catch(a){return new wd}};var yd=function(a){if(a instanceof yd)return a;if(nd(a))throw Error("Type of given value has an equivalent Pixie type.");this.value=a};yd.prototype.getValue=function(){return this.value};yd.prototype.toString=function(){return String(this.value)};var Ad=function(a){this.promise=a;this.Ea=!1;this.aa=new Ga;this.aa.set("then",zd(this));this.aa.set("catch",zd(this,!0));this.aa.set("finally",zd(this,!1,!0))};k=Ad.prototype;k.get=function(a){return this.aa.get(a)};k.set=function(a,b){this.Ea||this.aa.set(a,b)};k.has=function(a){return this.aa.has(a)};k.remove=function(a){this.Ea||this.aa.remove(a)};k.Aa=function(){return this.aa.Aa()};k.Ac=function(){return this.aa.Ac()};k.ac=function(){return this.aa.ac()};
var zd=function(a,b,c){b=b===void 0?!1:b;c=c===void 0?!1:c;return new td("",function(d,e){b&&(e=d,d=void 0);c&&(e=d);d instanceof td||(d=void 0);e instanceof td||(e=void 0);var f=this.K.ub(),g=function(m){return function(n){try{return c?(m.invoke(f),a.promise):m.invoke(f,n)}catch(p){return Promise.reject(p instanceof Error?new yd(p):String(p))}}},h=a.promise.then(d&&g(d),e&&g(e));return new Ad(h)})};Ad.prototype.Wa=function(){this.Ea=!0};Ad.prototype.Db=function(){return this.Ea};function Bd(a,b,c){var d=xd(),e=function(g,h){for(var m=g.Aa(),n=0;n<m.length;n++)h[m[n]]=f(g.get(m[n]))},f=function(g){if(g===null||g===void 0)return g;var h=d.get(g);if(h)return h;if(g instanceof pd){var m=[];d.set(g,m);for(var n=g.Aa(),p=0;p<n.length;p++)m[n[p]]=f(g.get(n[p]));return m}if(g instanceof Ad)return g.promise.then(function(u){return Bd(u,b,1)},function(u){return Promise.reject(Bd(u,b,1))});if(g instanceof Ya){var q={};d.set(g,q);e(g,q);return q}if(g instanceof td){var r=function(){for(var u=
[],v=0;v<arguments.length;v++)u[v]=Cd(arguments[v],b,c);var w=new Ja(b?b.Fd():new Ia);b&&w.Od(b.wb());return f(Ra(16)?g.apply(w,u):g.invoke.apply(g,[w].concat(ya(u))))};d.set(g,r);e(g,r);return r}var t=!1;switch(c){case 1:t=!0;break;case 2:t=!1;break;case 3:t=!1;break;default:}if(g instanceof yd&&t)return g.getValue();switch(typeof g){case "boolean":case "number":case "string":case "undefined":return g;
case "object":if(g===null)return null}};return f(a)}
function Cd(a,b,c){var d=xd(),e=function(g,h){for(var m in g)g.hasOwnProperty(m)&&h.set(m,f(g[m]))},f=function(g){var h=d.get(g);if(h)return h;if(Array.isArray(g)||ub(g)){var m=new pd;d.set(g,m);for(var n in g)g.hasOwnProperty(n)&&m.set(n,f(g[n]));return m}if(ld(g)){var p=new Ya;d.set(g,p);e(g,p);return p}if(typeof g==="function"){var q=new td("",function(){for(var u=Ca.apply(0,arguments),v=[],w=0;w<u.length;w++)v[w]=Bd(this.evaluate(u[w]),b,c);return f(this.K.cj()(g,g,v))});d.set(g,q);e(g,q);return q}var r=typeof g;if(g===null||r==="string"||r==="number"||r==="boolean")return g;var t=!1;switch(c){case 1:t=!0;break;case 2:t=!1;break;default:}if(g!==void 0&&t)return new yd(g)};return f(a)};var Dd={supportedMethods:"concat every filter forEach hasOwnProperty indexOf join lastIndexOf map pop push reduce reduceRight reverse shift slice some sort splice unshift toString".split(" "),concat:function(a){for(var b=[],c=0;c<this.length();c++)b.push(this.get(c));for(var d=1;d<arguments.length;d++)if(arguments[d]instanceof pd)for(var e=arguments[d],f=0;f<e.length();f++)b.push(e.get(f));else b.push(arguments[d]);return new pd(b)},every:function(a,b){for(var c=this.length(),d=0;d<this.length()&&
d<c;d++)if(this.has(d)&&!b.invoke(a,this.get(d),d,this))return!1;return!0},filter:function(a,b){for(var c=this.length(),d=[],e=0;e<this.length()&&e<c;e++)this.has(e)&&b.invoke(a,this.get(e),e,this)&&d.push(this.get(e));return new pd(d)},forEach:function(a,b){for(var c=this.length(),d=0;d<this.length()&&d<c;d++)this.has(d)&&b.invoke(a,this.get(d),d,this)},hasOwnProperty:function(a,b){return this.has(b)},indexOf:function(a,b,c){var d=this.length(),e=c===void 0?0:Number(c);e<0&&(e=Math.max(d+e,0));for(var f=
e;f<d;f++)if(this.has(f)&&this.get(f)===b)return f;return-1},join:function(a,b){for(var c=[],d=0;d<this.length();d++)c.push(this.get(d));return c.join(b)},lastIndexOf:function(a,b,c){var d=this.length(),e=d-1;c!==void 0&&(e=c<0?d+c:Math.min(c,e));for(var f=e;f>=0;f--)if(this.has(f)&&this.get(f)===b)return f;return-1},map:function(a,b){for(var c=this.length(),d=[],e=0;e<this.length()&&e<c;e++)this.has(e)&&(d[e]=b.invoke(a,this.get(e),e,this));return new pd(d)},pop:function(){return this.pop()},push:function(a){return this.push.apply(this,
ya(Ca.apply(1,arguments)))},reduce:function(a,b,c){var d=this.length(),e,f=0;if(c!==void 0)e=c;else{if(d===0)throw Oa(Error("TypeError: Reduce on List with no elements."));for(var g=0;g<d;g++)if(this.has(g)){e=this.get(g);f=g+1;break}if(g===d)throw Oa(Error("TypeError: Reduce on List with no elements."));}for(var h=f;h<d;h++)this.has(h)&&(e=b.invoke(a,e,this.get(h),h,this));return e},reduceRight:function(a,b,c){var d=this.length(),e,f=d-1;if(c!==void 0)e=c;else{if(d===0)throw Oa(Error("TypeError: ReduceRight on List with no elements."));
for(var g=1;g<=d;g++)if(this.has(d-g)){e=this.get(d-g);f=d-(g+1);break}if(g>d)throw Oa(Error("TypeError: ReduceRight on List with no elements."));}for(var h=f;h>=0;h--)this.has(h)&&(e=b.invoke(a,e,this.get(h),h,this));return e},reverse:function(){for(var a=qd(this),b=a.length-1,c=0;b>=0;b--,c++)a.hasOwnProperty(b)?this.set(c,a[b]):this.remove(c);return this},shift:function(){return this.shift()},slice:function(a,b,c){var d=this.length();b===void 0&&(b=0);b=b<0?Math.max(d+b,0):Math.min(b,d);c=c===
void 0?d:c<0?Math.max(d+c,0):Math.min(c,d);c=Math.max(b,c);for(var e=[],f=b;f<c;f++)e.push(this.get(f));return new pd(e)},some:function(a,b){for(var c=this.length(),d=0;d<this.length()&&d<c;d++)if(this.has(d)&&b.invoke(a,this.get(d),d,this))return!0;return!1},sort:function(a,b){var c=qd(this);b===void 0?c.sort():c.sort(function(e,f){return Number(b.invoke(a,e,f))});for(var d=0;d<c.length;d++)c.hasOwnProperty(d)?this.set(d,c[d]):this.remove(d);return this},splice:function(a,b,c){return this.splice.apply(this,
[b,c].concat(ya(Ca.apply(3,arguments))))},toString:function(){return this.toString()},unshift:function(a){return this.unshift.apply(this,ya(Ca.apply(1,arguments)))}};var Ed={charAt:1,concat:1,indexOf:1,lastIndexOf:1,match:1,replace:1,search:1,slice:1,split:1,substring:1,toLowerCase:1,toLocaleLowerCase:1,toString:1,toUpperCase:1,toLocaleUpperCase:1,trim:1},Fd=new Fa("break"),Gd=new Fa("continue");function Hd(a,b){return this.evaluate(a)+this.evaluate(b)}function Id(a,b){return this.evaluate(a)&&this.evaluate(b)}
function Jd(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c);if(!(f instanceof pd))throw Error("Error: Non-List argument given to Apply instruction.");if(d===null||d===void 0)throw Oa(Error("TypeError: Can't read property "+e+" of "+d+"."));var g=typeof d==="number";if(typeof d==="boolean"||g){if(e==="toString"){if(g&&f.length()){var h=Bd(f.get(0));try{return d.toString(h)}catch(v){}}return d.toString()}throw Oa(Error("TypeError: "+d+"."+e+" is not a function."));}if(typeof d===
"string"){if(Ed.hasOwnProperty(e)){var m=2;m=1;var n=Bd(f,void 0,m);return Cd(d[e].apply(d,n),this.K)}throw Oa(Error("TypeError: "+e+" is not a function"));}if(d instanceof pd){if(d.has(e)){var p=d.get(String(e));if(p instanceof td){var q=qd(f);return Ra(16)?p.apply(this.K,q):p.invoke.apply(p,[this.K].concat(ya(q)))}throw Oa(Error("TypeError: "+e+" is not a function"));
}if(Dd.supportedMethods.indexOf(e)>=0){var r=qd(f);return Dd[e].call.apply(Dd[e],[d,this.K].concat(ya(r)))}}if(d instanceof td||d instanceof Ya||d instanceof Ad){if(d.has(e)){var t=d.get(e);if(t instanceof td){var u=qd(f);return Ra(16)?t.apply(this.K,u):t.invoke.apply(t,[this.K].concat(ya(u)))}throw Oa(Error("TypeError: "+e+" is not a function"));}if(e==="toString")return d instanceof td?d.getName():d.toString();if(e==="hasOwnProperty")return d.has(f.get(0))}if(d instanceof yd&&e==="toString")return d.toString();
throw Oa(Error("TypeError: Object has no '"+e+"' property."));}function Kd(a,b){a=this.evaluate(a);if(typeof a!=="string")throw Error("Invalid key name given for assignment.");var c=this.K;if(!c.has(a))throw Error("Attempting to assign to undefined value "+b);var d=this.evaluate(b);c.set(a,d);return d}function Ld(){var a=Ca.apply(0,arguments),b=this.K.ub(),c=Ta(b,a);if(c instanceof Fa)return c}function Md(){return Fd}
function Nd(a){for(var b=this.evaluate(a),c=0;c<b.length;c++){var d=this.evaluate(b[c]);if(d instanceof Fa)return d}}function Od(){for(var a=this.K,b=0;b<arguments.length-1;b+=2){var c=arguments[b];if(typeof c==="string"){var d=this.evaluate(arguments[b+1]);a.oh(c,d)}}}function Pd(){return Gd}function Qd(a,b){return new Fa(a,this.evaluate(b))}
function Rd(a,b){var c=Ca.apply(2,arguments),d;if(Ra(17)){for(var e=[],f=this.evaluate(b),g=0;g<f.length;g++)e.push(f[g]);d=new pd(e)}else{d=new pd;for(var h=this.evaluate(b),m=0;m<h.length;m++)d.push(h[m])}var n=[51,a,d].concat(ya(c));this.K.add(a,this.evaluate(n))}function Td(a,b){return this.evaluate(a)/this.evaluate(b)}function Ud(a,b){var c=this.evaluate(a),d=this.evaluate(b),e=c instanceof yd,f=d instanceof yd;return e||f?e&&f?c.getValue()===d.getValue():!1:c==d}
function Vd(){for(var a,b=0;b<arguments.length;b++)a=this.evaluate(arguments[b]);return a}function Wd(a,b,c,d){for(var e=0;e<b();e++){var f=a(c(e)),g=Ta(f,d);if(g instanceof Fa){if(g.type==="break")break;if(g.type==="return")return g}}}function Xd(a,b,c){if(typeof b==="string")return Wd(a,function(){return b.length},function(f){return f},c);if(b instanceof Ya||b instanceof Ad||b instanceof pd||b instanceof td){var d=b.Aa(),e=d.length;return Wd(a,function(){return e},function(f){return d[f]},c)}}
function Yd(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.K;return Xd(function(h){g.set(d,h);return g},e,f)}function Zd(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.K;return Xd(function(h){var m=g.ub();m.oh(d,h);return m},e,f)}function $d(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.K;return Xd(function(h){var m=g.ub();m.add(d,h);return m},e,f)}
function ae(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.K;return be(function(h){g.set(d,h);return g},e,f)}function ce(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.K;return be(function(h){var m=g.ub();m.oh(d,h);return m},e,f)}function de(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.K;return be(function(h){var m=g.ub();m.add(d,h);return m},e,f)}
function be(a,b,c){if(typeof b==="string")return Wd(a,function(){return b.length},function(d){return b[d]},c);if(b instanceof pd)return Wd(a,function(){return b.length()},function(d){return b.get(d)},c);throw Oa(Error("The value is not iterable."));}
function ee(a,b,c,d){function e(q,r){for(var t=0;t<f.length();t++){var u=f.get(t);r.add(u,q.get(u))}}var f=this.evaluate(a);if(!(f instanceof pd))throw Error("TypeError: Non-List argument given to ForLet instruction.");var g=this.K,h=this.evaluate(d),m=g.ub();for(e(g,m);Ua(m,b);){var n=Ta(m,h);if(n instanceof Fa){if(n.type==="break")break;if(n.type==="return")return n}var p=g.ub();e(m,p);Ua(p,c);m=p}}
function fe(a,b){var c=Ca.apply(2,arguments),d=this.K,e=this.evaluate(b);if(!(e instanceof pd))throw Error("Error: non-List value given for Fn argument names.");return new td(a,function(){return function(){var f=Ca.apply(0,arguments),g=d.ub();g.wb()===void 0&&g.Od(this.K.wb());for(var h=[],m=0;m<f.length;m++){var n=this.evaluate(f[m]);h[m]=n}for(var p=e.get("length"),q=0;q<p;q++)q<h.length?g.add(e.get(q),h[q]):g.add(e.get(q),void 0);g.add("arguments",new pd(h));var r=Ta(g,c);if(r instanceof Fa)return r.type===
"return"?r.data:r}}())}function ge(a){var b=this.evaluate(a),c=this.K;if(he&&!c.has(b))throw new ReferenceError(b+" is not defined.");return c.get(b)}
function ie(a,b){var c,d=this.evaluate(a),e=this.evaluate(b);if(d===void 0||d===null)throw Oa(Error("TypeError: Cannot read properties of "+d+" (reading '"+e+"')"));if(d instanceof Ya||d instanceof Ad||d instanceof pd||d instanceof td)c=d.get(e);else if(typeof d==="string")e==="length"?c=d.length:od(e)&&(c=d[e]);else if(d instanceof yd)return;return c}function je(a,b){return this.evaluate(a)>this.evaluate(b)}function ke(a,b){return this.evaluate(a)>=this.evaluate(b)}
function le(a,b){var c=this.evaluate(a),d=this.evaluate(b);c instanceof yd&&(c=c.getValue());d instanceof yd&&(d=d.getValue());return c===d}function me(a,b){return!le.call(this,a,b)}function ne(a,b,c){var d=[];this.evaluate(a)?d=this.evaluate(b):c&&(d=this.evaluate(c));var e=Ta(this.K,d);if(e instanceof Fa)return e}var he=!1;
function oe(a,b){return this.evaluate(a)<this.evaluate(b)}function pe(a,b){return this.evaluate(a)<=this.evaluate(b)}function qe(){if(Ra(17)){for(var a=[],b=0;b<arguments.length;b++){var c=this.evaluate(arguments[b]);a.push(c)}return new pd(a)}for(var d=new pd,e=0;e<arguments.length;e++){var f=this.evaluate(arguments[e]);d.push(f)}return d}function re(){for(var a=new Ya,b=0;b<arguments.length-1;b+=2){var c=String(this.evaluate(arguments[b])),d=this.evaluate(arguments[b+1]);a.set(c,d)}return a}
function se(a,b){return this.evaluate(a)%this.evaluate(b)}function te(a,b){return this.evaluate(a)*this.evaluate(b)}function ue(a){return-this.evaluate(a)}function ve(a){return!this.evaluate(a)}function we(a,b){return!Ud.call(this,a,b)}function xe(){return null}function ye(a,b){return this.evaluate(a)||this.evaluate(b)}function ze(a,b){var c=this.evaluate(a);this.evaluate(b);return c}function Ae(a){return this.evaluate(a)}function Be(){return Ca.apply(0,arguments)}
function Ce(a){return new Fa("return",this.evaluate(a))}function De(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c);if(d===null||d===void 0)throw Oa(Error("TypeError: Can't set property "+e+" of "+d+"."));(d instanceof td||d instanceof pd||d instanceof Ya)&&d.set(String(e),f);return f}function Ee(a,b){return this.evaluate(a)-this.evaluate(b)}
function Fe(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c);if(!Array.isArray(e)||!Array.isArray(f))throw Error("Error: Malformed switch instruction.");for(var g,h=!1,m=0;m<e.length;m++)if(h||d===this.evaluate(e[m]))if(g=this.evaluate(f[m]),g instanceof Fa){var n=g.type;if(n==="break")return;if(n==="return"||n==="continue")return g}else h=!0;if(f.length===e.length+1&&(g=this.evaluate(f[f.length-1]),g instanceof Fa&&(g.type==="return"||g.type==="continue")))return g}
function Ge(a,b,c){return this.evaluate(a)?this.evaluate(b):this.evaluate(c)}function He(a){var b=this.evaluate(a);return b instanceof td?"function":typeof b}function Ie(){for(var a=this.K,b=0;b<arguments.length;b++){var c=arguments[b];typeof c!=="string"||a.add(c,void 0)}}
function Je(a,b,c,d){var e=this.evaluate(d);if(this.evaluate(c)){var f=Ta(this.K,e);if(f instanceof Fa){if(f.type==="break")return;if(f.type==="return")return f}}for(;this.evaluate(a);){var g=Ta(this.K,e);if(g instanceof Fa){if(g.type==="break")break;if(g.type==="return")return g}this.evaluate(b)}}function Ke(a){return~Number(this.evaluate(a))}function Le(a,b){return Number(this.evaluate(a))<<Number(this.evaluate(b))}function Me(a,b){return Number(this.evaluate(a))>>Number(this.evaluate(b))}
function Ne(a,b){return Number(this.evaluate(a))>>>Number(this.evaluate(b))}function Oe(a,b){return Number(this.evaluate(a))&Number(this.evaluate(b))}function Pe(a,b){return Number(this.evaluate(a))^Number(this.evaluate(b))}function Qe(a,b){return Number(this.evaluate(a))|Number(this.evaluate(b))}function Re(){}
function Se(a,b,c){try{var d=this.evaluate(b);if(d instanceof Fa)return d}catch(h){if(!(h instanceof Na&&h.Tl))throw h;var e=this.K.ub();a!==""&&(h instanceof Na&&(h=h.qm),e.add(a,new yd(h)));var f=this.evaluate(c),g=Ta(e,f);if(g instanceof Fa)return g}}function Te(a,b){var c,d;try{d=this.evaluate(a)}catch(f){if(!(f instanceof Na&&f.Tl))throw f;c=f}var e=this.evaluate(b);if(e instanceof Fa)return e;if(c)throw c;if(d instanceof Fa)return d};var Ve=function(){this.C=new Xa;Ue(this)};Ve.prototype.execute=function(a){return this.C.Dj(a)};var Ue=function(a){var b=function(c,d){var e=new ud(String(c),d);e.Wa();var f=String(c);a.C.C.set(f,e);Sa.set(f,e)};b("map",re);b("and",cd);b("contains",fd);b("equals",dd);b("or",ed);b("startsWith",gd);b("variable",hd)};Ve.prototype.Ob=function(a){this.C.Ob(a)};var Xe=function(){this.H=!1;this.C=new Xa;We(this);this.H=!0};Xe.prototype.execute=function(a){return Ze(this.C.Dj(a))};var $e=function(a,b,c){return Ze(a.C.ho(b,c))};Xe.prototype.Wa=function(){this.C.Wa()};
var We=function(a){var b=function(c,d){var e=String(c),f=new ud(e,d);f.Wa();a.C.C.set(e,f);Sa.set(e,f)};b(0,Hd);b(1,Id);b(2,Jd);b(3,Kd);b(56,Oe);b(57,Le);b(58,Ke);b(59,Qe);b(60,Me);b(61,Ne);b(62,Pe);b(53,Ld);b(4,Md);b(5,Nd);b(68,Se);b(52,Od);b(6,Pd);b(49,Qd);b(7,qe);b(8,re);b(9,Nd);b(50,Rd);b(10,Td);b(12,Ud);b(13,Vd);b(67,Te);b(51,fe);b(47,Yd);b(54,Zd);b(55,$d);b(63,ee);b(64,ae);b(65,ce);b(66,de);b(15,ge);b(16,ie);b(17,ie);b(18,je);b(19,ke);b(20,le);b(21,me);b(22,ne);b(23,oe);b(24,pe);b(25,se);b(26,
te);b(27,ue);b(28,ve);b(29,we);b(45,xe);b(30,ye);b(32,ze);b(33,ze);b(34,Ae);b(35,Ae);b(46,Be);b(36,Ce);b(43,De);b(37,Ee);b(38,Fe);b(39,Ge);b(40,He);b(44,Re);b(41,Ie);b(42,Je)};Xe.prototype.Fd=function(){return this.C.Fd()};Xe.prototype.Ob=function(a){this.C.Ob(a)};Xe.prototype.Vc=function(a){this.C.Vc(a)};
function Ze(a){if(a instanceof Fa||a instanceof td||a instanceof pd||a instanceof Ya||a instanceof Ad||a instanceof yd||a===null||a===void 0||typeof a==="string"||typeof a==="number"||typeof a==="boolean")return a};var af=function(a){this.message=a};function bf(a){a.Mr=!0;return a};var cf=bf(function(a){return typeof a==="string"});function df(a){var b="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[a];return b===void 0?new af("Value "+a+" can not be encoded in web-safe base64 dictionary."):b};function ef(a){switch(a){case 1:return"1";case 2:case 4:return"0";default:return"-"}};var ff=/^[1-9a-zA-Z_-][1-9a-c][1-9a-v]\d$/;function gf(a,b){for(var c="",d=!0;a>7;){var e=a&31;a>>=5;d?d=!1:e|=32;c=""+df(e)+c}a<<=2;d||(a|=32);return c=""+df(a|b)+c}
function hf(a,b){var c;var d=a.Uc,e=a.Bh;d===void 0?c="":(e||(e=0),c=""+gf(1,1)+df(d<<2|e));var f=a.Sl,g=a.Po,h="4"+c+(f?""+gf(2,1)+df(f):"")+(g?""+gf(12,1)+df(g):""),m,n=a.Ej;m=n&&ff.test(n)?""+gf(3,2)+n:"";var p,q=a.Aj;p=q?""+gf(4,1)+df(q):"";var r;var t=a.ctid;if(t&&b){var u=gf(5,3),v=t.split("-"),w=v[0].toUpperCase();if(w!=="GTM"&&w!=="OPT")r="";else{var y=v[1];r=""+u+df(1+y.length)+(a.gm||0)+y}}else r="";var z=a.Eq,C=a.we,D=a.Pa,G=a.Qr,I=h+m+p+r+(z?""+gf(6,1)+df(z):"")+(C?""+gf(7,3)+df(C.length)+
C:"")+(D?""+gf(8,3)+df(D.length)+D:"")+(G?""+gf(9,3)+df(G.length)+G:""),M;var T=a.Ul;T=T===void 0?{}:T;for(var da=[],N=l(Object.keys(T)),W=N.next();!W.done;W=N.next()){var ia=W.value;da[Number(ia)]=T[ia]}if(da.length){var ka=gf(10,3),Y;if(da.length===0)Y=df(0);else{for(var X=[],ja=0,wa=!1,sa=0;sa<da.length;sa++){wa=!0;var Va=sa%6;da[sa]&&(ja|=1<<Va);Va===5&&(X.push(df(ja)),ja=0,wa=!1)}wa&&X.push(df(ja));Y=X.join("")}var Wa=Y;M=""+ka+df(Wa.length)+Wa}else M="";var Db=a.rm,Vb=a.uq;return I+M+(Db?""+
gf(11,3)+df(Db.length)+Db:"")+(Vb?""+gf(13,3)+df(Vb.length)+Vb:"")};var jf=function(){function a(b){return{toString:function(){return b}}}return{Sm:a("consent"),Sj:a("convert_case_to"),Tj:a("convert_false_to"),Uj:a("convert_null_to"),Vj:a("convert_true_to"),Wj:a("convert_undefined_to"),Sq:a("debug_mode_metadata"),Ua:a("function"),Bi:a("instance_name"),ko:a("live_only"),lo:a("malware_disabled"),METADATA:a("metadata"),oo:a("original_activity_id"),nr:a("original_vendor_template_id"),mr:a("once_on_load"),no:a("once_per_event"),rl:a("once_per_load"),rr:a("priority_override"),
wr:a("respected_consent_types"),Cl:a("setup_tags"),mh:a("tag_id"),Kl:a("teardown_tags")}}();var Ff;var Gf=[],Hf=[],If=[],Jf=[],Kf=[],Lf,Mf,Nf;function Of(a){Nf=Nf||a}
function Pf(){for(var a=data.resource||{},b=a.macros||[],c=0;c<b.length;c++)Gf.push(b[c]);for(var d=a.tags||[],e=0;e<d.length;e++)Jf.push(d[e]);for(var f=a.predicates||[],g=0;g<f.length;g++)If.push(f[g]);for(var h=a.rules||[],m=0;m<h.length;m++){for(var n=h[m],p={},q=0;q<n.length;q++){var r=n[q][0];p[r]=Array.prototype.slice.call(n[q],1);r!=="if"&&r!=="unless"||Qf(p[r])}Hf.push(p)}}
function Qf(a){}var Rf,Sf=[],Tf=[];function Uf(a,b){var c={};c[jf.Ua]="__"+a;for(var d in b)b.hasOwnProperty(d)&&(c["vtp_"+d]=b[d]);return c}
function Vf(a,b,c){try{return Mf(Wf(a,b,c))}catch(d){JSON.stringify(a)}return 2}
var Wf=function(a,b,c){c=c||[];var d={},e;for(e in a)a.hasOwnProperty(e)&&(d[e]=Xf(a[e],b,c));return d},Xf=function(a,b,c){if(Array.isArray(a)){var d;switch(a[0]){case "function_id":return a[1];case "list":d=[];for(var e=1;e<a.length;e++)d.push(Xf(a[e],b,c));return d;case "macro":var f=a[1];if(c[f])return;var g=Gf[f];if(!g||b.isBlocked(g))return;c[f]=!0;var h=String(g[jf.Bi]);try{var m=Wf(g,b,c);m.vtp_gtmEventId=b.id;b.priorityId&&(m.vtp_gtmPriorityId=b.priorityId);d=Yf(m,{event:b,index:f,type:2,
name:h});Rf&&(d=Rf.Qo(d,m))}catch(z){b.logMacroError&&b.logMacroError(z,Number(f),h),d=!1}c[f]=!1;return d;case "map":d={};for(var n=1;n<a.length;n+=2)d[Xf(a[n],b,c)]=Xf(a[n+1],b,c);return d;case "template":d=[];for(var p=!1,q=1;q<a.length;q++){var r=Xf(a[q],b,c);Nf&&(p=p||Nf.Rp(r));d.push(r)}return Nf&&p?Nf.Vo(d):d.join("");case "escape":d=Xf(a[1],b,c);if(Nf&&Array.isArray(a[1])&&a[1][0]==="macro"&&Nf.Sp(a))return Nf.jq(d);d=String(d);for(var t=2;t<a.length;t++)qf[a[t]]&&(d=qf[a[t]](d));return d;
case "tag":var u=a[1];if(!Jf[u])throw Error("Unable to resolve tag reference "+u+".");return{Yl:a[2],index:u};case "zb":var v={arg0:a[2],arg1:a[3],ignore_case:a[5]};v[jf.Ua]=a[1];var w=Vf(v,b,c),y=!!a[4];return y||w!==2?y!==(w===1):null;default:throw Error("Attempting to expand unknown Value type: "+a[0]+".");}}return a},Yf=function(a,b){var c=a[jf.Ua],d=b&&b.event;if(!c)throw Error("Error: No function name given for function call.");var e=Lf[c],f=b&&b.type===2&&(d==null?void 0:d.reportMacroDiscrepancy)&&
e&&Sf.indexOf(c)!==-1,g={},h={},m;for(m in a)a.hasOwnProperty(m)&&Gb(m,"vtp_")&&(e&&(g[m]=a[m]),!e||f)&&(h[m.substring(4)]=a[m]);e&&d&&d.cachedModelValues&&(g.vtp_gtmCachedValues=d.cachedModelValues);if(b){if(b.name==null){var n;a:{var p=b.type,q=b.index;if(q==null)n="";else{var r;switch(p){case 2:r=Gf[q];break;case 1:r=Jf[q];break;default:n="";break a}var t=r&&r[jf.Bi];n=t?String(t):""}}b.name=n}e&&(g.vtp_gtmEntityIndex=b.index,g.vtp_gtmEntityName=b.name)}var u,v,w;if(f&&Tf.indexOf(c)===-1){Tf.push(c);
var y=Ab();u=e(g);var z=Ab()-y,C=Ab();v=Ff(c,h,b);w=z-(Ab()-C)}else if(e&&(u=e(g)),!e||f)v=Ff(c,h,b);f&&d&&(d.reportMacroDiscrepancy(d.id,c,void 0,!0),nd(u)?(Array.isArray(u)?Array.isArray(v):ld(u)?ld(v):typeof u==="function"?typeof v==="function":u===v)||d.reportMacroDiscrepancy(d.id,c):u!==v&&d.reportMacroDiscrepancy(d.id,c),w!==void 0&&d.reportMacroDiscrepancy(d.id,c,w));return e?u:v};var Zf=function(a,b,c){var d;d=Error.call(this,c);this.message=d.message;"stack"in d&&(this.stack=d.stack);this.permissionId=a;this.parameters=b;this.name="PermissionError"};va(Zf,Error);Zf.prototype.getMessage=function(){return this.message};function $f(a,b){if(Array.isArray(a)){Object.defineProperty(a,"context",{value:{line:b[0]}});for(var c=1;c<a.length;c++)$f(a[c],b[c])}};function ag(){return function(a,b){var c;var d=bg;a instanceof Na?(a.C=d,c=a):c=new Na(a,d);var e=c;b&&e.debugInfo.push(b);throw e;}}function bg(a){if(!a.length)return a;a.push({id:"main",line:0});for(var b=a.length-1;b>0;b--)nb(a[b].id)&&a.splice(b++,1);for(var c=a.length-1;c>0;c--)a[c].line=a[c-1].line;a.splice(0,1);return a};function cg(a){function b(r){for(var t=0;t<r.length;t++)d[r[t]]=!0}for(var c=[],d=[],e=dg(a),f=0;f<Hf.length;f++){var g=Hf[f],h=eg(g,e);if(h){for(var m=g.add||[],n=0;n<m.length;n++)c[m[n]]=!0;b(g.block||[])}else h===null&&b(g.block||[]);}for(var p=[],q=0;q<Jf.length;q++)c[q]&&!d[q]&&(p[q]=!0);return p}
function eg(a,b){for(var c=a["if"]||[],d=0;d<c.length;d++){var e=b(c[d]);if(e===0)return!1;if(e===2)return null}for(var f=a.unless||[],g=0;g<f.length;g++){var h=b(f[g]);if(h===2)return null;if(h===1)return!1}return!0}function dg(a){var b=[];return function(c){b[c]===void 0&&(b[c]=Vf(If[c],a));return b[c]}};function fg(a,b){b[jf.Sj]&&typeof a==="string"&&(a=b[jf.Sj]===1?a.toLowerCase():a.toUpperCase());b.hasOwnProperty(jf.Uj)&&a===null&&(a=b[jf.Uj]);b.hasOwnProperty(jf.Wj)&&a===void 0&&(a=b[jf.Wj]);b.hasOwnProperty(jf.Vj)&&a===!0&&(a=b[jf.Vj]);b.hasOwnProperty(jf.Tj)&&a===!1&&(a=b[jf.Tj]);return a};var gg=function(){this.C={}},ig=function(a,b){var c=hg.C,d;(d=c.C)[a]!=null||(d[a]=[]);c.C[a].push(function(){return b.apply(null,ya(Ca.apply(0,arguments)))})};function jg(a,b,c,d){if(a)for(var e=0;e<a.length;e++){var f=void 0,g="A policy function denied the permission request";try{f=a[e](b,c,d),g+="."}catch(h){g=typeof h==="string"?g+(": "+h):h instanceof Error?g+(": "+h.message):g+"."}if(!f)throw new Zf(c,d,g);}}
function kg(a,b,c){return function(d){if(d){var e=a.C[d],f=a.C.all;if(e||f){var g=c.apply(void 0,[d].concat(ya(Ca.apply(1,arguments))));jg(e,b,d,g);jg(f,b,d,g)}}}};var og=function(){var a=data.permissions||{},b=lg.ctid,c=this;this.H={};this.C=new gg;var d={},e={},f=kg(this.C,b,function(g){return g&&d[g]?d[g].apply(void 0,[g].concat(ya(Ca.apply(1,arguments)))):{}});tb(a,function(g,h){function m(p){var q=Ca.apply(1,arguments);if(!n[p])throw mg(p,{},"The requested additional permission "+p+" is not configured.");f.apply(null,[p].concat(ya(q)))}var n={};tb(h,function(p,q){var r=ng(p,q);n[p]=r.assert;d[p]||(d[p]=r.T);r.Ql&&!e[p]&&(e[p]=r.Ql)});c.H[g]=function(p,
q){var r=n[p];if(!r)throw mg(p,{},"The requested permission "+p+" is not configured.");var t=Array.prototype.slice.call(arguments,0);r.apply(void 0,t);f.apply(void 0,t);var u=e[p];u&&u.apply(null,[m].concat(ya(t.slice(1))))}})},pg=function(a){return hg.H[a]||function(){}};
function ng(a,b){var c=Uf(a,b);c.vtp_permissionName=a;c.vtp_createPermissionError=mg;try{return Yf(c)}catch(d){return{assert:function(e){throw new Zf(e,{},"Permission "+e+" is unknown.");},T:function(){throw new Zf(a,{},"Permission "+a+" is unknown.");}}}}function mg(a,b,c){return new Zf(a,b,c)};var qg=!1;var rg={};rg.Jm=wb('');rg.fp=wb('');
var vg=function(a){var b={},c=0;tb(a,function(e,f){if(f!=null){var g=(""+f).replace(/~/g,"~~");if(sg.hasOwnProperty(e))b[sg[e]]=g;else if(tg.hasOwnProperty(e)){var h=tg[e];b.hasOwnProperty(h)||(b[h]=g)}else if(e==="category")for(var m=g.split("/",5),n=0;n<m.length;n++){var p=b,q=ug[n],r=m[n];p.hasOwnProperty(q)||(p[q]=r)}else if(c<27){var t=String.fromCharCode(c<10?48+c:65+c-10);b["k"+t]=(""+String(e)).replace(/~/g,"~~");b["v"+t]=g;c++}}});var d=[];tb(b,function(e,f){d.push(""+e+f)});return d.join("~")},
sg={item_id:"id",item_name:"nm",item_brand:"br",item_category:"ca",item_category2:"c2",item_category3:"c3",item_category4:"c4",item_category5:"c5",item_variant:"va",price:"pr",quantity:"qt",coupon:"cp",item_list_name:"ln",index:"lp",item_list_id:"li",discount:"ds",affiliation:"af",promotion_id:"pi",promotion_name:"pn",creative_name:"cn",creative_slot:"cs",location_id:"lo"},tg={id:"id",name:"nm",brand:"br",variant:"va",list_name:"ln",list_position:"lp",list:"ln",position:"lp",creative:"cn"},ug=["ca",
"c2","c3","c4","c5"];function wg(a,b){if(a==="")return b;var c=Number(a);return isNaN(c)?b:c};var xg=[];function yg(a){switch(a){case 1:return 0;case 216:return 15;case 222:return 18;case 38:return 12;case 219:return 9;case 220:return 10;case 53:return 1;case 54:return 2;case 52:return 6;case 203:return 16;case 75:return 3;case 103:return 13;case 197:return 14;case 114:return 11;case 116:return 4;case 221:return 17;case 135:return 8;case 136:return 5}}function zg(a,b){xg[a]=b;var c=yg(a);c!==void 0&&(Pa[c]=b)}function B(a){zg(a,!0)}B(39);
B(34);B(35);B(36);B(56);
B(145);B(153);
B(144);
B(120);B(5);B(111);
B(139);B(87);B(92);B(159);
B(132);B(20);B(72);
B(113);B(154);B(116);zg(23,!1),B(24);Qa[1]=wg('1',6E4);Qa[3]=wg('10',1);Qa[2]=wg('',50);B(29);
Ag(26,25);
B(37);B(9);
B(91);B(123);B(158);B(71);
B(136);B(127);
B(27);B(69);
B(135);B(95);B(38);
B(103);B(112);B(63);B(152);
B(101);
B(122);B(121);
B(134);
B(22);

B(19);
B(90);
B(114);B(59);
B(164);
B(175);
B(185);
B(186);B(192);
B(200);B(202);
B(210);
B(213);

function E(a){return!!xg[a]}function Ag(a,b){for(var c=!1,d=!1,e=0;c===d;)if(c=((Math.random()*4294967296|0)&1)===0,d=((Math.random()*4294967296|0)&1)===0,e++,e>30)return;c?B(b):B(a)};
var Bg=function(){this.events=[];this.C="";this.ra={};this.baseUrl="";this.N=0;this.P=this.H=!1;this.endpoint=0;E(89)&&(this.P=!0)};Bg.prototype.add=function(a){return this.R(a)?(this.events.push(a),this.C=a.H,this.ra=a.ra,this.baseUrl=a.baseUrl,this.N+=a.P,this.H=a.N,this.endpoint=a.endpoint,this.destinationId=a.destinationId,this.fa=a.eventId,this.ma=a.priorityId,!0):!1};Bg.prototype.R=function(a){return this.events.length?this.events.length>=20||a.P+this.N>=16384?!1:this.baseUrl===a.baseUrl&&this.H===
a.N&&this.Da(a):!0};Bg.prototype.Da=function(a){var b=this;if(!this.P)return this.C===a.H;var c=Object.keys(this.ra);return c.length===Object.keys(a.ra).length&&c.every(function(d){return a.ra.hasOwnProperty(d)&&String(b.ra[d])===String(a.ra[d])})};var Cg={},Dg=(Cg.uaa=!0,Cg.uab=!0,Cg.uafvl=!0,Cg.uamb=!0,Cg.uam=!0,Cg.uap=!0,Cg.uapv=!0,Cg.uaw=!0,Cg);
var Gg=function(a,b){var c=a.events;if(c.length===1)return Eg(c[0],b);var d=[];a.C&&d.push(a.C);for(var e={},f=0;f<c.length;f++)tb(c[f].Pd,function(t,u){u!=null&&(e[t]=e[t]||{},e[t][String(u)]=e[t][String(u)]+1||1)});var g={};tb(e,function(t,u){var v,w=-1,y=0;tb(u,function(z,C){y+=C;var D=(z.length+t.length+2)*(C-1);D>w&&(v=z,w=D)});y===c.length&&(g[t]=v)});Fg(g,d);b&&d.push("_s="+b);for(var h=d.join("&"),m=[],n={},p=0;p<c.length;n={tj:void 0},p++){var q=[];n.tj={};tb(c[p].Pd,function(t){return function(u,
v){g[u]!==""+v&&(t.tj[u]=v)}}(n));c[p].C&&q.push(c[p].C);Fg(n.tj,q);m.push(q.join("&"))}var r=m.join("\r\n");return{params:h,body:r}},Eg=function(a,b){var c=[];a.H&&c.push(a.H);b&&c.push("_s="+b);Fg(a.Pd,c);var d=!1;a.C&&(c.push(a.C),d=!0);var e=c.join("&"),f="",g=e.length+a.baseUrl.length+1;d&&g>2048&&(f=c.pop(),e=c.join("&"));return{params:e,body:f}},Fg=function(a,b){tb(a,function(c,d){d!=null&&b.push(encodeURIComponent(c)+"="+encodeURIComponent(d))})};var Hg=function(a){var b=[];tb(a,function(c,d){d!=null&&b.push(encodeURIComponent(c)+"="+encodeURIComponent(String(d)))});return b.join("&")},Ig=function(a,b,c,d,e,f,g,h){this.baseUrl=b;this.endpoint=c;this.destinationId=f;this.eventId=g;this.priorityId=h;this.ra=a.ra;this.Pd=a.Pd;this.Zi=a.Zi;this.N=d;this.H=Hg(a.ra);this.C=Hg(a.Zi);this.P=this.C.length;if(e&&this.P>16384)throw Error("EVENT_TOO_LARGE");};
var Lg=function(a,b){for(var c=0;c<b.length;c++){var d=a,e=b[c];if(!Jg.exec(e))throw Error("Invalid key wildcard");var f=e.indexOf(".*"),g=f!==-1&&f===e.length-2,h=g?e.slice(0,e.length-2):e,m;a:if(d.length===0)m=!1;else{for(var n=d.split("."),p=0;p<n.length;p++)if(!Kg.exec(n[p])){m=!1;break a}m=!0}if(!m||h.length>d.length||!g&&d.length!==e.length?0:g?Gb(d,h)&&(d===h||d.charAt(h.length)==="."):d===h)return!0}return!1},Kg=/^[a-z$_][\w-$]*$/i,Jg=/^(?:[a-z_$][a-z-_$0-9]*\.)*[a-z_$][a-z-_$0-9]*(?:\.\*)?$/i;
var Mg=["matches","webkitMatchesSelector","mozMatchesSelector","msMatchesSelector","oMatchesSelector"];function Ng(a,b){var c=String(a),d=String(b),e=c.length-d.length;return e>=0&&c.indexOf(d,e)===e}function Og(a,b){return String(a).split(",").indexOf(String(b))>=0}var Pg=new sb;function Qg(a,b,c){var d=c?"i":void 0;try{var e=String(b)+String(d),f=Pg.get(e);f||(f=new RegExp(b,d),Pg.set(e,f));return f.test(a)}catch(g){return!1}}function Rg(a,b){return String(a).indexOf(String(b))>=0}
function Sg(a,b){return String(a)===String(b)}function Tg(a,b){return Number(a)>=Number(b)}function Ug(a,b){return Number(a)<=Number(b)}function Vg(a,b){return Number(a)>Number(b)}function Wg(a,b){return Number(a)<Number(b)}function Xg(a,b){return Gb(String(a),String(b))};var dh=/^([a-z][a-z0-9]*):(!|\?)(\*|string|boolean|number|Fn|PixieMap|List|OpaqueValue)$/i,eh={Fn:"function",PixieMap:"Object",List:"Array"};
function fh(a,b){for(var c=["input:!*"],d=0;d<c.length;d++){var e=dh.exec(c[d]);if(!e)throw Error("Internal Error in "+a);var f=e[1],g=e[2]==="!",h=e[3],m=b[d];if(m==null){if(g)throw Error("Error in "+a+". Required argument "+f+" not supplied.");}else if(h!=="*"){var n=typeof m;m instanceof td?n="Fn":m instanceof pd?n="List":m instanceof Ya?n="PixieMap":m instanceof Ad?n="PixiePromise":m instanceof yd&&(n="OpaqueValue");if(n!==h)throw Error("Error in "+a+". Argument "+f+" has type "+((eh[n]||n)+", which does not match required type ")+
((eh[h]||h)+"."));}}}function F(a,b,c){for(var d=[],e=l(c),f=e.next();!f.done;f=e.next()){var g=f.value;g instanceof td?d.push("function"):g instanceof pd?d.push("Array"):g instanceof Ya?d.push("Object"):g instanceof Ad?d.push("Promise"):g instanceof yd?d.push("OpaqueValue"):d.push(typeof g)}return Error("Argument error in "+a+". Expected argument types ["+(b.join(",")+"], but received [")+(d.join(",")+"]."))}function gh(a){return a instanceof Ya}function hh(a){return gh(a)||a===null||ih(a)}
function jh(a){return a instanceof td}function kh(a){return jh(a)||a===null||ih(a)}function lh(a){return a instanceof pd}function mh(a){return a instanceof yd}function nh(a){return typeof a==="string"}function oh(a){return nh(a)||a===null||ih(a)}function ph(a){return typeof a==="boolean"}function qh(a){return ph(a)||ih(a)}function rh(a){return ph(a)||a===null||ih(a)}function sh(a){return typeof a==="number"}function ih(a){return a===void 0};function th(a){return""+a}
function uh(a,b){var c=[];return c};function vh(a,b){var c=new td(a,function(){for(var d=Array.prototype.slice.call(arguments,0),e=0;e<d.length;e++)d[e]=this.evaluate(d[e]);try{return b.apply(this,d)}catch(g){throw Oa(g);}});c.Wa();return c}
function wh(a,b){var c=new Ya,d;for(d in b)if(b.hasOwnProperty(d)){var e=b[d];lb(e)?c.set(d,vh(a+"_"+d,e)):ld(e)?c.set(d,wh(a+"_"+d,e)):(nb(e)||mb(e)||typeof e==="boolean")&&c.set(d,e)}c.Wa();return c};function xh(a,b){if(!nh(a))throw F(this.getName(),["string"],arguments);if(!oh(b))throw F(this.getName(),["string","undefined"],arguments);var c={},d=new Ya;return d=wh("AssertApiSubject",
c)};function yh(a,b){if(!oh(b))throw F(this.getName(),["string","undefined"],arguments);if(a instanceof Ad)throw Error("Argument actual cannot have type Promise. Assertions on asynchronous code aren't supported.");var c={},d=new Ya;return d=wh("AssertThatSubject",c)};function zh(a){return function(){for(var b=Ca.apply(0,arguments),c=[],d=this.K,e=0;e<b.length;++e)c.push(Bd(b[e],d));return Cd(a.apply(null,c))}}function Ah(){for(var a=Math,b=Bh,c={},d=0;d<b.length;d++){var e=b[d];a.hasOwnProperty(e)&&(c[e]=zh(a[e].bind(a)))}return c};function Ch(a){return a!=null&&Gb(a,"__cvt_")};function Dh(a){var b;return b};function Eh(a){var b;if(!nh(a))throw F(this.getName(),["string"],arguments);try{b=decodeURIComponent(a)}catch(c){}return b};function Fh(a){try{return encodeURI(a)}catch(b){}};function Gh(a){try{return encodeURIComponent(String(a))}catch(b){}};
var Hh=function(a,b){for(var c=0;c<b.length;c++){if(a===void 0)return;a=a[b[c]]}return a},Ih=function(a,b){var c=b.preHit;if(c){var d=a[0];switch(d){case "hitData":return a.length<2?void 0:Hh(c.getHitData(a[1]),a.slice(2));case "metadata":return a.length<2?void 0:Hh(c.getMetadata(a[1]),a.slice(2));case "eventName":return c.getEventName();case "destinationId":return c.getDestinationId();default:throw Error(d+" is not a valid field that can be accessed\n                      from PreHit data.");}}},
Kh=function(a,b){if(a){if(a.contextValue!==void 0){var c;a:{var d=a.contextValue,e=d.keyParts;if(e&&e.length!==0){var f=d.namespaceType;switch(f){case 1:c=Ih(e,b);break a;case 2:var g=b.macro;c=g?g[e[0]]:void 0;break a;default:throw Error("Unknown Namespace Type used: "+f);}}c=void 0}return c}if(a.booleanExpressionValue!==void 0)return Jh(a.booleanExpressionValue,b);if(a.booleanValue!==void 0)return!!a.booleanValue;if(a.stringValue!==void 0)return String(a.stringValue);if(a.integerValue!==void 0)return Number(a.integerValue);
if(a.doubleValue!==void 0)return Number(a.doubleValue);throw Error("Unknown field used for variable of type ExpressionValue:"+a);}},Jh=function(a,b){var c=a.args;if(!Array.isArray(c)||c.length===0)throw Error('Invalid boolean expression format. Expected "args":'+c+" property to\n         be non-empty array.");var d=function(g){return Kh(g,b)};switch(a.type){case 1:for(var e=0;e<c.length;e++)if(d(c[e]))return!0;return!1;case 2:for(var f=0;f<c.length;f++)if(!d(c[f]))return!1;return c.length>0;case 3:return!d(c[0]);
case 4:return Qg(d(c[0]),d(c[1]),!1);case 5:return Sg(d(c[0]),d(c[1]));case 6:return Xg(d(c[0]),d(c[1]));case 7:return Ng(d(c[0]),d(c[1]));case 8:return Rg(d(c[0]),d(c[1]));case 9:return Wg(d(c[0]),d(c[1]));case 10:return Ug(d(c[0]),d(c[1]));case 11:return Vg(d(c[0]),d(c[1]));case 12:return Tg(d(c[0]),d(c[1]));case 13:return Og(d(c[0]),String(d(c[1])));default:throw Error('Invalid boolean expression format. Expected "type" property tobe a positive integer which is less than 14.');}};function Lh(a){if(!oh(a))throw F(this.getName(),["string|undefined"],arguments);};function Mh(a,b){if(!sh(a)||!sh(b))throw F(this.getName(),["number","number"],arguments);return qb(a,b)};function Nh(){return(new Date).getTime()};function Oh(a){if(a===null)return"null";if(a instanceof pd)return"array";if(a instanceof td)return"function";if(a instanceof yd){var b=a.getValue();if((b==null?void 0:b.constructor)===void 0||b.constructor.name===void 0){var c=String(b);return c.substring(8,c.length-1)}return String(b.constructor.name)}return typeof a};function Ph(a){function b(c){return function(d){try{return c(d)}catch(e){(qg||rg.Jm)&&a.call(this,e.message)}}}return{parse:b(function(c){return Cd(JSON.parse(c))}),stringify:b(function(c){return JSON.stringify(Bd(c))}),publicName:"JSON"}};function Qh(a){return vb(Bd(a,this.K))};function Rh(a){return Number(Bd(a,this.K))};function Sh(a){return a===null?"null":a===void 0?"undefined":a.toString()};function Th(a,b,c){var d=null,e=!1;return e?d:null};var Bh="floor ceil round max min abs pow sqrt".split(" ");function Uh(){var a={};return{tp:function(b){return a.hasOwnProperty(b)?a[b]:void 0},Fm:function(b,c){a[b]=c},reset:function(){a={}}}}function Vh(a,b){return function(){return td.prototype.invoke.apply(a,[b].concat(ya(Ca.apply(0,arguments))))}}
function Wh(a,b){if(!nh(a))throw F(this.getName(),["string","any"],arguments);}
function Xh(a,b){if(!nh(a)||!gh(b))throw F(this.getName(),["string","PixieMap"],arguments);};var Yh={};var Zh=function(a){var b=new Ya;if(a instanceof pd)for(var c=a.Aa(),d=0;d<c.length;d++){var e=c[d];a.has(e)&&b.set(e,a.get(e))}else if(a instanceof td)for(var f=a.Aa(),g=0;g<f.length;g++){var h=f[g];b.set(h,a.get(h))}else for(var m=0;m<a.length;m++)b.set(m,a[m]);return b};
Yh.keys=function(a){fh(this.getName(),arguments);if(a instanceof pd||a instanceof td||typeof a==="string")a=Zh(a);if(a instanceof Ya||a instanceof Ad)return new pd(a.Aa());return new pd};
Yh.values=function(a){fh(this.getName(),arguments);if(a instanceof pd||a instanceof td||typeof a==="string")a=Zh(a);if(a instanceof Ya||a instanceof Ad)return new pd(a.Ac());return new pd};
Yh.entries=function(a){fh(this.getName(),arguments);if(a instanceof pd||a instanceof td||typeof a==="string")a=Zh(a);if(a instanceof Ya||a instanceof Ad)return new pd(a.ac().map(function(b){return new pd(b)}));return new pd};
Yh.freeze=function(a){(a instanceof Ya||a instanceof Ad||a instanceof pd||a instanceof td)&&a.Wa();return a};Yh.delete=function(a,b){if(a instanceof Ya&&!a.Db())return a.remove(b),!0;return!1};function H(a,b){var c=Ca.apply(2,arguments),d=a.K.wb();if(!d)throw Error("Missing program state.");if(d.rq){try{d.Rl.apply(null,[b].concat(ya(c)))}catch(e){throw fb("TAGGING",21),e;}return}d.Rl.apply(null,[b].concat(ya(c)))};var $h=function(){this.H={};this.C={};this.N=!0;};$h.prototype.get=function(a,b){var c=this.contains(a)?this.H[a]:void 0;return c};$h.prototype.contains=function(a){return this.H.hasOwnProperty(a)};
$h.prototype.add=function(a,b,c){if(this.contains(a))throw Error("Attempting to add a function which already exists: "+a+".");if(this.C.hasOwnProperty(a))throw Error("Attempting to add an API with an existing private API name: "+a+".");this.H[a]=c?void 0:lb(b)?vh(a,b):wh(a,b)};function ai(a,b){var c=void 0;return c};function bi(){var a={};return a};var J={m:{Ka:"ad_personalization",U:"ad_storage",V:"ad_user_data",ja:"analytics_storage",hc:"region",da:"consent_updated",sg:"wait_for_update",dn:"app_remove",fn:"app_store_refund",gn:"app_store_subscription_cancel",hn:"app_store_subscription_convert",jn:"app_store_subscription_renew",kn:"consent_update",bk:"add_payment_info",dk:"add_shipping_info",Sd:"add_to_cart",Td:"remove_from_cart",ek:"view_cart",Xc:"begin_checkout",Ud:"select_item",kc:"view_item_list",Gc:"select_promotion",mc:"view_promotion",
mb:"purchase",Vd:"refund",zb:"view_item",fk:"add_to_wishlist",ln:"exception",mn:"first_open",nn:"first_visit",qa:"gtag.config",Fb:"gtag.get",on:"in_app_purchase",Yc:"page_view",pn:"screen_view",qn:"session_start",rn:"source_update",sn:"timing_complete",tn:"track_social",Wd:"user_engagement",un:"user_id_update",Me:"gclid_link_decoration_source",Ne:"gclid_storage_source",nc:"gclgb",nb:"gclid",gk:"gclid_len",Xd:"gclgs",Yd:"gcllp",Zd:"gclst",ya:"ads_data_redaction",Oe:"gad_source",Pe:"gad_source_src",
Zc:"gclid_url",hk:"gclsrc",Qe:"gbraid",ae:"wbraid",Ga:"allow_ad_personalization_signals",zg:"allow_custom_scripts",Re:"allow_direct_google_requests",Ag:"allow_display_features",Bg:"allow_enhanced_conversions",Pb:"allow_google_signals",ob:"allow_interest_groups",vn:"app_id",wn:"app_installer_id",xn:"app_name",yn:"app_version",Qb:"auid",zn:"auto_detection_enabled",bd:"aw_remarketing",Qh:"aw_remarketing_only",Cg:"discount",Dg:"aw_feed_country",Eg:"aw_feed_language",sa:"items",Fg:"aw_merchant_id",ik:"aw_basket_type",
Se:"campaign_content",Te:"campaign_id",Ue:"campaign_medium",Ve:"campaign_name",We:"campaign",Xe:"campaign_source",Ye:"campaign_term",Rb:"client_id",jk:"rnd",Rh:"consent_update_type",An:"content_group",Bn:"content_type",cb:"conversion_cookie_prefix",Ze:"conversion_id",Ra:"conversion_linker",Sh:"conversion_linker_disabled",dd:"conversion_api",Gg:"cookie_deprecation",pb:"cookie_domain",qb:"cookie_expires",Ab:"cookie_flags",ed:"cookie_name",Sb:"cookie_path",eb:"cookie_prefix",Hc:"cookie_update",fd:"country",
Sa:"currency",Th:"customer_buyer_stage",af:"customer_lifetime_value",Uh:"customer_loyalty",Vh:"customer_ltv_bucket",bf:"custom_map",Wh:"gcldc",gd:"dclid",kk:"debug_mode",oa:"developer_id",Cn:"disable_merchant_reported_purchases",hd:"dc_custom_params",Dn:"dc_natural_search",lk:"dynamic_event_settings",mk:"affiliation",Hg:"checkout_option",Xh:"checkout_step",nk:"coupon",cf:"item_list_name",Yh:"list_name",En:"promotions",be:"shipping",Zh:"tax",Ig:"engagement_time_msec",Jg:"enhanced_client_id",ai:"enhanced_conversions",
pk:"enhanced_conversions_automatic_settings",df:"estimated_delivery_date",bi:"euid_logged_in_state",ef:"event_callback",Gn:"event_category",Tb:"event_developer_id_string",Hn:"event_label",jd:"event",Kg:"event_settings",Lg:"event_timeout",In:"description",Jn:"fatal",Kn:"experiments",di:"firebase_id",ce:"first_party_collection",Mg:"_x_20",qc:"_x_19",qk:"fledge_drop_reason",rk:"fledge",sk:"flight_error_code",tk:"flight_error_message",uk:"fl_activity_category",vk:"fl_activity_group",ei:"fl_advertiser_id",
wk:"fl_ar_dedupe",ff:"match_id",xk:"fl_random_number",yk:"tran",zk:"u",Ng:"gac_gclid",de:"gac_wbraid",Ak:"gac_wbraid_multiple_conversions",Bk:"ga_restrict_domain",fi:"ga_temp_client_id",Ln:"ga_temp_ecid",kd:"gdpr_applies",Ck:"geo_granularity",Ic:"value_callback",rc:"value_key",sc:"google_analysis_params",ee:"_google_ng",fe:"google_signals",Dk:"google_tld",hf:"gpp_sid",jf:"gpp_string",Og:"groups",Ek:"gsa_experiment_id",kf:"gtag_event_feature_usage",Fk:"gtm_up",Jc:"iframe_state",lf:"ignore_referrer",
gi:"internal_traffic_results",Gk:"_is_fpm",Kc:"is_legacy_converted",Lc:"is_legacy_loaded",Pg:"is_passthrough",ld:"_lps",Bb:"language",Qg:"legacy_developer_id_string",Ta:"linker",he:"accept_incoming",uc:"decorate_forms",la:"domains",Mc:"url_position",md:"merchant_feed_label",nd:"merchant_feed_language",od:"merchant_id",Hk:"method",Mn:"name",Ik:"navigation_type",nf:"new_customer",Rg:"non_interaction",Nn:"optimize_id",Jk:"page_hostname",pf:"page_path",Ya:"page_referrer",Gb:"page_title",Kk:"passengers",
Lk:"phone_conversion_callback",On:"phone_conversion_country_code",Mk:"phone_conversion_css_class",Pn:"phone_conversion_ids",Nk:"phone_conversion_number",Ok:"phone_conversion_options",Qn:"_platinum_request_status",Rn:"_protected_audience_enabled",ie:"quantity",Sg:"redact_device_info",hi:"referral_exclusion_definition",Vq:"_request_start_time",Vb:"restricted_data_processing",Sn:"retoken",Tn:"sample_rate",ii:"screen_name",Nc:"screen_resolution",Pk:"_script_source",Un:"search_term",rb:"send_page_view",
pd:"send_to",rd:"server_container_url",qf:"session_duration",Tg:"session_engaged",ji:"session_engaged_time",Wb:"session_id",Ug:"session_number",rf:"_shared_user_id",je:"delivery_postal_code",Wq:"_tag_firing_delay",Xq:"_tag_firing_time",Yq:"temporary_client_id",ki:"_timezone",li:"topmost_url",Vn:"tracking_id",mi:"traffic_type",La:"transaction_id",vc:"transport_url",Qk:"trip_type",ud:"update",Hb:"url_passthrough",Rk:"uptgs",tf:"_user_agent_architecture",uf:"_user_agent_bitness",vf:"_user_agent_full_version_list",
wf:"_user_agent_mobile",xf:"_user_agent_model",yf:"_user_agent_platform",zf:"_user_agent_platform_version",Af:"_user_agent_wow64",fb:"user_data",ni:"user_data_auto_latency",oi:"user_data_auto_meta",ri:"user_data_auto_multi",si:"user_data_auto_selectors",ui:"user_data_auto_status",wc:"user_data_mode",Vg:"user_data_settings",Ma:"user_id",Xb:"user_properties",Sk:"_user_region",Bf:"us_privacy_string",za:"value",Tk:"wbraid_multiple_conversions",wd:"_fpm_parameters",zi:"_host_name",fl:"_in_page_command",
il:"_ip_override",nl:"_is_passthrough_cid",xc:"non_personalized_ads",Ki:"_sst_parameters",oc:"conversion_label",Ca:"page_location",Ub:"global_developer_id_string",sd:"tc_privacy_string"}};var ci={},di=(ci[J.m.da]="gcu",ci[J.m.nc]="gclgb",ci[J.m.nb]="gclaw",ci[J.m.gk]="gclid_len",ci[J.m.Xd]="gclgs",ci[J.m.Yd]="gcllp",ci[J.m.Zd]="gclst",ci[J.m.Qb]="auid",ci[J.m.Cg]="dscnt",ci[J.m.Dg]="fcntr",ci[J.m.Eg]="flng",ci[J.m.Fg]="mid",ci[J.m.ik]="bttype",ci[J.m.Rb]="gacid",ci[J.m.oc]="label",ci[J.m.dd]="capi",ci[J.m.Gg]="pscdl",ci[J.m.Sa]="currency_code",ci[J.m.Th]="clobs",ci[J.m.af]="vdltv",ci[J.m.Uh]="clolo",ci[J.m.Vh]="clolb",ci[J.m.kk]="_dbg",ci[J.m.df]="oedeld",ci[J.m.Tb]="edid",ci[J.m.qk]=
"fdr",ci[J.m.rk]="fledge",ci[J.m.Ng]="gac",ci[J.m.de]="gacgb",ci[J.m.Ak]="gacmcov",ci[J.m.kd]="gdpr",ci[J.m.Ub]="gdid",ci[J.m.ee]="_ng",ci[J.m.hf]="gpp_sid",ci[J.m.jf]="gpp",ci[J.m.Ek]="gsaexp",ci[J.m.kf]="_tu",ci[J.m.Jc]="frm",ci[J.m.Pg]="gtm_up",ci[J.m.ld]="lps",ci[J.m.Qg]="did",ci[J.m.md]="fcntr",ci[J.m.nd]="flng",ci[J.m.od]="mid",ci[J.m.nf]=void 0,ci[J.m.Gb]="tiba",ci[J.m.Vb]="rdp",ci[J.m.Wb]="ecsid",ci[J.m.rf]="ga_uid",ci[J.m.je]="delopc",ci[J.m.sd]="gdpr_consent",ci[J.m.La]="oid",ci[J.m.Rk]=
"uptgs",ci[J.m.tf]="uaa",ci[J.m.uf]="uab",ci[J.m.vf]="uafvl",ci[J.m.wf]="uamb",ci[J.m.xf]="uam",ci[J.m.yf]="uap",ci[J.m.zf]="uapv",ci[J.m.Af]="uaw",ci[J.m.ni]="ec_lat",ci[J.m.oi]="ec_meta",ci[J.m.ri]="ec_m",ci[J.m.si]="ec_sel",ci[J.m.ui]="ec_s",ci[J.m.wc]="ec_mode",ci[J.m.Ma]="userId",ci[J.m.Bf]="us_privacy",ci[J.m.za]="value",ci[J.m.Tk]="mcov",ci[J.m.zi]="hn",ci[J.m.fl]="gtm_ee",ci[J.m.xc]="npa",ci[J.m.Ze]=null,ci[J.m.Nc]=null,ci[J.m.Bb]=null,ci[J.m.sa]=null,ci[J.m.Ca]=null,ci[J.m.Ya]=null,ci[J.m.li]=
null,ci[J.m.wd]=null,ci[J.m.Me]=null,ci[J.m.Ne]=null,ci[J.m.sc]=null,ci);function ei(a,b){if(a){var c=a.split("x");c.length===2&&(fi(b,"u_w",c[0]),fi(b,"u_h",c[1]))}}
function gi(a){var b=hi;b=b===void 0?ii:b;var c;var d=b;if(a&&a.length){for(var e=[],f=0;f<a.length;++f){var g=a[f];g&&e.push({item_id:d(g),quantity:g.quantity,value:g.price,start_date:g.start_date,end_date:g.end_date})}c=e}else c=[];var h;var m=c;if(m){for(var n=[],p=0;p<m.length;p++){var q=m[p],r=[];q&&(r.push(ji(q.value)),r.push(ji(q.quantity)),r.push(ji(q.item_id)),r.push(ji(q.start_date)),r.push(ji(q.end_date)),n.push("("+r.join("*")+")"))}h=n.length>0?n.join(""):""}else h="";return h}
function ii(a){return ki(a.item_id,a.id,a.item_name)}function ki(){for(var a=l(Ca.apply(0,arguments)),b=a.next();!b.done;b=a.next()){var c=b.value;if(c!==null&&c!==void 0)return c}}function li(a){if(a&&a.length){for(var b=[],c=0;c<a.length;++c){var d=a[c];d&&d.estimated_delivery_date?b.push(""+d.estimated_delivery_date):b.push("")}return b.join(",")}}function fi(a,b,c){c===void 0||c===null||c===""&&!Dg[b]||(a[b]=c)}function ji(a){return typeof a!=="number"&&typeof a!=="string"?"":a.toString()};var mi={},ni=function(){for(var a=!1,b=!1,c=0;a===b;)if(a=qb(0,1)===0,b=qb(0,1)===0,c++,c>30)return;return a},pi={xq:oi};function qi(a,b){var c=mi[b],d=c.studyId,e=c.experimentId,f=c.probability;if(!(a.studies||{})[d]){var g=a.studies||{};g[d]=!0;a.studies=g;mi[b].active||(mi[b].probability>.5?ri(a,e):f<=0||f>1||pi.xq(a,b))}}
function oi(a,b){var c=mi[b],d=c.controlId2;if(!(qb(0,9999)<c.probability*(c.controlId2&&c.probability<=.25?4:2)*1E4))return a;si(a,{experimentId:c.experimentId,controlId:c.controlId,controlId2:c.controlId2&&c.probability<=.25?d:void 0,experimentCallback:function(){}});return a}function ri(a,b){var c=a.exp||{};c[b]=!0;a.exp=c}
function si(a,b){var c=b.experimentId,d=b.controlId,e=b.controlId2,f=b.experimentCallback;if((a.exp||{})[c])f();else if(!((a.exp||{})[d]||e&&(a.exp||{})[e])){var g=ni()?0:1;e&&(g|=(ni()?0:1)<<1);g===0?(ri(a,c),f()):g===1?ri(a,d):g===2&&ri(a,e)}};var K={J:{Mj:"call_conversion",W:"conversion",Wn:"floodlight",Df:"ga_conversion",Gi:"landing_page",Ha:"page_view",na:"remarketing",kb:"user_data_lead",Na:"user_data_web"}};function vi(a,b){if(!wi)return null;if(Element.prototype.closest)try{return a.closest(b)}catch(e){return null}var c=Element.prototype.matches||Element.prototype.webkitMatchesSelector||Element.prototype.mozMatchesSelector||Element.prototype.msMatchesSelector||Element.prototype.oMatchesSelector,d=a;if(!A.documentElement.contains(d))return null;do{try{if(c.call(d,b))return d}catch(e){break}d=d.parentElement||d.parentNode}while(d!==null&&d.nodeType===1);return null}var xi=!1;
if(A.querySelectorAll)try{var yi=A.querySelectorAll(":root");yi&&yi.length==1&&yi[0]==A.documentElement&&(xi=!0)}catch(a){}var wi=xi;var zi="email sha256_email_address phone_number sha256_phone_number first_name last_name".split(" "),Ai="first_name sha256_first_name last_name sha256_last_name street sha256_street city region country postal_code".split(" ");function Bi(a,b){if(!b._tag_metadata){for(var c={},d=0,e=0;e<a.length;e++)d+=Ci(a[e],b,c)?1:0;d>0&&(b._tag_metadata=c)}}function Ci(a,b,c){var d=b[a];if(d===void 0)return!1;c[a]=Array.isArray(d)?d.map(function(){return{mode:"c"}}):{mode:"c"};return!0}
function Di(a){if(E(178)&&a){Bi(zi,a);for(var b=ob(a.address),c=0;c<b.length;c++){var d=b[c];d&&Bi(Ai,d)}var e=a.home_address;e&&Bi(Ai,e)}}
function Ei(a,b,c){function d(f,g){g=String(g).substring(0,100);e.push(""+f+encodeURIComponent(g))}if(!c)return"";var e=[];d("i",String(a));d("f",b);c.mode&&d("m",c.mode);c.isPreHashed&&d("p","1");c.rawLength&&d("r",String(c.rawLength));c.normalizedLength&&d("n",String(c.normalizedLength));c.location&&d("l",c.location);c.selector&&d("s",c.selector);return e.join(".")};function Fi(a){switch(a){case 0:break;case 9:return"e4";case 6:return"e5";case 14:return"e6";default:return"e7"}};function Gi(){this.blockSize=-1};function Hi(a,b){this.blockSize=-1;this.blockSize=64;this.N=Da.Uint8Array?new Uint8Array(this.blockSize):Array(this.blockSize);this.P=this.H=0;this.C=[];this.fa=a;this.R=b;this.ma=Da.Int32Array?new Int32Array(64):Array(64);Ii===void 0&&(Da.Int32Array?Ii=new Int32Array(Ji):Ii=Ji);this.reset()}Ea(Hi,Gi);for(var Ki=[],Li=0;Li<63;Li++)Ki[Li]=0;var Mi=[].concat(128,Ki);
Hi.prototype.reset=function(){this.P=this.H=0;var a;if(Da.Int32Array)a=new Int32Array(this.R);else{var b=this.R,c=b.length;if(c>0){for(var d=Array(c),e=0;e<c;e++)d[e]=b[e];a=d}else a=[]}this.C=a};
var Ni=function(a){for(var b=a.N,c=a.ma,d=0,e=0;e<b.length;)c[d++]=b[e]<<24|b[e+1]<<16|b[e+2]<<8|b[e+3],e=d*4;for(var f=16;f<64;f++){var g=c[f-15]|0,h=c[f-2]|0;c[f]=((c[f-16]|0)+((g>>>7|g<<25)^(g>>>18|g<<14)^g>>>3)|0)+((c[f-7]|0)+((h>>>17|h<<15)^(h>>>19|h<<13)^h>>>10)|0)|0}for(var m=a.C[0]|0,n=a.C[1]|0,p=a.C[2]|0,q=a.C[3]|0,r=a.C[4]|0,t=a.C[5]|0,u=a.C[6]|0,v=a.C[7]|0,w=0;w<64;w++){var y=((m>>>2|m<<30)^(m>>>13|m<<19)^(m>>>22|m<<10))+(m&n^m&p^n&p)|0,z=(v+((r>>>6|r<<26)^(r>>>11|r<<21)^(r>>>25|r<<7))|
0)+(((r&t^~r&u)+(Ii[w]|0)|0)+(c[w]|0)|0)|0;v=u;u=t;t=r;r=q+z|0;q=p;p=n;n=m;m=z+y|0}a.C[0]=a.C[0]+m|0;a.C[1]=a.C[1]+n|0;a.C[2]=a.C[2]+p|0;a.C[3]=a.C[3]+q|0;a.C[4]=a.C[4]+r|0;a.C[5]=a.C[5]+t|0;a.C[6]=a.C[6]+u|0;a.C[7]=a.C[7]+v|0};
Hi.prototype.update=function(a,b){b===void 0&&(b=a.length);var c=0,d=this.H;if(typeof a==="string")for(;c<b;)this.N[d++]=a.charCodeAt(c++),d==this.blockSize&&(Ni(this),d=0);else{var e,f=typeof a;e=f!="object"?f:a?Array.isArray(a)?"array":f:"null";if(e=="array"||e=="object"&&typeof a.length=="number")for(;c<b;){var g=a[c++];if(!("number"==typeof g&&0<=g&&255>=g&&g==(g|0)))throw Error("message must be a byte array");this.N[d++]=g;d==this.blockSize&&(Ni(this),d=0)}else throw Error("message must be string or array");
}this.H=d;this.P+=b};Hi.prototype.digest=function(){var a=[],b=this.P*8;this.H<56?this.update(Mi,56-this.H):this.update(Mi,this.blockSize-(this.H-56));for(var c=63;c>=56;c--)this.N[c]=b&255,b/=256;Ni(this);for(var d=0,e=0;e<this.fa;e++)for(var f=24;f>=0;f-=8)a[d++]=this.C[e]>>f&255;return a};
var Ji=[1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,
4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298],Ii;function Oi(){Hi.call(this,8,Pi)}Ea(Oi,Hi);var Pi=[1779033703,3144134277,1013904242,2773480762,1359893119,2600822924,528734635,1541459225];var Qi=/^[0-9A-Fa-f]{64}$/;function Ri(a){try{return(new TextEncoder).encode(a)}catch(e){for(var b=[],c=0;c<a.length;c++){var d=a.charCodeAt(c);d<128?b.push(d):d<2048?b.push(192|d>>6,128|d&63):d<55296||d>=57344?b.push(224|d>>12,128|d>>6&63,128|d&63):(d=65536+((d&1023)<<10|a.charCodeAt(++c)&1023),b.push(240|d>>18,128|d>>12&63,128|d>>6&63,128|d&63))}return new Uint8Array(b)}}
function Si(a){var b=x;if(a===""||a==="e0")return Promise.resolve(a);var c;if((c=b.crypto)==null?0:c.subtle){if(Qi.test(a))return Promise.resolve(a);try{var d=Ri(a);return b.crypto.subtle.digest("SHA-256",d).then(function(e){return Ti(e,b)}).catch(function(){return"e2"})}catch(e){return Promise.resolve("e2")}}else return Promise.resolve("e1")}
function Ti(a,b){var c=Array.from(new Uint8Array(a)).map(function(d){return String.fromCharCode(d)}).join("");return b.btoa(c).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")};var Ui=[],Vi=[],Wi;function Xi(a){Wi?Wi(a):Ui.push(a)}function Yi(a,b){if(!E(190))return b;var c,d=!1;d=d===void 0?!1:d;var e,f;c=((e=data)==null?0:(f=e.blob)==null?0:f.hasOwnProperty(a))?!!data.blob[a]:d;return c!==b?(Xi(a),b):c}function Zi(a,b){if(!E(190))return b;var c=$i(a,"");return c!==b?(Xi(a),b):c}function $i(a,b){b=b===void 0?"":b;var c,d;return((c=data)==null?0:(d=c.blob)==null?0:d.hasOwnProperty(a))?String(data.blob[a]):b}
function aj(a,b){if(!E(190))return b;var c,d,e;c=((d=data)==null?0:(e=d.blob)==null?0:e.hasOwnProperty(a))?Number(data.blob[a]):0;return c===b||isNaN(c)&&isNaN(b)?c:(Xi(a),b)}function bj(){var a=cj,b=dj;Wi=a;for(var c=l(Ui),d=c.next();!d.done;d=c.next())a(d.value);Ui.length=0;if(E(225)){for(var e=l(Vi),f=e.next();!f.done;f=e.next())b(f.value);Vi.length=0}};var ej={Pm:'5000',Qm:'5000',Zm:'',bn:'1000',ao:'US-CO~US-CT~US-MT~US-NE~US-NH~US-TX~US-MN~US-NJ~US-MD',bo:'US-CO~US-CT~US-MT~US-NE~US-NH~US-TX~US-MN~US-NJ~US-MD',xo:Zi(44,'101509157~103116026~103200004~103233427~104684208~104684211')},fj={Fo:Number(ej.Pm)||-1,Go:Number(ej.Qm)||-1,bp:Number(ej.Zm)||0,ep:Number(ej.bn)||0,xp:ej.ao.split("~"),
yp:ej.bo.split("~"),Oq:ej.xo};ma(Object,"assign").call(Object,{},fj);function L(a){fb("GTM",a)};
var kj=function(a,b){var c=E(178),d=["tv.1"],e=["tvd.1"],f=gj(a);if(f)return d.push(f),{Za:!1,Fj:d.join("~"),og:{},Ch:c?e.join("~"):void 0};var g={},h=0;var m=0,n=hj(a,function(u,v,w){m++;var y=u.value,z;if(w){var C=v+"__"+h++;z="${userData."+C+"|sha256}";g[C]=y}else z=encodeURIComponent(encodeURIComponent(y));u.index!==void 0&&(v+=u.index);d.push(v+"."+z);if(c){var D=Ei(m,v,u.metadata);D&&e.push(D)}}).Za,p=e.join("~");
var q=d.join("~"),r={userData:g},t=b===3;return b===2||t?{Za:n,Fj:q,og:r,cp:t?"tv.9~${"+(q+"|encryptRsa}"):"tv.1~${"+(q+"|encrypt}"),encryptionKeyString:t?ij():jj(),Ch:c?p:void 0}:{Za:n,Fj:q,og:r,Ch:c?p:void 0}},mj=function(a){if(!(a!=null&&Object.keys(a).length>0))return!1;var b=lj(a);return hj(b,function(){}).Za},hj=function(a,b){b=b===void 0?function(){}:b;for(var c=!1,d=!1,e=l(a),f=e.next();!f.done;f=e.next()){var g=f.value;if(g.value){var h=nj[g.name];if(h){var m=oj(g);m&&(c=!0);d=!0;b(g,h,m)}}}return{Za:d,
gj:c}},oj=function(a){var b=pj(a.name),c=/^e\d+$/.test(a.value),d;if(d=b&&!c){var e=a.value;d=!(qj.test(e)||Qi.test(e))}return d},pj=function(a){return rj.indexOf(a)!==-1},jj=function(){return'{\x22keys\x22:[{\x22hpkePublicKey\x22:{\x22params\x22:{\x22aead\x22:\x22AES_128_GCM\x22,\x22kdf\x22:\x22HKDF_SHA256\x22,\x22kem\x22:\x22DHKEM_P256_HKDF_SHA256\x22},\x22publicKey\x22:\x22BLmFt6UXBhRmCroatpW1SXiUGX8nlIzsjWuo/35QAO+zaS+otiG5QcR9nM1Cps71ya2tmVIsN5veaAal7MHFLEs\x3d\x22,\x22version\x22:0},\x22id\x22:\x22aec78412-6373-47d5-ac96-9fcee93fb999\x22},{\x22hpkePublicKey\x22:{\x22params\x22:{\x22aead\x22:\x22AES_128_GCM\x22,\x22kdf\x22:\x22HKDF_SHA256\x22,\x22kem\x22:\x22DHKEM_P256_HKDF_SHA256\x22},\x22publicKey\x22:\x22BC/FqS2VfJxJt+KUoa5szFzBglEsbyx+I9x123cX99SEO7P1N7hO6AIp93nTAdi/z2DFSAto+EqKKdcuaTb9W0s\x3d\x22,\x22version\x22:0},\x22id\x22:\x22a8322124-3ea2-4d88-b25b-86e2f0112cae\x22},{\x22hpkePublicKey\x22:{\x22params\x22:{\x22aead\x22:\x22AES_128_GCM\x22,\x22kdf\x22:\x22HKDF_SHA256\x22,\x22kem\x22:\x22DHKEM_P256_HKDF_SHA256\x22},\x22publicKey\x22:\x22BKfFh+mfP+VYN5VmB9shcyG0A1lRYz8Xzw3WGLlsKlBmFEaKsavgS+aJLQV57OOtxcD75yF5XPI4JCpAEVT6aZE\x3d\x22,\x22version\x22:0},\x22id\x22:\x2269d58b45-d2bb-4a7f-9952-57e6e8373ee3\x22},{\x22hpkePublicKey\x22:{\x22params\x22:{\x22aead\x22:\x22AES_128_GCM\x22,\x22kdf\x22:\x22HKDF_SHA256\x22,\x22kem\x22:\x22DHKEM_P256_HKDF_SHA256\x22},\x22publicKey\x22:\x22BGKg2rrDYEGZYBnoJcCvLOBw40XwX02uo+UmyosodkDpDhfJRS/gnmzpZxgdB0K64JD4BNvJP8lOXmDgfjDJnr0\x3d\x22,\x22version\x22:0},\x22id\x22:\x221cfcadd3-649d-4616-a730-b7cbb203d3b2\x22},{\x22hpkePublicKey\x22:{\x22params\x22:{\x22aead\x22:\x22AES_128_GCM\x22,\x22kdf\x22:\x22HKDF_SHA256\x22,\x22kem\x22:\x22DHKEM_P256_HKDF_SHA256\x22},\x22publicKey\x22:\x22BIj0YjU0Id8OOxdy8oAkpsYU3WUMzeTX3IB3zolk/AGHi8e4L1Wndgs+eEljcMtqAzqNrV2PUboMi62U86LWEtA\x3d\x22,\x22version\x22:0},\x22id\x22:\x2212ffea68-4f40-48ea-9714-010853b2215c\x22}]}'},uj=function(a){if(x.Promise){var b=void 0;return b}},zj=function(a,b,c,d,e){if(x.Promise)try{var f=lj(a),g=vj(f,e).then(wj);return g}catch(p){}},Bj=function(a){try{return wj(Aj(lj(a)))}catch(b){}},tj=function(a,b){var c=void 0;return c},wj=function(a){var b=E(178),c=a.Tc,d=a.time,e=["tv.1"],f=["tvd.1"],g=gj(c);if(g)return e.push(g),{Lb:e.join("~"),gj:!1,Za:!1,time:d,fj:!0,Ch:b?f.join("~"):void 0};var h=c.filter(function(r){return!oj(r)}),
m=0,n=hj(h,function(r,t){m++;var u=r.value,v=r.index;v!==void 0&&(t+=v);e.push(t+"."+u);if(b){var w=Ei(m,t,r.metadata);w&&f.push(w)}}),p=n.gj,q=n.Za;return{Lb:encodeURIComponent(e.join("~")),gj:p,Za:q,time:d,fj:!1,Ch:b?f.join("~"):void 0}},gj=function(a){if(a.length===1&&a[0].name==="error_code")return nj.error_code+"."+a[0].value},yj=function(a){if(a.length===1&&a[0].name==="error_code")return!1;for(var b=l(a),c=b.next();!c.done;c=b.next()){var d=c.value;if(nj[d.name]&&d.value)return!0}return!1},
lj=function(a){function b(t,u,v,w,y){var z=Cj(t);if(z!=="")if(Qi.test(z)){y&&(y.isPreHashed=!0);var C={name:u,value:z,index:w};y&&(C.metadata=y);m.push(C)}else{var D=v(z),G={name:u,value:D,index:w};y&&(G.metadata=y,D&&(y.rawLength=String(z).length,y.normalizedLength=D.length));m.push(G)}}function c(t,u){var v=t;if(mb(v)||Array.isArray(v)){v=ob(t);for(var w=0;w<v.length;++w){var y=Cj(v[w]),z=Qi.test(y);u&&!z&&L(89);!u&&z&&L(88)}}}function d(t,u){var v=t[u];c(v,!1);var w=Dj[u];t[w]&&(t[u]&&L(90),v=
t[w],c(v,!0));return v}function e(t,u,v,w){var y=t._tag_metadata||{},z=t[u],C=y[u];c(z,!1);var D=Dj[u];if(D){var G=t[D],I=y[D];G&&(z&&L(90),z=G,C=I,c(z,!0))}if(w!==void 0)b(z,u,v,w,C);else{z=ob(z);C=ob(C);for(var M=0;M<z.length;++M)b(z[M],u,v,void 0,C[M])}}function f(t,u,v){if(E(178))e(t,u,v,void 0);else for(var w=ob(d(t,u)),y=0;y<w.length;++y)b(w[y],u,v)}function g(t,u,v,w){if(E(178))e(t,u,v,w);else{var y=d(t,u);b(y,u,v,w)}}function h(t){return function(u){L(64);return t(u)}}var m=[];if(x.location.protocol!==
"https:")return m.push({name:"error_code",value:"e3",index:void 0}),m;f(a,"email",Ej);f(a,"phone_number",Fj);f(a,"first_name",h(Gj));f(a,"last_name",h(Gj));var n=a.home_address||{};f(n,"street",h(Hj));f(n,"city",h(Hj));f(n,"postal_code",h(Ij));f(n,"region",h(Hj));f(n,"country",h(Ij));for(var p=ob(a.address||{}),q=0;q<p.length;q++){var r=p[q];g(r,"first_name",Gj,q);g(r,"last_name",Gj,q);g(r,"street",Hj,q);g(r,"city",Hj,q);g(r,"postal_code",Ij,q);g(r,"region",Hj,q);g(r,"country",Ij,q)}return m},Jj=
function(a){var b=a?lj(a):[];return wj({Tc:b})},Kj=function(a){return a&&a!=null&&Object.keys(a).length>0&&x.Promise?lj(a).some(function(b){return b.value&&pj(b.name)&&!Qi.test(b.value)}):!1},Cj=function(a){return a==null?"":mb(a)?yb(String(a)):"e0"},Ij=function(a){return a.replace(Lj,"")},Gj=function(a){return Hj(a.replace(/\s/g,""))},Hj=function(a){return yb(a.replace(Mj,"").toLowerCase())},Fj=function(a){a=a.replace(/[\s-()/.]/g,"");a.charAt(0)!=="+"&&(a="+"+a);return Nj.test(a)?a:"e0"},Ej=function(a){var b=
a.toLowerCase().split("@");if(b.length===2){var c=b[0];/^(gmail|googlemail)\./.test(b[1])&&(c=c.replace(/\./g,""));c=c+"@"+b[1];if(Oj.test(c))return c}return"e0"},Aj=function(a){var b=Zc();try{a.forEach(function(e){if(e.value&&pj(e.name)){var f;var g=e.value,h=x;if(g===""||g==="e0"||Qi.test(g))f=g;else try{var m=new Oi;m.update(Ri(g));f=Ti(m.digest(),h)}catch(n){f="e2"}e.value=f}});var c={Tc:a};if(b!==void 0){var d=Zc();b&&d&&(c.time=Math.round(d)-Math.round(b))}return c}catch(e){return{Tc:[]}}},
vj=function(a,b){if(!a.some(function(d){return d.value&&pj(d.name)}))return Promise.resolve({Tc:a});if(!x.Promise)return Promise.resolve({Tc:[]});var c=b?Zc():void 0;return Promise.all(a.map(function(d){return d.value&&pj(d.name)?Si(d.value).then(function(e){d.value=e}):Promise.resolve()})).then(function(){var d={Tc:a};if(c!==void 0){var e=Zc();c&&e!==void 0&&(d.time=Math.round(e)-Math.round(c))}return d}).catch(function(){return{Tc:[]}})},Mj=/[0-9`~!@#$%^&*()_\-+=:;<>,.?|/\\[\]]/g,Oj=/^\S+@\S+\.\S+$/,
Nj=/^\+\d{10,15}$/,Lj=/[.~]/g,qj=/^[0-9A-Za-z_-]{43}$/,Pj={},nj=(Pj.email="em",Pj.phone_number="pn",Pj.first_name="fn",Pj.last_name="ln",Pj.street="sa",Pj.city="ct",Pj.region="rg",Pj.country="co",Pj.postal_code="pc",Pj.error_code="ec",Pj),Qj={},Dj=(Qj.email="sha256_email_address",Qj.phone_number="sha256_phone_number",Qj.first_name="sha256_first_name",Qj.last_name="sha256_last_name",Qj.street="sha256_street",Qj);var rj=Object.freeze(["email","phone_number","first_name","last_name","street"]);var Rj={},Sj=(Rj[J.m.ob]=1,Rj[J.m.rd]=2,Rj[J.m.vc]=2,Rj[J.m.ya]=3,Rj[J.m.af]=4,Rj[J.m.zg]=5,Rj[J.m.Hc]=6,Rj[J.m.eb]=6,Rj[J.m.pb]=6,Rj[J.m.ed]=6,Rj[J.m.Sb]=6,Rj[J.m.Ab]=6,Rj[J.m.qb]=7,Rj[J.m.Vb]=9,Rj[J.m.Ag]=10,Rj[J.m.Pb]=11,Rj),Tj={},Uj=(Tj.unknown=13,Tj.standard=14,Tj.unique=15,Tj.per_session=16,Tj.transactions=17,Tj.items_sold=18,Tj);var hb=[];function Vj(a,b){b=b===void 0?!1:b;for(var c=Object.keys(a),d=l(Object.keys(Sj)),e=d.next();!e.done;e=d.next()){var f=e.value;if(c.includes(f)){var g=Sj[f],h=b;h=h===void 0?!1:h;fb("GTAG_EVENT_FEATURE_CHANNEL",g);h&&(hb[g]=!0)}}};var Wj=function(){this.C=new Set;this.H=new Set},Yj=function(a){var b=Xj.R;a=a===void 0?[]:a;var c=[].concat(ya(b.C)).concat([].concat(ya(b.H))).concat(a);c.sort(function(d,e){return d-e});return c},Zj=function(){var a=[].concat(ya(Xj.R.C));a.sort(function(b,c){return b-c});return a},ak=function(){var a=Xj.R,b=fj.Oq;a.C=new Set;if(b!=="")for(var c=l(b.split("~")),d=c.next();!d.done;d=c.next()){var e=Number(d.value);isNaN(e)||a.C.add(e)}};var bk={},ck=Zi(14,"57n0"),dk=aj(15,Number("0")),ek=Zi(19,"dataLayer");Zi(20,"");Zi(16,"ChAI8JyXxAYQ8p7kwdr81OdHEiUAhzAODJpg3mqHcCAZSTFDG/LIy1nV7Ob7oIaBh3dfIoTboYgFGgIVxg\x3d\x3d");var fk={__cl:1,__ecl:1,__ehl:1,__evl:1,__fal:1,__fil:1,__fsl:1,__hl:1,__jel:1,__lcl:1,__sdl:1,__tl:1,__ytl:1},gk={__paused:1,__tg:1},hk;for(hk in fk)fk.hasOwnProperty(hk)&&(gk[hk]=1);var ik=Yi(11,wb("true")),jk=!1;
function kk(){var a=!1;a=!0;return a}var lk=E(218)?Yi(45,kk()):kk(),mk,nk=!1;mk=nk;bk.xg=Zi(3,"www.googletagmanager.com");var ok=""+bk.xg+(lk?"/gtag/js":"/gtm.js"),pk=null,qk=null,rk={},sk={};bk.Tm=Yi(2,wb("true"));var tk="";
bk.Li=tk;var Xj=new function(){this.R=new Wj;this.C=this.N=!1;this.H=0;this.Da=this.Va=this.sb=this.P="";this.fa=this.ma=!1};function uk(){var a;a=a===void 0?[]:a;return Yj(a).join("~")}function vk(){var a=Xj.P.length;return Xj.P[a-1]==="/"?Xj.P.substring(0,a-1):Xj.P}function wk(){return Xj.C?E(84)?Xj.H===0:Xj.H!==1:!1}function xk(a){for(var b={},c=l(a.split("|")),d=c.next();!d.done;d=c.next())b[d.value]=!0;return b};var yk=new sb,zk={},Ak={},Dk={name:ek,set:function(a,b){md(Ib(a,b),zk);Bk()},get:function(a){return Ck(a,2)},reset:function(){yk=new sb;zk={};Bk()}};function Ck(a,b){return b!=2?yk.get(a):Ek(a)}function Ek(a,b){var c=a.split(".");b=b||[];for(var d=zk,e=0;e<c.length;e++){if(d===null)return!1;if(d===void 0)break;d=d[c[e]];if(b.indexOf(d)!==-1)return}return d}function Fk(a,b){Ak.hasOwnProperty(a)||(yk.set(a,b),md(Ib(a,b),zk),Bk())}
function Gk(){for(var a=["gtm.allowlist","gtm.blocklist","gtm.whitelist","gtm.blacklist","tagTypeBlacklist"],b=0;b<a.length;b++){var c=a[b],d=Ck(c,1);if(Array.isArray(d)||ld(d))d=md(d,null);Ak[c]=d}}function Bk(a){tb(Ak,function(b,c){yk.set(b,c);md(Ib(b),zk);md(Ib(b,c),zk);a&&delete Ak[b]})}function Hk(a,b){var c,d=(b===void 0?2:b)!==1?Ek(a):yk.get(a);jd(d)==="array"||jd(d)==="object"?c=md(d,null):c=d;return c};
var Jk=function(a){for(var b=[],c=Object.keys(Ik),d=0;d<c.length;d++){var e=c[d],f=Ik[e],g=void 0,h=(g=a[e])!=null?g:"0";b.push(f+"-"+h)}return b.join("~")},Kk=function(a,b){return a||b?a&&!b?"1":!a&&b?"2":"3":"0"},Lk=function(a,b,c){if(a!==void 0)return Array.isArray(a)?a.map(function(){return{mode:"m",location:b,selector:c}}):{mode:"m",location:b,selector:c}},Mk=function(a,b,c,d,e){if(!c)return!1;for(var f=String(c.value),g,h=void 0,m=f.replace(/\["?'?/g,".").replace(/"?'?\]/g,"").split(",").map(function(D){return D.trim()}).filter(function(D){return D&&
!Gb(D,"#")&&!Gb(D,".")}),n=0;n<m.length;n++){var p=m[n];if(Gb(p,"dataLayer."))g=Ck(p.substring(10)),h=Lk(g,"d",p);else{var q=p.split(".");g=x[q.shift()];for(var r=0;r<q.length;r++)g=g&&g[q[r]];h=Lk(g,"j",p)}if(g!==void 0)break}if(g===void 0&&wi)try{var t=wi?A.querySelectorAll(f):null;if(t&&t.length>0){g=[];for(var u=0;u<t.length&&u<(b==="email"||b==="phone_number"?5:1);u++)g.push(Pc(t[u])||yb(t[u].value));g=g.length===1?g[0]:g;h=Lk(g,"c",f)}}catch(D){L(149)}if(E(60)){for(var v,w,y=0;y<m.length;y++){var z=
m[y];v=Ck(z);if(v!==void 0){w=Lk(v,"d",z);break}}var C=g!==void 0;e[b]=Kk(v!==void 0,C);C||(g=v,h=w)}return g?(a[b]=g,d&&h&&(d[b]=h),!0):!1},Nk=function(a,b,c){b=b===void 0?{}:b;c=c===void 0?!1:c;if(a){var d={},e=!1,f={};e=Mk(d,"email",a.email,f,b)||e;e=Mk(d,"phone_number",a.phone,f,b)||e;d.address=[];for(var g=a.name_and_address||[],h=0;h<g.length;h++){var m={},n={};e=Mk(m,"first_name",g[h].first_name,n,b)||e;e=Mk(m,"last_name",g[h].last_name,n,b)||e;e=Mk(m,"street",g[h].street,n,b)||e;e=Mk(m,"city",
g[h].city,n,b)||e;e=Mk(m,"region",g[h].region,n,b)||e;e=Mk(m,"country",g[h].country,n,b)||e;e=Mk(m,"postal_code",g[h].postal_code,n,b)||e;d.address.push(m);c&&(m._tag_metadata=n)}c&&(d._tag_metadata=f);return e?d:void 0}},Ok=function(a,b){switch(a.enhanced_conversions_mode){case "manual":if(b&&ld(b))return b;var c=a.enhanced_conversions_manual_var;if(c!==void 0)return c;var d=x.enhanced_conversion_data;d&&fb("GTAG_EVENT_FEATURE_CHANNEL",8);return d;case "automatic":return Nk(a[J.m.pk])}},Pk=function(a){return ld(a)?
!!a.enable_code:!1},Ik={email:"1",phone_number:"2",first_name:"3",last_name:"4",country:"5",postal_code:"6",street:"7",city:"8",region:"9"};var Sk=/:[0-9]+$/,Tk=/^\d+\.fls\.doubleclick\.net$/;function Uk(a,b,c,d){var e=Vk(a,!!d,b),f,g;return c?(g=e[b])!=null?g:[]:(f=e[b])==null?void 0:f[0]}function Vk(a,b,c){for(var d={},e=l(a.split("&")),f=e.next();!f.done;f=e.next()){var g=l(f.value.split("=")),h=g.next().value,m=xa(g),n=decodeURIComponent(h.replace(/\+/g," "));if(c===void 0||n===c){var p=m.join("=");d[n]||(d[n]=[]);d[n].push(b?p:decodeURIComponent(p.replace(/\+/g," ")))}}return d}
function Wk(a){try{return decodeURIComponent(a)}catch(b){}}function Xk(a,b,c,d,e){b&&(b=String(b).toLowerCase());if(b==="protocol"||b==="port")a.protocol=Yk(a.protocol)||Yk(x.location.protocol);b==="port"?a.port=String(Number(a.hostname?a.port:x.location.port)||(a.protocol==="http"?80:a.protocol==="https"?443:"")):b==="host"&&(a.hostname=(a.hostname||x.location.hostname).replace(Sk,"").toLowerCase());return Zk(a,b,c,d,e)}
function Zk(a,b,c,d,e){var f,g=Yk(a.protocol);b&&(b=String(b).toLowerCase());switch(b){case "url_no_fragment":f=$k(a);break;case "protocol":f=g;break;case "host":f=a.hostname.replace(Sk,"").toLowerCase();if(c){var h=/^www\d*\./.exec(f);h&&h[0]&&(f=f.substring(h[0].length))}break;case "port":f=String(Number(a.port)||(g==="http"?80:g==="https"?443:""));break;case "path":a.pathname||a.hostname||fb("TAGGING",1);f=a.pathname.substring(0,1)==="/"?a.pathname:"/"+a.pathname;var m=f.split("/");(d||[]).indexOf(m[m.length-
1])>=0&&(m[m.length-1]="");f=m.join("/");break;case "query":f=a.search.replace("?","");e&&(f=Uk(f,e,!1));break;case "extension":var n=a.pathname.split(".");f=n.length>1?n[n.length-1]:"";f=f.split("/")[0];break;case "fragment":f=a.hash.replace("#","");break;default:f=a&&a.href}return f}function Yk(a){return a?a.replace(":","").toLowerCase():""}function $k(a){var b="";if(a&&a.href){var c=a.href.indexOf("#");b=c<0?a.href:a.href.substring(0,c)}return b}var al={},bl=0;
function cl(a){var b=al[a];if(!b){var c=A.createElement("a");a&&(c.href=a);var d=c.pathname;d[0]!=="/"&&(a||fb("TAGGING",1),d="/"+d);var e=c.hostname.replace(Sk,"");b={href:c.href,protocol:c.protocol,host:c.host,hostname:e,pathname:d,search:c.search,hash:c.hash,port:c.port};bl<5&&(al[a]=b,bl++)}return b}function dl(a,b,c){var d=cl(a);return Nb(b,d,c)}
function el(a){var b=cl(x.location.href),c=Xk(b,"host",!1);if(c&&c.match(Tk)){var d=Xk(b,"path");if(d){var e=d.split(a+"=");if(e.length>1)return e[1].split(";")[0].split("?")[0]}}};var fl={"https://www.google.com":"/g","https://www.googleadservices.com":"/as","https://pagead2.googlesyndication.com":"/gs"},gl=["/as/d/ccm/conversion","/g/d/ccm/conversion","/gs/ccm/conversion","/d/ccm/form-data"];function hl(a,b){if(a){var c=""+a;c.indexOf("http://")!==0&&c.indexOf("https://")!==0&&(c="https://"+c);c[c.length-1]==="/"&&(c=c.substring(0,c.length-1));return cl(""+c+b).href}}function il(a,b){if(wk()||Xj.N)return hl(a,b)}
function jl(){return!!bk.Li&&bk.Li.split("@@").join("")!=="SGTM_TOKEN"}function kl(a){for(var b=l([J.m.rd,J.m.vc]),c=b.next();!c.done;c=b.next()){var d=O(a,c.value);if(d)return d}}function ll(a,b,c){c=c===void 0?"":c;if(!wk())return a;var d=b?fl[a]||"":"";d==="/gs"&&(c="");return""+vk()+d+c}function ml(a){if(!wk())return a;for(var b=l(gl),c=b.next();!c.done;c=b.next())if(Gb(a,""+vk()+c.value))return a+"&_uip="+encodeURIComponent("::");return a};function nl(a){var b=String(a[jf.Ua]||"").replace(/_/g,"");return Gb(b,"cvt")?"cvt":b}var ol=x.location.search.indexOf("?gtm_latency=")>=0||x.location.search.indexOf("&gtm_latency=")>=0;var pl={sq:aj(27,Number("0.005000")),Zo:aj(42,Number("0.010000"))},ql=Math.random(),rl=ol||ql<Number(pl.sq),sl=ol||ql>=1-Number(pl.Zo);var tl=function(a,b){var c=function(){};c.prototype=a.prototype;var d=new c;a.apply(d,Array.prototype.slice.call(arguments,1));return d},ul=function(a){var b=a;return function(){if(b){var c=b;b=null;c()}}};var vl,wl;a:{for(var xl=["CLOSURE_FLAGS"],yl=Da,zl=0;zl<xl.length;zl++)if(yl=yl[xl[zl]],yl==null){wl=null;break a}wl=yl}var Al=wl&&wl[610401301];vl=Al!=null?Al:!1;function Bl(){var a=Da.navigator;if(a){var b=a.userAgent;if(b)return b}return""}var Cl,Dl=Da.navigator;Cl=Dl?Dl.userAgentData||null:null;function El(a){if(!vl||!Cl)return!1;for(var b=0;b<Cl.brands.length;b++){var c=Cl.brands[b].brand;if(c&&c.indexOf(a)!=-1)return!0}return!1}function Fl(a){return Bl().indexOf(a)!=-1};function Gl(){return vl?!!Cl&&Cl.brands.length>0:!1}function Hl(){return Gl()?!1:Fl("Opera")}function Il(){return Fl("Firefox")||Fl("FxiOS")}function Jl(){return Gl()?El("Chromium"):(Fl("Chrome")||Fl("CriOS"))&&!(Gl()?0:Fl("Edge"))||Fl("Silk")};var Kl=function(a){Kl[" "](a);return a};Kl[" "]=function(){};var Ll=function(a){return decodeURIComponent(a.replace(/\+/g," "))};function Ml(){return vl?!!Cl&&!!Cl.platform:!1}function Nl(){return Fl("iPhone")&&!Fl("iPod")&&!Fl("iPad")}function Ol(){Nl()||Fl("iPad")||Fl("iPod")};Hl();Gl()||Fl("Trident")||Fl("MSIE");Fl("Edge");!Fl("Gecko")||Bl().toLowerCase().indexOf("webkit")!=-1&&!Fl("Edge")||Fl("Trident")||Fl("MSIE")||Fl("Edge");Bl().toLowerCase().indexOf("webkit")!=-1&&!Fl("Edge")&&Fl("Mobile");Ml()||Fl("Macintosh");Ml()||Fl("Windows");(Ml()?Cl.platform==="Linux":Fl("Linux"))||Ml()||Fl("CrOS");Ml()||Fl("Android");Nl();Fl("iPad");Fl("iPod");Ol();Bl().toLowerCase().indexOf("kaios");var Pl=function(a){try{var b;if(b=!!a&&a.location.href!=null)a:{try{Kl(a.foo);b=!0;break a}catch(c){}b=!1}return b}catch(c){return!1}},Ql=function(a,b){if(a)for(var c in a)Object.prototype.hasOwnProperty.call(a,c)&&b(a[c],c,a)},Rl=function(a,b){for(var c=a,d=0;d<50;++d){var e;try{e=!(!c.frames||!c.frames[b])}catch(h){e=!1}if(e)return c;var f;a:{try{var g=c.parent;if(g&&g!=c){f=g;break a}}catch(h){}f=null}if(!(c=f))break}return null},Sl=function(a){var b=x;if(b.top==b)return 0;if(a===void 0?0:a){var c=
b.location.ancestorOrigins;if(c)return c[c.length-1]==b.location.origin?1:2}return Pl(b.top)?1:2},Tl=function(a){a=a===void 0?document:a;return a.createElement("img")},Ul=function(){for(var a=x,b=a;a&&a!=a.parent;)a=a.parent,Pl(a)&&(b=a);return b};function Vl(a){var b;b=b===void 0?document:b;var c;return!((c=b.featurePolicy)==null||!c.allowedFeatures().includes(a))};function Wl(){return Vl("join-ad-interest-group")&&lb(uc.joinAdInterestGroup)}
function Xl(a,b,c){var d=Qa[3]===void 0?1:Qa[3],e='iframe[data-tagging-id="'+b+'"]',f=[];try{if(d===1){var g=A.querySelector(e);g&&(f=[g])}else f=Array.from(A.querySelectorAll(e))}catch(r){}var h;a:{try{h=A.querySelectorAll('iframe[allow="join-ad-interest-group"][data-tagging-id*="-"]');break a}catch(r){}h=void 0}var m=h,n=((m==null?void 0:m.length)||0)>=(Qa[2]===void 0?50:Qa[2]),p;if(p=f.length>=1){var q=Number(f[f.length-1].dataset.loadTime);q!==void 0&&Ab()-q<(Qa[1]===void 0?6E4:Qa[1])?(fb("TAGGING",
9),p=!0):p=!1}if(p)return!1;if(d===1)if(f.length>=1)Yl(f[0]);else{if(n)return fb("TAGGING",10),!1}else f.length>=d?Yl(f[0]):n&&Yl(m[0]);Ic(a,c,{allow:"join-ad-interest-group"},{taggingId:b,loadTime:Ab()});return!0}function Yl(a){try{a.parentNode.removeChild(a)}catch(b){}};function Zl(a,b,c){var d,e=a.GooglebQhCsO;e||(e={},a.GooglebQhCsO=e);d=e;if(d[b])return!1;d[b]=[];d[b][0]=c;return!0};var $l=function(a){for(var b=[],c=0,d=0;d<a.length;d++){var e=a.charCodeAt(d);e<128?b[c++]=e:(e<2048?b[c++]=e>>6|192:((e&64512)==55296&&d+1<a.length&&(a.charCodeAt(d+1)&64512)==56320?(e=65536+((e&1023)<<10)+(a.charCodeAt(++d)&1023),b[c++]=e>>18|240,b[c++]=e>>12&63|128):b[c++]=e>>12|224,b[c++]=e>>6&63|128),b[c++]=e&63|128)}return b};Il();Nl()||Fl("iPod");Fl("iPad");!Fl("Android")||Jl()||Il()||Hl()||Fl("Silk");Jl();!Fl("Safari")||Jl()||(Gl()?0:Fl("Coast"))||Hl()||(Gl()?0:Fl("Edge"))||(Gl()?El("Microsoft Edge"):Fl("Edg/"))||(Gl()?El("Opera"):Fl("OPR"))||Il()||Fl("Silk")||Fl("Android")||Ol();var am={},bm=null,cm=function(a){for(var b=[],c=0,d=0;d<a.length;d++){var e=a.charCodeAt(d);e>255&&(b[c++]=e&255,e>>=8);b[c++]=e}var f=4;f===void 0&&(f=0);if(!bm){bm={};for(var g="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split(""),h=["+/=","+/","-_=","-_.","-_"],m=0;m<5;m++){var n=g.concat(h[m].split(""));am[m]=n;for(var p=0;p<n.length;p++){var q=n[p];bm[q]===void 0&&(bm[q]=p)}}}for(var r=am[f],t=Array(Math.floor(b.length/3)),u=r[64]||"",v=0,w=0;v<b.length-2;v+=3){var y=b[v],
z=b[v+1],C=b[v+2],D=r[y>>2],G=r[(y&3)<<4|z>>4],I=r[(z&15)<<2|C>>6],M=r[C&63];t[w++]=""+D+G+I+M}var T=0,da=u;switch(b.length-v){case 2:T=b[v+1],da=r[(T&15)<<2]||u;case 1:var N=b[v];t[w]=""+r[N>>2]+r[(N&3)<<4|T>>4]+da+u}return t.join("")};var dm=function(a,b,c,d){for(var e=b,f=c.length;(e=a.indexOf(c,e))>=0&&e<d;){var g=a.charCodeAt(e-1);if(g==38||g==63){var h=a.charCodeAt(e+f);if(!h||h==61||h==38||h==35)return e}e+=f+1}return-1},em=/#|$/,fm=function(a,b){var c=a.search(em),d=dm(a,0,b,c);if(d<0)return null;var e=a.indexOf("&",d);if(e<0||e>c)e=c;d+=b.length+1;return Ll(a.slice(d,e!==-1?e:0))},gm=/[?&]($|#)/,hm=function(a,b,c){for(var d,e=a.search(em),f=0,g,h=[];(g=dm(a,f,b,e))>=0;)h.push(a.substring(f,g)),f=Math.min(a.indexOf("&",g)+
1||e,e);h.push(a.slice(f));d=h.join("").replace(gm,"$1");var m,n=c!=null?"="+encodeURIComponent(String(c)):"";var p=b+n;if(p){var q,r=d.indexOf("#");r<0&&(r=d.length);var t=d.indexOf("?"),u;t<0||t>r?(t=r,u=""):u=d.substring(t+1,r);q=[d.slice(0,t),u,d.slice(r)];var v=q[1];q[1]=p?v?v+"&"+p:p:v;m=q[0]+(q[1]?"?"+q[1]:"")+q[2]}else m=d;return m};function im(a,b,c,d,e,f,g){var h=fm(c,"fmt");if(d){var m=fm(c,"random"),n=fm(c,"label")||"";if(!m)return!1;var p=cm(Ll(n)+":"+Ll(m));if(!Zl(a,p,d))return!1}h&&Number(h)!==4&&(c=hm(c,"rfmt",h));var q=hm(c,"fmt",4),r=b.getElementsByTagName("script")[0].parentElement;g==null||jm(g);Gc(q,function(){g==null||km(g);a.google_noFurtherRedirects&&d&&(a.google_noFurtherRedirects=null,d())},function(){g==null||km(g);e==null||e()},f,r||void 0);return!0};var lm={},mm=(lm[1]={},lm[2]={},lm[3]={},lm[4]={},lm);function nm(a,b,c){var d=om(b,c);if(d){var e=mm[b][d];e||(e=mm[b][d]=[]);e.push(ma(Object,"assign").call(Object,{},a))}}function pm(a,b){var c=om(a,b);if(c){var d=mm[a][c];d&&(mm[a][c]=d.filter(function(e){return!e.Bm}))}}function qm(a){switch(a){case "script-src":case "script-src-elem":return 1;case "frame-src":return 4;case "connect-src":return 2;case "img-src":return 3}}
function om(a,b){var c=b;if(b[0]==="/"){var d;c=((d=x.location)==null?void 0:d.origin)+b}try{var e=new URL(c);return a===4?e.origin:e.origin+e.pathname}catch(f){}}function rm(a){var b=Ca.apply(1,arguments);sl&&(nm(a,2,b[0]),nm(a,3,b[0]));Sc.apply(null,ya(b))}function sm(a){var b=Ca.apply(1,arguments);sl&&nm(a,2,b[0]);return Tc.apply(null,ya(b))}function tm(a){var b=Ca.apply(1,arguments);sl&&nm(a,3,b[0]);Jc.apply(null,ya(b))}
function um(a){var b=Ca.apply(1,arguments),c=b[0];sl&&(nm(a,2,c),nm(a,3,c));return Vc.apply(null,ya(b))}function vm(a){var b=Ca.apply(1,arguments);sl&&nm(a,1,b[0]);Gc.apply(null,ya(b))}function wm(a){var b=Ca.apply(1,arguments);b[0]&&sl&&nm(a,4,b[0]);Ic.apply(null,ya(b))}function xm(a){var b=Ca.apply(1,arguments);sl&&nm(a,1,b[2]);return im.apply(null,ya(b))}function ym(a){var b=Ca.apply(1,arguments);sl&&nm(a,4,b[0]);Xl.apply(null,ya(b))};var zm=/gtag[.\/]js/,Am=/gtm[.\/]js/,Bm=!1;function Cm(a){if(Bm)return"1";var b,c=(b=a.scriptElement)==null?void 0:b.src;if(c){if(zm.test(c))return"3";if(Am.test(c))return"2"}return"0"};function Dm(a,b,c){var d=Em(),e=Fm().container[a];e&&e.state!==3||(Fm().container[a]={state:1,context:b,parent:d},Gm({ctid:a,isDestination:!1},c))}function Gm(a,b){var c=Fm();c.pending||(c.pending=[]);pb(c.pending,function(d){return d.target.ctid===a.ctid&&d.target.isDestination===a.isDestination})||c.pending.push({target:a,onLoad:b})}function Hm(){var a=x.google_tags_first_party;Array.isArray(a)||(a=[]);for(var b={},c=l(a),d=c.next();!d.done;d=c.next())b[d.value]=!0;return Object.freeze(b)}
var Im=function(){this.container={};this.destination={};this.canonical={};this.pending=[];this.injectedFirstPartyContainers={};this.injectedFirstPartyContainers=Hm()};function Fm(){var a=yc("google_tag_data",{}),b=a.tidr;b&&typeof b==="object"||(b=new Im,a.tidr=b);var c=b;c.container||(c.container={});c.destination||(c.destination={});c.canonical||(c.canonical={});c.pending||(c.pending=[]);c.injectedFirstPartyContainers||(c.injectedFirstPartyContainers=Hm());return c};var Jm={},lg={ctid:Zi(5,"G-SMNVEQYP6G"),canonicalContainerId:Zi(6,"204592928"),tm:Zi(10,"G-SMNVEQYP6G|GT-M3LVV29P"),vm:Zi(9,"G-SMNVEQYP6G")};Jm.qe=Yi(7,wb(""));function Km(){return Jm.qe&&Lm().some(function(a){return a===lg.ctid})}function Mm(){return lg.canonicalContainerId||"_"+lg.ctid}function Nm(){return lg.tm?lg.tm.split("|"):[lg.ctid]}
function Lm(){return lg.vm?lg.vm.split("|").filter(function(a){return a.indexOf("GTM-")!==0}):[]}function Om(){var a=Pm(Em()),b=a&&a.parent;if(b)return Pm(b)}function Qm(){var a=Pm(Em());if(a){for(;a.parent;){var b=Pm(a.parent);if(!b)break;a=b}return a}}function Pm(a){var b=Fm();return a.isDestination?b.destination[a.ctid]:b.container[a.ctid]}
function Rm(){var a=Fm();if(a.pending){for(var b,c=[],d=!1,e=Nm(),f=Lm(),g={},h=0;h<a.pending.length;g={kg:void 0},h++)g.kg=a.pending[h],pb(g.kg.target.isDestination?f:e,function(m){return function(n){return n===m.kg.target.ctid}}(g))?d||(b=g.kg.onLoad,d=!0):c.push(g.kg);a.pending=c;if(b)try{b(Mm())}catch(m){}}}
function Sm(){for(var a=lg.ctid,b=Nm(),c=Lm(),d=function(n,p){var q={canonicalContainerId:lg.canonicalContainerId,scriptContainerId:a,state:2,containers:b.slice(),destinations:c.slice()};wc&&(q.scriptElement=wc);xc&&(q.scriptSource=xc);if(Om()===void 0){var r;a:{if((q.scriptContainerId||"").indexOf("GTM-")>=0){var t;b:{var u,v=(u=q.scriptElement)==null?void 0:u.src;if(v){for(var w=Xj.C,y=cl(v),z=w?y.pathname:""+y.hostname+y.pathname,C=A.scripts,D="",G=0;G<C.length;++G){var I=C[G];if(!(I.innerHTML.length===
0||!w&&I.innerHTML.indexOf(q.scriptContainerId||"SHOULD_NOT_BE_SET")<0||I.innerHTML.indexOf(z)<0)){if(I.innerHTML.indexOf("(function(w,d,s,l,i)")>=0){t=String(G);break b}D=String(G)}}if(D){t=D;break b}}t=void 0}var M=t;if(M){Bm=!0;r=M;break a}}var T=[].slice.call(A.scripts);r=q.scriptElement?String(T.indexOf(q.scriptElement)):"-1"}q.htmlLoadOrder=r;q.loadScriptType=Cm(q)}var da=p?e.destination:e.container,N=da[n];N?(p&&N.state===0&&L(93),ma(Object,"assign").call(Object,N,q)):da[n]=q},e=Fm(),f=l(b),
g=f.next();!g.done;g=f.next())d(g.value,!1);for(var h=l(c),m=h.next();!m.done;m=h.next())d(m.value,!0);e.canonical[Mm()]={};Rm()}function Tm(){var a=Mm();return!!Fm().canonical[a]}function Um(a){return!!Fm().container[a]}function Vm(a){var b=Fm().destination[a];return!!b&&!!b.state}function Em(){return{ctid:lg.ctid,isDestination:Jm.qe}}function Wm(){var a=Fm().container,b;for(b in a)if(a.hasOwnProperty(b)&&a[b].state===1)return!0;return!1}
function Xm(){var a={};tb(Fm().destination,function(b,c){c.state===0&&(a[b]=c)});return a}function Ym(a){return!!(a&&a.parent&&a.context&&a.context.source===1&&a.parent.ctid.indexOf("GTM-")!==0)}function Zm(){for(var a=Fm(),b=l(Nm()),c=b.next();!c.done;c=b.next())if(a.injectedFirstPartyContainers[c.value])return!0;return!1};var $m={Ia:{me:0,pe:1,Hi:2}};$m.Ia[$m.Ia.me]="FULL_TRANSMISSION";$m.Ia[$m.Ia.pe]="LIMITED_TRANSMISSION";$m.Ia[$m.Ia.Hi]="NO_TRANSMISSION";var an={X:{Ib:0,Fa:1,Fc:2,Oc:3}};an.X[an.X.Ib]="NO_QUEUE";an.X[an.X.Fa]="ADS";an.X[an.X.Fc]="ANALYTICS";an.X[an.X.Oc]="MONITORING";function bn(){var a=yc("google_tag_data",{});return a.ics=a.ics||new cn}var cn=function(){this.entries={};this.waitPeriodTimedOut=this.wasSetLate=this.accessedAny=this.accessedDefault=this.usedImplicit=this.usedUpdate=this.usedDefault=this.usedDeclare=this.active=!1;this.C=[]};
cn.prototype.default=function(a,b,c,d,e,f,g){this.usedDefault||this.usedDeclare||!this.accessedDefault&&!this.accessedAny||(this.wasSetLate=!0);this.usedDefault=this.active=!0;fb("TAGGING",19);b==null?fb("TAGGING",18):dn(this,a,b==="granted",c,d,e,f,g)};cn.prototype.waitForUpdate=function(a,b,c){for(var d=0;d<a.length;d++)dn(this,a[d],void 0,void 0,"","",b,c)};
var dn=function(a,b,c,d,e,f,g,h){var m=a.entries,n=m[b]||{},p=n.region,q=d&&mb(d)?d.toUpperCase():void 0;e=e.toUpperCase();f=f.toUpperCase();if(e===""||q===f||(q===e?p!==f:!q&&!p)){var r=!!(g&&g>0&&n.update===void 0),t={region:q,declare_region:n.declare_region,implicit:n.implicit,default:c!==void 0?c:n.default,declare:n.declare,update:n.update,quiet:r};if(e!==""||n.default!==!1)m[b]=t;r&&x.setTimeout(function(){m[b]===t&&t.quiet&&(fb("TAGGING",2),a.waitPeriodTimedOut=!0,a.clearTimeout(b,void 0,h),
a.notifyListeners())},g)}};k=cn.prototype;k.clearTimeout=function(a,b,c){var d=[a],e=c.delegatedConsentTypes,f;for(f in e)e.hasOwnProperty(f)&&e[f]===a&&d.push(f);var g=this.entries[a]||{},h=this.getConsentState(a,c);if(g.quiet){g.quiet=!1;for(var m=l(d),n=m.next();!n.done;n=m.next())en(this,n.value)}else if(b!==void 0&&h!==b)for(var p=l(d),q=p.next();!q.done;q=p.next())en(this,q.value)};
k.update=function(a,b,c){this.usedDefault||this.usedDeclare||this.usedUpdate||!this.accessedAny||(this.wasSetLate=!0);this.usedUpdate=this.active=!0;if(b!=null){var d=this.getConsentState(a,c),e=this.entries;(e[a]=e[a]||{}).update=b==="granted";this.clearTimeout(a,d,c)}};
k.declare=function(a,b,c,d,e){this.usedDeclare=this.active=!0;var f=this.entries,g=f[a]||{},h=g.declare_region,m=c&&mb(c)?c.toUpperCase():void 0;d=d.toUpperCase();e=e.toUpperCase();if(d===""||m===e||(m===d?h!==e:!m&&!h)){var n={region:g.region,declare_region:m,declare:b==="granted",implicit:g.implicit,default:g.default,update:g.update,quiet:g.quiet};if(d!==""||g.declare!==!1)f[a]=n}};
k.implicit=function(a,b){this.usedImplicit=!0;var c=this.entries,d=c[a]=c[a]||{};d.implicit!==!1&&(d.implicit=b==="granted")};
k.getConsentState=function(a,b){var c=this.entries,d=c[a]||{},e=d.update;if(e!==void 0)return e?1:2;if(b.usedContainerScopedDefaults){var f=b.containerScopedDefaults[a];if(f===3)return 1;if(f===2)return 2}else if(e=d.default,e!==void 0)return e?1:2;if(b==null?0:b.delegatedConsentTypes.hasOwnProperty(a)){var g=b.delegatedConsentTypes[a],h=c[g]||{};e=h.update;if(e!==void 0)return e?1:2;if(b.usedContainerScopedDefaults){var m=b.containerScopedDefaults[g];if(m===3)return 1;if(m===2)return 2}else if(e=
h.default,e!==void 0)return e?1:2}e=d.declare;if(e!==void 0)return e?1:2;e=d.implicit;return e!==void 0?e?3:4:0};k.addListener=function(a,b){this.C.push({consentTypes:a,Sc:b})};var en=function(a,b){for(var c=0;c<a.C.length;++c){var d=a.C[c];Array.isArray(d.consentTypes)&&d.consentTypes.indexOf(b)!==-1&&(d.wm=!0)}};cn.prototype.notifyListeners=function(a,b){for(var c=0;c<this.C.length;++c){var d=this.C[c];if(d.wm){d.wm=!1;try{d.Sc({consentEventId:a,consentPriorityId:b})}catch(e){}}}};var fn=!1,gn=!1,hn={},jn={delegatedConsentTypes:{},corePlatformServices:{},usedCorePlatformServices:!1,selectedAllCorePlatformServices:!1,containerScopedDefaults:(hn.ad_storage=1,hn.analytics_storage=1,hn.ad_user_data=1,hn.ad_personalization=1,hn),usedContainerScopedDefaults:!1};function kn(a){var b=bn();b.accessedAny=!0;return(mb(a)?[a]:a).every(function(c){switch(b.getConsentState(c,jn)){case 1:case 3:return!0;case 2:case 4:return!1;default:return!0}})}
function ln(a){var b=bn();b.accessedAny=!0;return b.getConsentState(a,jn)}function mn(a){var b=bn();b.accessedAny=!0;return!(b.entries[a]||{}).quiet}function nn(){if(!Ra(7))return!1;var a=bn();a.accessedAny=!0;if(a.active)return!0;if(!jn.usedContainerScopedDefaults)return!1;for(var b=l(Object.keys(jn.containerScopedDefaults)),c=b.next();!c.done;c=b.next())if(jn.containerScopedDefaults[c.value]!==1)return!0;return!1}function on(a,b){bn().addListener(a,b)}
function pn(a,b){bn().notifyListeners(a,b)}function qn(a,b){function c(){for(var e=0;e<b.length;e++)if(!mn(b[e]))return!0;return!1}if(c()){var d=!1;on(b,function(e){d||c()||(d=!0,a(e))})}else a({})}
function rn(a,b){function c(){for(var h=[],m=0;m<e.length;m++){var n=e[m];kn(n)&&!f[n]&&h.push(n)}return h}function d(h){for(var m=0;m<h.length;m++)f[h[m]]=!0}var e=mb(b)?[b]:b,f={},g=c();g.length!==e.length&&(d(g),on(e,function(h){function m(q){q.length!==0&&(d(q),h.consentTypes=q,a(h))}var n=c();if(n.length!==0){var p=Object.keys(f).length;n.length+p>=e.length?m(n):x.setTimeout(function(){m(c())},500)}}))};var sn={},tn=(sn[an.X.Ib]=$m.Ia.me,sn[an.X.Fa]=$m.Ia.me,sn[an.X.Fc]=$m.Ia.me,sn[an.X.Oc]=$m.Ia.me,sn),un=function(a,b){this.C=a;this.consentTypes=b};un.prototype.isConsentGranted=function(){switch(this.C){case 0:return this.consentTypes.every(function(a){return kn(a)});case 1:return this.consentTypes.some(function(a){return kn(a)});default:mc(this.C,"consentsRequired had an unknown type")}};
var vn={},wn=(vn[an.X.Ib]=new un(0,[]),vn[an.X.Fa]=new un(0,["ad_storage"]),vn[an.X.Fc]=new un(0,["analytics_storage"]),vn[an.X.Oc]=new un(1,["ad_storage","analytics_storage"]),vn);var yn=function(a){var b=this;this.type=a;this.C=[];on(wn[a].consentTypes,function(){xn(b)||b.flush()})};yn.prototype.flush=function(){for(var a=l(this.C),b=a.next();!b.done;b=a.next()){var c=b.value;c()}this.C=[]};var xn=function(a){return tn[a.type]===$m.Ia.Hi&&!wn[a.type].isConsentGranted()},zn=function(a,b){xn(a)?a.C.push(b):b()},An=new Map;function Bn(a){An.has(a)||An.set(a,new yn(a));return An.get(a)};var Cn={Z:{Om:"aw_user_data_cache",Mh:"cookie_deprecation_label",yg:"diagnostics_page_id",Xn:"fl_user_data_cache",Zn:"ga4_user_data_cache",Ef:"ip_geo_data_cache",Ci:"ip_geo_fetch_in_progress",ql:"nb_data",sl:"page_experiment_ids",Of:"pt_data",tl:"pt_listener_set",Bl:"service_worker_endpoint",Dl:"shared_user_id",El:"shared_user_id_requested",kh:"shared_user_id_source"}};var Dn=function(a){return bf(function(b){for(var c in a)if(b===a[c]&&!/^[0-9]+$/.test(c))return!0;return!1})}(Cn.Z);
function En(a,b){b=b===void 0?!1:b;if(Dn(a)){var c,d,e=(d=(c=yc("google_tag_data",{})).xcd)!=null?d:c.xcd={};if(e[a])return e[a];if(b){var f=void 0,g=1,h={},m={set:function(n){f=n;m.notify()},get:function(){return f},subscribe:function(n){h[String(g)]=n;return g++},unsubscribe:function(n){var p=String(n);return h.hasOwnProperty(p)?(delete h[p],!0):!1},notify:function(){for(var n=l(Object.keys(h)),p=n.next();!p.done;p=n.next()){var q=p.value;try{h[q](a,f)}catch(r){}}}};return e[a]=m}}}
function Fn(a,b){var c=En(a,!0);c&&c.set(b)}function Gn(a){var b;return(b=En(a))==null?void 0:b.get()}function Hn(a){var b={},c=En(a);if(!c){c=En(a,!0);if(!c)return;c.set(b)}return c.get()}function In(a,b){if(typeof b==="function"){var c;return(c=En(a,!0))==null?void 0:c.subscribe(b)}}function Jn(a,b){var c=En(a);return c?c.unsubscribe(b):!1};var Kn="https://"+Zi(21,"www.googletagmanager.com"),Ln="/td?id="+lg.ctid,Mn={},Nn=(Mn.tdp=1,Mn.exp=1,Mn.pid=1,Mn.dl=1,Mn.seq=1,Mn.t=1,Mn.v=1,Mn),On=["mcc"],Pn={},Qn={},Rn=!1;function Sn(a,b,c){Qn[a]=b;(c===void 0||c)&&Tn(a)}function Tn(a,b){Pn[a]!==void 0&&(b===void 0||!b)||Gb(lg.ctid,"GTM-")&&a==="mcc"||(Pn[a]=!0)}
function Un(a){a=a===void 0?!1:a;var b=Object.keys(Pn).filter(function(c){return Pn[c]===!0&&Qn[c]!==void 0&&(a||!On.includes(c))}).map(function(c){var d=Qn[c];typeof d==="function"&&(d=d());return d?"&"+c+"="+d:""}).join("");return""+ll(Kn)+Ln+(""+b+"&z=0")}function Vn(){Object.keys(Pn).forEach(function(a){Nn[a]||(Pn[a]=!1)})}
function Wn(a){a=a===void 0?!1:a;if(Xj.fa&&sl&&lg.ctid){var b=Bn(an.X.Oc);if(xn(b))Rn||(Rn=!0,zn(b,Wn));else{var c=Un(a),d={destinationId:lg.ctid,endpoint:61};a?um(d,c,void 0,{Eh:!0},void 0,function(){tm(d,c+"&img=1")}):tm(d,c);Vn();Rn=!1}}}function Xn(){Object.keys(Pn).filter(function(a){return Pn[a]&&!Nn[a]}).length>0&&Wn(!0)}var Yn;function Zn(){if(Gn(Cn.Z.yg)===void 0){var a=function(){Fn(Cn.Z.yg,qb());Yn=0};a();x.setInterval(a,864E5)}else In(Cn.Z.yg,function(){Yn=0});Yn=0}
function $n(){Zn();Sn("v","3");Sn("t","t");Sn("pid",function(){return String(Gn(Cn.Z.yg))});Sn("seq",function(){return String(++Yn)});Sn("exp",uk());Lc(x,"pagehide",Xn)};var ao=["ad_storage","analytics_storage","ad_user_data","ad_personalization"],bo=[J.m.rd,J.m.vc,J.m.ce,J.m.Rb,J.m.Wb,J.m.Ma,J.m.Ta,J.m.eb,J.m.pb,J.m.Sb],co=!1,eo=!1,fo={},go={};function ho(){!eo&&co&&(ao.some(function(a){return jn.containerScopedDefaults[a]!==1})||io("mbc"));eo=!0}function io(a){sl&&(Sn(a,"1"),Wn())}function jo(a,b){if(!fo[b]&&(fo[b]=!0,go[b]))for(var c=l(bo),d=c.next();!d.done;d=c.next())if(O(a,d.value)){io("erc");break}};function ko(a){fb("HEALTH",a)};var lo={rp:Zi(22,"eyIwIjoiVk4iLCIxIjoiVk4tMjEiLCIyIjpmYWxzZSwiMyI6Imdvb2dsZS5jb20udm4iLCI0IjoiIiwiNSI6dHJ1ZSwiNiI6ZmFsc2UsIjciOiJhZF9zdG9yYWdlfGFuYWx5dGljc19zdG9yYWdlfGFkX3VzZXJfZGF0YXxhZF9wZXJzb25hbGl6YXRpb24ifQ")},mo={},no=!1;function oo(){function a(){c!==void 0&&Jn(Cn.Z.Ef,c);try{var e=Gn(Cn.Z.Ef);mo=JSON.parse(e)}catch(f){L(123),ko(2),mo={}}no=!0;b()}var b=po,c=void 0,d=Gn(Cn.Z.Ef);d?a(d):(c=In(Cn.Z.Ef,a),qo())}
function qo(){function a(c){Fn(Cn.Z.Ef,c||"{}");Fn(Cn.Z.Ci,!1)}if(!Gn(Cn.Z.Ci)){Fn(Cn.Z.Ci,!0);var b="";try{x.fetch(b,{method:"GET",cache:"no-store",mode:"cors",credentials:"omit"}).then(function(c){c.ok?c.text().then(function(d){a(d)},function(){a()}):a()},function(){a()})}catch(c){a()}}}
function ro(){var a=lo.rp;try{return JSON.parse(db(a))}catch(b){return L(123),ko(2),{}}}function so(){return mo["0"]||""}function to(){return mo["1"]||""}function uo(){var a=!1;a=!!mo["2"];return a}function vo(){return mo["6"]!==!1}function wo(){var a="";a=mo["4"]||"";return a}
function xo(){var a=!1;a=!!mo["5"];return a}function yo(){var a="";a=mo["3"]||"";return a};var zo={},Ao=Object.freeze((zo[J.m.Ga]=1,zo[J.m.Ag]=1,zo[J.m.Bg]=1,zo[J.m.Pb]=1,zo[J.m.sa]=1,zo[J.m.pb]=1,zo[J.m.qb]=1,zo[J.m.Ab]=1,zo[J.m.ed]=1,zo[J.m.Sb]=1,zo[J.m.eb]=1,zo[J.m.Hc]=1,zo[J.m.bf]=1,zo[J.m.oa]=1,zo[J.m.lk]=1,zo[J.m.ef]=1,zo[J.m.Kg]=1,zo[J.m.Lg]=1,zo[J.m.ce]=1,zo[J.m.Bk]=1,zo[J.m.sc]=1,zo[J.m.fe]=1,zo[J.m.Dk]=1,zo[J.m.Og]=1,zo[J.m.gi]=1,zo[J.m.Kc]=1,zo[J.m.Lc]=1,zo[J.m.Ta]=1,zo[J.m.hi]=1,zo[J.m.Vb]=1,zo[J.m.rb]=1,zo[J.m.pd]=1,zo[J.m.rd]=1,zo[J.m.qf]=1,zo[J.m.ji]=1,zo[J.m.je]=1,zo[J.m.vc]=
1,zo[J.m.ud]=1,zo[J.m.Vg]=1,zo[J.m.Xb]=1,zo[J.m.wd]=1,zo[J.m.Ki]=1,zo));Object.freeze([J.m.Ca,J.m.Ya,J.m.Gb,J.m.Bb,J.m.ii,J.m.Ma,J.m.di,J.m.An]);
var Bo={},Co=Object.freeze((Bo[J.m.dn]=1,Bo[J.m.fn]=1,Bo[J.m.gn]=1,Bo[J.m.hn]=1,Bo[J.m.jn]=1,Bo[J.m.mn]=1,Bo[J.m.nn]=1,Bo[J.m.on]=1,Bo[J.m.qn]=1,Bo[J.m.Wd]=1,Bo)),Do={},Eo=Object.freeze((Do[J.m.bk]=1,Do[J.m.dk]=1,Do[J.m.Sd]=1,Do[J.m.Td]=1,Do[J.m.ek]=1,Do[J.m.Xc]=1,Do[J.m.Ud]=1,Do[J.m.kc]=1,Do[J.m.Gc]=1,Do[J.m.mc]=1,Do[J.m.mb]=1,Do[J.m.Vd]=1,Do[J.m.zb]=1,Do[J.m.fk]=1,Do)),Fo=Object.freeze([J.m.Ga,J.m.Re,J.m.Pb,J.m.Hc,J.m.ce,J.m.lf,J.m.rb,J.m.ud]),Go=Object.freeze([].concat(ya(Fo))),Ho=Object.freeze([J.m.qb,
J.m.Lg,J.m.qf,J.m.ji,J.m.Ig]),Io=Object.freeze([].concat(ya(Ho))),Jo={},Ko=(Jo[J.m.U]="1",Jo[J.m.ja]="2",Jo[J.m.V]="3",Jo[J.m.Ka]="4",Jo),Lo={},Mo=Object.freeze((Lo.search="s",Lo.youtube="y",Lo.playstore="p",Lo.shopping="h",Lo.ads="a",Lo.maps="m",Lo));function No(a){return typeof a!=="object"||a===null?{}:a}function Oo(a){return a===void 0||a===null?"":typeof a==="object"?a.toString():String(a)}function Po(a){if(a!==void 0&&a!==null)return Oo(a)}function Qo(a){return typeof a==="number"?a:Po(a)};function Ro(a){return a&&a.indexOf("pending:")===0?So(a.substr(8)):!1}function So(a){if(a==null||a.length===0)return!1;var b=Number(a),c=Ab();return b<c+3E5&&b>c-9E5};var To=!1,Uo=!1,Vo=!1,Wo=0,Xo=!1,Yo=[];function Zo(a){if(Wo===0)Xo&&Yo&&(Yo.length>=100&&Yo.shift(),Yo.push(a));else if($o()){var b=Zi(41,'google.tagmanager.ta.prodqueue'),c=yc(b,[]);c.length>=50&&c.shift();c.push(a)}}function ap(){bp();Mc(A,"TAProdDebugSignal",ap)}function bp(){if(!Uo){Uo=!0;cp();var a=Yo;Yo=void 0;a==null||a.forEach(function(b){Zo(b)})}}
function cp(){var a=A.documentElement.getAttribute("data-tag-assistant-prod-present");So(a)?Wo=1:!Ro(a)||To||Vo?Wo=2:(Vo=!0,Lc(A,"TAProdDebugSignal",ap,!1),x.setTimeout(function(){bp();To=!0},200))}function $o(){if(!Xo)return!1;switch(Wo){case 1:case 0:return!0;case 2:return!1;default:return!1}};var dp=!1;function ep(a,b){var c=Nm(),d=Lm();if($o()){var e=fp("INIT");e.containerLoadSource=a!=null?a:0;b&&(e.parentTargetReference=b);e.aliases=c;e.destinations=d;Zo(e)}}
function gp(a){var b,c,d,e;b=a.targetId;c=a.request;d=a.Oa;e=a.isBatched;var f;if(f=$o()){var g;a:switch(c.endpoint){case 19:case 47:case 44:g=!0;break a;default:g=!1}f=!g}if(f){var h=fp("GTAG_HIT",{eventId:d.eventId,priorityId:d.priorityId});h.target=b;h.url=c.url;c.postBody&&(h.postBody=c.postBody);h.parameterEncoding=c.parameterEncoding;h.endpoint=c.endpoint;e!==void 0&&(h.isBatched=e);Zo(h)}}function hp(a){$o()&&gp(a())}
function fp(a,b){b=b===void 0?{}:b;b.groupId=ip;var c,d=b,e={publicId:jp};d.eventId!=null&&(e.eventId=d.eventId);d.priorityId!=null&&(e.priorityId=d.priorityId);d.eventName&&(e.eventName=d.eventName);d.groupId&&(e.groupId=d.groupId);d.tagName&&(e.tagName=d.tagName);c={containerProduct:"GTM",key:e,version:'1',messageType:a};c.containerProduct=dp?"OGT":"GTM";c.key.targetRef=kp;return c}var jp="",kp={ctid:"",isDestination:!1},ip;
function lp(a){var b=lg.ctid,c=Km();Wo=0;Xo=!0;cp();ip=a;jp=b;dp=lk;kp={ctid:b,isDestination:c}};var mp=[J.m.U,J.m.ja,J.m.V,J.m.Ka],np,op;function pp(a){var b=a[J.m.hc];b||(b=[""]);for(var c={cg:0};c.cg<b.length;c={cg:c.cg},++c.cg)tb(a,function(d){return function(e,f){if(e!==J.m.hc){var g=Oo(f),h=b[d.cg],m=so(),n=to();gn=!0;fn&&fb("TAGGING",20);bn().declare(e,g,h,m,n)}}}(c))}
function qp(a){ho();!op&&np&&io("crc");op=!0;var b=a[J.m.sg];b&&L(41);var c=a[J.m.hc];c?L(40):c=[""];for(var d={dg:0};d.dg<c.length;d={dg:d.dg},++d.dg)tb(a,function(e){return function(f,g){if(f!==J.m.hc&&f!==J.m.sg){var h=Po(g),m=c[e.dg],n=Number(b),p=so(),q=to();n=n===void 0?0:n;fn=!0;gn&&fb("TAGGING",20);bn().default(f,h,m,p,q,n,jn)}}}(d))}
function rp(a){jn.usedContainerScopedDefaults=!0;var b=a[J.m.hc];if(b){var c=Array.isArray(b)?b:[b];if(!c.includes(to())&&!c.includes(so()))return}tb(a,function(d,e){switch(d){case "ad_storage":case "analytics_storage":case "ad_user_data":case "ad_personalization":break;default:return}jn.usedContainerScopedDefaults=!0;jn.containerScopedDefaults[d]=e==="granted"?3:2})}
function sp(a,b){ho();np=!0;tb(a,function(c,d){var e=Oo(d);fn=!0;gn&&fb("TAGGING",20);bn().update(c,e,jn)});pn(b.eventId,b.priorityId)}function tp(a){a.hasOwnProperty("all")&&(jn.selectedAllCorePlatformServices=!0,tb(Mo,function(b){jn.corePlatformServices[b]=a.all==="granted";jn.usedCorePlatformServices=!0}));tb(a,function(b,c){b!=="all"&&(jn.corePlatformServices[b]=c==="granted",jn.usedCorePlatformServices=!0)})}function P(a){Array.isArray(a)||(a=[a]);return a.every(function(b){return kn(b)})}
function up(a,b){on(a,b)}function vp(a,b){rn(a,b)}function wp(a,b){qn(a,b)}function xp(){var a=[J.m.U,J.m.Ka,J.m.V];bn().waitForUpdate(a,500,jn)}function yp(a){for(var b=l(a),c=b.next();!c.done;c=b.next()){var d=c.value;bn().clearTimeout(d,void 0,jn)}pn()}function zp(){if(!mk)for(var a=vo()?xk(Xj.Va):xk(Xj.sb),b=0;b<mp.length;b++){var c=mp[b],d=c,e=a[c]?"granted":"denied";bn().implicit(d,e)}};var Ap=!1,Bp=[];function Cp(){if(!Ap){Ap=!0;for(var a=Bp.length-1;a>=0;a--)Bp[a]();Bp=[]}};var Dp=x.google_tag_manager=x.google_tag_manager||{};function Ep(a,b){return Dp[a]=Dp[a]||b()}function Fp(){var a=lg.ctid,b=Gp;Dp[a]=Dp[a]||b}function Hp(){var a=Dp.sequence||1;Dp.sequence=a+1;return a}x.google_tag_data=x.google_tag_data||{};function Ip(){if(Dp.pscdl!==void 0)Gn(Cn.Z.Mh)===void 0&&Fn(Cn.Z.Mh,Dp.pscdl);else{var a=function(c){Dp.pscdl=c;Fn(Cn.Z.Mh,c)},b=function(){a("error")};try{uc.cookieDeprecationLabel?(a("pending"),uc.cookieDeprecationLabel.getValue().then(a).catch(b)):a("noapi")}catch(c){b(c)}}};var Jp=0;function Kp(a){sl&&a===void 0&&Jp===0&&(Sn("mcc","1"),Jp=1)};var Lp={Cf:{Um:"cd",Vm:"ce",Wm:"cf",Xm:"cpf",Ym:"cu"}};var Mp=/^(?:AW|DC|G|GF|GT|HA|MC|UA)$/,Np=/\s/;
function Op(a,b){if(mb(a)){a=yb(a);var c=a.indexOf("-");if(!(c<0)){var d=a.substring(0,c);if(Mp.test(d)){var e=a.substring(c+1),f;if(b){var g=function(n){var p=n.indexOf("/");return p<0?[n]:[n.substring(0,p),n.substring(p+1)]};f=g(e);if(d==="DC"&&f.length===2){var h=g(f[1]);h.length===2&&(f[1]=h[0],f.push(h[1]))}}else{f=e.split("/");for(var m=0;m<f.length;m++)if(!f[m]||Np.test(f[m])&&(d!=="AW"||m!==1))return}return{id:a,prefix:d,destinationId:d+"-"+f[0],ids:f}}}}}
function Pp(a,b){for(var c={},d=0;d<a.length;++d){var e=Op(a[d],b);e&&(c[e.id]=e)}var f=[],g;for(g in c)if(c.hasOwnProperty(g)){var h=c[g];h.prefix==="AW"&&h.ids[Qp[1]]&&f.push(h.destinationId)}for(var m=0;m<f.length;++m)delete c[f[m]];for(var n=[],p=l(Object.keys(c)),q=p.next();!q.done;q=p.next())n.push(c[q.value]);return n}var Rp={},Qp=(Rp[0]=0,Rp[1]=1,Rp[2]=2,Rp[3]=0,Rp[4]=1,Rp[5]=0,Rp[6]=0,Rp[7]=0,Rp);var Sp=Number('')||500,Tp={},Up={},Vp={initialized:11,complete:12,interactive:13},Wp={},Xp=Object.freeze((Wp[J.m.rb]=!0,Wp)),Yp=void 0;function Zp(a,b){if(b.length&&sl){var c;(c=Tp)[a]!=null||(c[a]=[]);Up[a]!=null||(Up[a]=[]);var d=b.filter(function(e){return!Up[a].includes(e)});Tp[a].push.apply(Tp[a],ya(d));Up[a].push.apply(Up[a],ya(d));!Yp&&d.length>0&&(Tn("tdc",!0),Yp=x.setTimeout(function(){Wn();Tp={};Yp=void 0},Sp))}}
function $p(a,b){var c={},d;for(d in b)b.hasOwnProperty(d)&&(c[d]=!0);for(var e in a)a.hasOwnProperty(e)&&(c[e]=!0);return c}
function aq(a,b,c,d){c=c===void 0?{}:c;d=d===void 0?"":d;if(a===b)return[];var e=function(r,t){var u;jd(t)==="object"?u=t[r]:jd(t)==="array"&&(u=t[r]);return u===void 0?Xp[r]:u},f=$p(a,b),g;for(g in f)if(f.hasOwnProperty(g)){var h=(d?d+".":"")+g,m=e(g,a),n=e(g,b),p=jd(m)==="object"||jd(m)==="array",q=jd(n)==="object"||jd(n)==="array";if(p&&q)aq(m,n,c,h);else if(p||q||m!==n)c[h]=!0}return Object.keys(c)}
function bq(){Sn("tdc",function(){Yp&&(x.clearTimeout(Yp),Yp=void 0);var a=[],b;for(b in Tp)Tp.hasOwnProperty(b)&&a.push(b+"*"+Tp[b].join("."));return a.length?a.join("!"):void 0},!1)};var cq=function(a,b,c,d,e,f,g,h,m,n,p){this.eventId=a;this.priorityId=b;this.C=c;this.R=d;this.N=e;this.P=f;this.H=g;this.eventMetadata=h;this.onSuccess=m;this.onFailure=n;this.isGtmEvent=p},dq=function(a,b){var c=[];switch(b){case 3:c.push(a.C);c.push(a.R);c.push(a.N);c.push(a.P);c.push(a.H);break;case 2:c.push(a.C);break;case 1:c.push(a.R);c.push(a.N);c.push(a.P);c.push(a.H);break;case 4:c.push(a.C),c.push(a.R),c.push(a.N),c.push(a.P)}return c},O=function(a,b,c,d){for(var e=l(dq(a,d===void 0?3:
d)),f=e.next();!f.done;f=e.next()){var g=f.value;if(g[b]!==void 0)return g[b]}return c},eq=function(a){for(var b={},c=dq(a,4),d=l(c),e=d.next();!e.done;e=d.next())for(var f=Object.keys(e.value),g=l(f),h=g.next();!h.done;h=g.next())b[h.value]=1;return Object.keys(b)};
cq.prototype.getMergedValues=function(a,b,c){function d(n){ld(n)&&tb(n,function(p,q){f=!0;e[p]=q})}b=b===void 0?3:b;var e={},f=!1;c&&d(c);var g=dq(this,b);g.reverse();for(var h=l(g),m=h.next();!m.done;m=h.next())d(m.value[a]);return f?e:void 0};
var fq=function(a){for(var b=[J.m.We,J.m.Se,J.m.Te,J.m.Ue,J.m.Ve,J.m.Xe,J.m.Ye],c=dq(a,3),d=l(c),e=d.next();!e.done;e=d.next()){for(var f=e.value,g={},h=!1,m=l(b),n=m.next();!n.done;n=m.next()){var p=n.value;f[p]!==void 0&&(g[p]=f[p],h=!0)}var q=h?g:void 0;if(q)return q}return{}},gq=function(a,b){this.eventId=a;this.priorityId=b;this.H={};this.R={};this.C={};this.N={};this.fa={};this.P={};this.eventMetadata={};this.isGtmEvent=!1;this.onSuccess=function(){};this.onFailure=function(){}},hq=function(a,
b){a.H=b;return a},iq=function(a,b){a.R=b;return a},jq=function(a,b){a.C=b;return a},kq=function(a,b){a.N=b;return a},lq=function(a,b){a.fa=b;return a},mq=function(a,b){a.P=b;return a},nq=function(a,b){a.eventMetadata=b||{};return a},oq=function(a,b){a.onSuccess=b;return a},pq=function(a,b){a.onFailure=b;return a},qq=function(a,b){a.isGtmEvent=b;return a},rq=function(a){return new cq(a.eventId,a.priorityId,a.H,a.R,a.C,a.N,a.P,a.eventMetadata,a.onSuccess,a.onFailure,a.isGtmEvent)};var Q={A:{Jj:"accept_by_default",rg:"add_tag_timing",Ih:"allow_ad_personalization",Lj:"batch_on_navigation",Nj:"client_id_source",Ie:"consent_event_id",Je:"consent_priority_id",Rq:"consent_state",da:"consent_updated",Wc:"conversion_linker_enabled",xa:"cookie_options",ug:"create_dc_join",vg:"create_fpm_geo_join",wg:"create_fpm_signals_join",Rd:"create_google_join",Le:"em_event",Uq:"endpoint_for_debug",Zj:"enhanced_client_id_source",Ph:"enhanced_match_result",ke:"euid_mode_enabled",hb:"event_start_timestamp_ms",
Xk:"event_usage",Xg:"extra_tag_experiment_ids",er:"add_parameter",xi:"attribution_reporting_experiment",yi:"counting_method",Yg:"send_as_iframe",gr:"parameter_order",Zg:"parsed_target",Yn:"ga4_collection_subdomain",al:"gbraid_cookie_marked",ia:"hit_type",xd:"hit_type_override",eo:"is_config_command",Ff:"is_consent_update",Gf:"is_conversion",jl:"is_ecommerce",yd:"is_external_event",Di:"is_fallback_aw_conversion_ping_allowed",Hf:"is_first_visit",kl:"is_first_visit_conversion",ah:"is_fl_fallback_conversion_flow_allowed",
If:"is_fpm_encryption",bh:"is_fpm_split",ne:"is_gcp_conversion",ml:"is_google_signals_allowed",zd:"is_merchant_center",eh:"is_new_to_site",fh:"is_server_side_destination",oe:"is_session_start",ol:"is_session_start_conversion",jr:"is_sgtm_ga_ads_conversion_study_control_group",kr:"is_sgtm_prehit",pl:"is_sgtm_service_worker",Ei:"is_split_conversion",fo:"is_syn",Jf:"join_id",Fi:"join_elapsed",Kf:"join_timer_sec",se:"tunnel_updated",qr:"prehit_for_retry",ur:"promises",vr:"record_aw_latency",yc:"redact_ads_data",
te:"redact_click_ids",qo:"remarketing_only",zl:"send_ccm_parallel_ping",jh:"send_fledge_experiment",xr:"send_ccm_parallel_test_ping",Pf:"send_to_destinations",Ji:"send_to_targets",Al:"send_user_data_hit",ib:"source_canonical_id",Ba:"speculative",Fl:"speculative_in_message",Gl:"suppress_script_load",Hl:"syn_or_mod",Ll:"transient_ecsid",Qf:"transmission_type",jb:"user_data",Ar:"user_data_from_automatic",Br:"user_data_from_automatic_getter",ve:"user_data_from_code",nh:"user_data_from_manual",Nl:"user_data_mode",
Rf:"user_id_updated"}};var sq={Nm:Number("5"),Sr:Number("")},tq=[],uq=!1;function vq(a){tq.push(a)}var wq="?id="+lg.ctid,xq=void 0,yq={},zq=void 0,Aq=new function(){var a=5;sq.Nm>0&&(a=sq.Nm);this.H=a;this.C=0;this.N=[]},Bq=1E3;
function Cq(a,b){var c=xq;if(c===void 0)if(b)c=Hp();else return"";for(var d=[ll("https://www.googletagmanager.com"),"/a",wq],e=l(tq),f=e.next();!f.done;f=e.next())for(var g=f.value,h=g({eventId:c,Qd:!!a}),m=l(h),n=m.next();!n.done;n=m.next()){var p=l(n.value),q=p.next().value,r=p.next().value;d.push("&"+q+"="+r)}d.push("&z=0");return d.join("")}
function Dq(){if(Xj.fa&&(zq&&(x.clearTimeout(zq),zq=void 0),xq!==void 0&&Eq)){var a=Bn(an.X.Oc);if(xn(a))uq||(uq=!0,zn(a,Dq));else{var b;if(!(b=yq[xq])){var c=Aq;b=c.C<c.H?!1:Ab()-c.N[c.C%c.H]<1E3}if(b||Bq--<=0)L(1),yq[xq]=!0;else{var d=Aq,e=d.C++%d.H;d.N[e]=Ab();var f=Cq(!0);tm({destinationId:lg.ctid,endpoint:56,eventId:xq},f);uq=Eq=!1}}}}function Fq(){if(rl&&Xj.fa){var a=Cq(!0,!0);tm({destinationId:lg.ctid,endpoint:56,eventId:xq},a)}}var Eq=!1;
function Gq(a){yq[a]||(a!==xq&&(Dq(),xq=a),Eq=!0,zq||(zq=x.setTimeout(Dq,500)),Cq().length>=2022&&Dq())}var Hq=qb();function Iq(){Hq=qb()}function Jq(){return[["v","3"],["t","t"],["pid",String(Hq)]]};var Kq={};function Lq(a,b,c){rl&&a!==void 0&&(Kq[a]=Kq[a]||[],Kq[a].push(c+b),Gq(a))}function Mq(a){var b=a.eventId,c=a.Qd,d=[],e=Kq[b]||[];e.length&&d.push(["epr",e.join(".")]);c&&delete Kq[b];return d};function Nq(a,b,c,d){var e=Op(a,!0);e&&Oq.register(e,b,c,d)}function Pq(a,b,c,d){var e=Op(c,d.isGtmEvent);e&&(jk&&(d.deferrable=!0),Oq.push("event",[b,a],e,d))}function Qq(a,b,c,d){var e=Op(c,d.isGtmEvent);e&&Oq.push("get",[a,b],e,d)}function Rq(a){var b=Op(a,!0),c;b?c=Sq(Oq,b).C:c={};return c}function Uq(a,b){var c=Op(a,!0);c&&Vq(Oq,c,b)}
var Wq=function(){this.R={};this.C={};this.H={};this.fa=null;this.P={};this.N=!1;this.status=1},Xq=function(a,b,c,d){this.H=Ab();this.C=b;this.args=c;this.messageContext=d;this.type=a},Yq=function(){this.destinations={};this.C={};this.commands=[]},Sq=function(a,b){return a.destinations[b.destinationId]=a.destinations[b.destinationId]||new Wq},Zq=function(a,b,c,d){if(d.C){var e=Sq(a,d.C),f=e.fa;if(f){var g=md(c,null),h=md(e.R[d.C.id],null),m=md(e.P,null),n=md(e.C,null),p=md(a.C,null),q={};if(rl)try{q=
md(zk,null)}catch(w){L(72)}var r=d.C.prefix,t=function(w){Lq(d.messageContext.eventId,r,w)},u=rq(qq(pq(oq(nq(lq(kq(mq(jq(iq(hq(new gq(d.messageContext.eventId,d.messageContext.priorityId),g),h),m),n),p),q),d.messageContext.eventMetadata),function(){if(t){var w=t;t=void 0;w("2");if(d.messageContext.onSuccess)d.messageContext.onSuccess()}}),function(){if(t){var w=t;t=void 0;w("3");if(d.messageContext.onFailure)d.messageContext.onFailure()}}),!!d.messageContext.isGtmEvent)),v=function(){try{Lq(d.messageContext.eventId,
r,"1");var w=d.type,y=d.C.id;if(sl&&w==="config"){var z,C=(z=Op(y))==null?void 0:z.ids;if(!(C&&C.length>1)){var D,G=yc("google_tag_data",{});G.td||(G.td={});D=G.td;var I=md(u.P);md(u.C,I);var M=[],T;for(T in D)D.hasOwnProperty(T)&&aq(D[T],I).length&&M.push(T);M.length&&(Zp(y,M),fb("TAGGING",Vp[A.readyState]||14));D[y]=I}}f(d.C.id,b,d.H,u)}catch(da){Lq(d.messageContext.eventId,r,"4")}};b==="gtag.get"?v():zn(e.ma,v)}}};
Yq.prototype.register=function(a,b,c,d){var e=Sq(this,a);e.status!==3&&(e.fa=b,e.status=3,e.ma=Bn(c),Vq(this,a,d||{}),this.flush())};
Yq.prototype.push=function(a,b,c,d){c!==void 0&&(Sq(this,c).status===1&&(Sq(this,c).status=2,this.push("require",[{}],c,{})),Sq(this,c).N&&(d.deferrable=!1),d.eventMetadata||(d.eventMetadata={}),d.eventMetadata[Q.A.Pf]||(d.eventMetadata[Q.A.Pf]=[c.destinationId]),d.eventMetadata[Q.A.Ji]||(d.eventMetadata[Q.A.Ji]=[c.id]));this.commands.push(new Xq(a,c,b,d));d.deferrable||this.flush()};
Yq.prototype.flush=function(a){for(var b=this,c=[],d=!1,e={};this.commands.length;e={Qc:void 0,sh:void 0}){var f=this.commands[0],g=f.C;if(f.messageContext.deferrable)!g||Sq(this,g).N?(f.messageContext.deferrable=!1,this.commands.push(f)):c.push(f),this.commands.shift();else{switch(f.type){case "require":if(Sq(this,g).status!==3&&!a){this.commands.push.apply(this.commands,c);return}break;case "set":var h=f.args[0];tb(h,function(t,u){md(Ib(t,u),b.C)});Vj(h,!0);break;case "config":var m=Sq(this,g);
e.Qc={};tb(f.args[0],function(t){return function(u,v){md(Ib(u,v),t.Qc)}}(e));var n=!!e.Qc[J.m.ud];delete e.Qc[J.m.ud];var p=g.destinationId===g.id;Vj(e.Qc,!0);n||(p?m.P={}:m.R[g.id]={});m.N&&n||Zq(this,J.m.qa,e.Qc,f);m.N=!0;p?md(e.Qc,m.P):(md(e.Qc,m.R[g.id]),L(70));d=!0;break;case "event":e.sh={};tb(f.args[0],function(t){return function(u,v){md(Ib(u,v),t.sh)}}(e));Vj(e.sh);Zq(this,f.args[1],e.sh,f);break;case "get":var q={},r=(q[J.m.rc]=f.args[0],q[J.m.Ic]=f.args[1],q);Zq(this,J.m.Fb,r,f)}this.commands.shift();
$q(this,f)}}this.commands.push.apply(this.commands,c);d&&this.flush()};var $q=function(a,b){if(b.type!=="require")if(b.C)for(var c=Sq(a,b.C).H[b.type]||[],d=0;d<c.length;d++)c[d]();else for(var e in a.destinations)if(a.destinations.hasOwnProperty(e)){var f=a.destinations[e];if(f&&f.H)for(var g=f.H[b.type]||[],h=0;h<g.length;h++)g[h]()}},Vq=function(a,b,c){var d=md(c,null);md(Sq(a,b).C,d);Sq(a,b).C=d},Oq=new Yq;function ar(a,b,c){return typeof a.addEventListener==="function"?(a.addEventListener(b,c,!1),!0):!1}function br(a,b,c){typeof a.removeEventListener==="function"&&a.removeEventListener(b,c,!1)};function cr(a,b,c,d){d=d===void 0?!1:d;a.google_image_requests||(a.google_image_requests=[]);var e=Tl(a.document);if(c){var f=function(){if(c){var g=a.google_image_requests,h=rc(g,e);h>=0&&Array.prototype.splice.call(g,h,1)}br(e,"load",f);br(e,"error",f)};ar(e,"load",f);ar(e,"error",f)}d&&(e.attributionSrc="");e.src=b;a.google_image_requests.push(e)}
function dr(a){var b;b=b===void 0?!1:b;var c="https://pagead2.googlesyndication.com/pagead/gen_204?id=tcfe";Ql(a,function(d,e){if(d||d===0)c+="&"+e+"="+encodeURIComponent(String(d))});er(c,b)}
function er(a,b){var c=window,d;b=b===void 0?!1:b;d=d===void 0?!1:d;if(c.fetch){var e={keepalive:!0,credentials:"include",redirect:"follow",method:"get",mode:"no-cors"};d&&(e.mode="cors","setAttributionReporting"in XMLHttpRequest.prototype?e.attributionReporting={eventSourceEligible:"true",triggerEligible:"false"}:e.headers={"Attribution-Reporting-Eligible":"event-source"});c.fetch(a,e)}else cr(c,a,b===void 0?!1:b,d===void 0?!1:d)};var fr=function(){this.fa=this.fa;this.P=this.P};fr.prototype.fa=!1;fr.prototype.dispose=function(){this.fa||(this.fa=!0,this.N())};fr.prototype[ha.Symbol.dispose]=function(){this.dispose()};fr.prototype.addOnDisposeCallback=function(a,b){this.fa?b!==void 0?a.call(b):a():(this.P||(this.P=[]),b&&(a=a.bind(b)),this.P.push(a))};fr.prototype.N=function(){if(this.P)for(;this.P.length;)this.P.shift()()};function gr(a){a.addtlConsent!==void 0&&typeof a.addtlConsent!=="string"&&(a.addtlConsent=void 0);a.gdprApplies!==void 0&&typeof a.gdprApplies!=="boolean"&&(a.gdprApplies=void 0);return a.tcString!==void 0&&typeof a.tcString!=="string"||a.listenerId!==void 0&&typeof a.listenerId!=="number"?2:a.cmpStatus&&a.cmpStatus!=="error"?0:3}
var hr=function(a,b){b=b===void 0?{}:b;fr.call(this);this.C=null;this.ma={};this.sb=0;this.R=null;this.H=a;var c;this.Va=(c=b.timeoutMs)!=null?c:500;var d;this.Da=(d=b.Hr)!=null?d:!1};va(hr,fr);hr.prototype.N=function(){this.ma={};this.R&&(br(this.H,"message",this.R),delete this.R);delete this.ma;delete this.H;delete this.C;fr.prototype.N.call(this)};var jr=function(a){return typeof a.H.__tcfapi==="function"||ir(a)!=null};
hr.prototype.addEventListener=function(a){var b=this,c={internalBlockOnErrors:this.Da},d=ul(function(){return a(c)}),e=0;this.Va!==-1&&(e=setTimeout(function(){c.tcString="tcunavailable";c.internalErrorState=1;d()},this.Va));var f=function(g,h){clearTimeout(e);g?(c=g,c.internalErrorState=gr(c),c.internalBlockOnErrors=b.Da,h&&c.internalErrorState===0||(c.tcString="tcunavailable",h||(c.internalErrorState=3))):(c.tcString="tcunavailable",c.internalErrorState=3);a(c)};try{kr(this,"addEventListener",f)}catch(g){c.tcString=
"tcunavailable",c.internalErrorState=3,e&&(clearTimeout(e),e=0),d()}};hr.prototype.removeEventListener=function(a){a&&a.listenerId&&kr(this,"removeEventListener",null,a.listenerId)};
var mr=function(a,b,c){var d;d=d===void 0?"755":d;var e;a:{if(a.publisher&&a.publisher.restrictions){var f=a.publisher.restrictions[b];if(f!==void 0){e=f[d===void 0?"755":d];break a}}e=void 0}var g=e;if(g===0)return!1;var h=c;c===2?(h=0,g===2&&(h=1)):c===3&&(h=1,g===1&&(h=0));var m;if(h===0)if(a.purpose&&a.vendor){var n=lr(a.vendor.consents,d===void 0?"755":d);m=n&&b==="1"&&a.purposeOneTreatment&&a.publisherCC==="CH"?!0:n&&lr(a.purpose.consents,b)}else m=!0;else m=h===1?a.purpose&&a.vendor?lr(a.purpose.legitimateInterests,
b)&&lr(a.vendor.legitimateInterests,d===void 0?"755":d):!0:!0;return m},lr=function(a,b){return!(!a||!a[b])},kr=function(a,b,c,d){c||(c=function(){});var e=a.H;if(typeof e.__tcfapi==="function"){var f=e.__tcfapi;f(b,2,c,d)}else if(ir(a)){nr(a);var g=++a.sb;a.ma[g]=c;if(a.C){var h={};a.C.postMessage((h.__tcfapiCall={command:b,version:2,callId:g,parameter:d},h),"*")}}else c({},!1)},ir=function(a){if(a.C)return a.C;a.C=Rl(a.H,"__tcfapiLocator");return a.C},nr=function(a){if(!a.R){var b=function(c){try{var d;
d=(typeof c.data==="string"?JSON.parse(c.data):c.data).__tcfapiReturn;a.ma[d.callId](d.returnValue,d.success)}catch(e){}};a.R=b;ar(a.H,"message",b)}},or=function(a){if(a.gdprApplies===!1)return!0;a.internalErrorState===void 0&&(a.internalErrorState=gr(a));return a.cmpStatus==="error"||a.internalErrorState!==0?a.internalBlockOnErrors?(dr({e:String(a.internalErrorState)}),!1):!0:a.cmpStatus!=="loaded"||a.eventStatus!=="tcloaded"&&a.eventStatus!=="useractioncomplete"?!1:!0};var pr={1:0,3:0,4:0,7:3,9:3,10:3};function qr(){return Ep("tcf",function(){return{}})}var rr=function(){return new hr(x,{timeoutMs:-1})};
function sr(){var a=qr(),b=rr();jr(b)&&!tr()&&!ur()&&L(124);if(!a.active&&jr(b)){tr()&&(a.active=!0,a.purposes={},a.cmpId=0,a.tcfPolicyVersion=0,bn().active=!0,a.tcString="tcunavailable");xp();try{b.addEventListener(function(c){if(c.internalErrorState!==0)vr(a),yp([J.m.U,J.m.Ka,J.m.V]),bn().active=!0;else if(a.gdprApplies=c.gdprApplies,a.cmpId=c.cmpId,a.enableAdvertiserConsentMode=c.enableAdvertiserConsentMode,ur()&&(a.active=!0),!wr(c)||tr()||ur()){a.tcfPolicyVersion=c.tcfPolicyVersion;var d;if(c.gdprApplies===
!1){var e={},f;for(f in pr)pr.hasOwnProperty(f)&&(e[f]=!0);d=e;b.removeEventListener(c)}else if(wr(c)){var g={},h;for(h in pr)if(pr.hasOwnProperty(h))if(h==="1"){var m,n=c,p={qp:!0};p=p===void 0?{}:p;m=or(n)?n.gdprApplies===!1?!0:n.tcString==="tcunavailable"?!p.idpcApplies:(p.idpcApplies||n.gdprApplies!==void 0||p.qp)&&(p.idpcApplies||typeof n.tcString==="string"&&n.tcString.length)?mr(n,"1",0):!0:!1;g["1"]=m}else g[h]=mr(c,h,pr[h]);d=g}if(d){a.tcString=c.tcString||"tcempty";a.purposes=d;var q={},
r=(q[J.m.U]=a.purposes["1"]?"granted":"denied",q);a.gdprApplies!==!0?(yp([J.m.U,J.m.Ka,J.m.V]),bn().active=!0):(r[J.m.Ka]=a.purposes["3"]&&a.purposes["4"]?"granted":"denied",typeof a.tcfPolicyVersion==="number"&&a.tcfPolicyVersion>=4?r[J.m.V]=a.purposes["1"]&&a.purposes["7"]?"granted":"denied":yp([J.m.V]),sp(r,{eventId:0},{gdprApplies:a?a.gdprApplies:void 0,tcString:xr()||""}))}}else yp([J.m.U,J.m.Ka,J.m.V])})}catch(c){vr(a),yp([J.m.U,J.m.Ka,J.m.V]),bn().active=!0}}}
function vr(a){a.type="e";a.tcString="tcunavailable"}function wr(a){return a.eventStatus==="tcloaded"||a.eventStatus==="useractioncomplete"||a.eventStatus==="cmpuishown"}function tr(){return x.gtag_enable_tcf_support===!0}function ur(){return qr().enableAdvertiserConsentMode===!0}function xr(){var a=qr();if(a.active)return a.tcString}function yr(){var a=qr();if(a.active&&a.gdprApplies!==void 0)return a.gdprApplies?"1":"0"}
function zr(a){if(!pr.hasOwnProperty(String(a)))return!0;var b=qr();return b.active&&b.purposes?!!b.purposes[String(a)]:!0};var Ar=[J.m.U,J.m.ja,J.m.V,J.m.Ka],Br={},Cr=(Br[J.m.U]=1,Br[J.m.ja]=2,Br);function Dr(a){if(a===void 0)return 0;switch(O(a,J.m.Ga)){case void 0:return 1;case !1:return 3;default:return 2}}function Er(){return(E(183)?fj.xp:fj.yp).indexOf(to())!==-1&&uc.globalPrivacyControl===!0}function Fr(a){if(Er())return!1;var b=Dr(a);if(b===3)return!1;switch(ln(J.m.Ka)){case 1:case 3:return!0;case 2:return!1;case 4:return b===2;case 0:return!0;default:return!1}}
function Gr(){return nn()||!kn(J.m.U)||!kn(J.m.ja)}function Hr(){var a={},b;for(b in Cr)Cr.hasOwnProperty(b)&&(a[Cr[b]]=ln(b));return"G1"+ef(a[1]||0)+ef(a[2]||0)}var Ir={},Jr=(Ir[J.m.U]=0,Ir[J.m.ja]=1,Ir[J.m.V]=2,Ir[J.m.Ka]=3,Ir);function Kr(a){switch(a){case void 0:return 1;case !0:return 3;case !1:return 2;default:return 0}}
function Lr(a){for(var b="1",c=0;c<Ar.length;c++){var d=b,e,f=Ar[c],g=jn.delegatedConsentTypes[f];e=g===void 0?0:Jr.hasOwnProperty(g)?12|Jr[g]:8;var h=bn();h.accessedAny=!0;var m=h.entries[f]||{};e=e<<2|Kr(m.implicit);b=d+(""+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[e]+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[Kr(m.declare)<<4|Kr(m.default)<<2|Kr(m.update)])}var n=b,p=(Er()?1:0)<<3,q=(nn()?1:0)<<2,r=Dr(a);b=n+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[p|
q|r];return b+=""+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[jn.containerScopedDefaults.ad_storage<<4|jn.containerScopedDefaults.analytics_storage<<2|jn.containerScopedDefaults.ad_user_data]+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[(jn.usedContainerScopedDefaults?1:0)<<2|jn.containerScopedDefaults.ad_personalization]}
function Mr(){if(!kn(J.m.V))return"-";for(var a=Object.keys(Mo),b={},c=l(a),d=c.next();!d.done;d=c.next()){var e=d.value;b[e]=jn.corePlatformServices[e]!==!1}for(var f="",g=l(a),h=g.next();!h.done;h=g.next()){var m=h.value;b[m]&&(f+=Mo[m])}(jn.usedCorePlatformServices?jn.selectedAllCorePlatformServices:1)&&(f+="o");return f||"-"}function Nr(){return vo()||(tr()||ur())&&yr()==="1"?"1":"0"}function Or(){return(vo()?!0:!(!tr()&&!ur())&&yr()==="1")||!kn(J.m.V)}
function Pr(){var a="0",b="0",c;var d=qr();c=d.active?d.cmpId:void 0;typeof c==="number"&&c>=0&&c<=4095&&(a="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[c>>6&63],b="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[c&63]);var e="0",f;var g=qr();f=g.active?g.tcfPolicyVersion:void 0;typeof f==="number"&&f>=0&&f<=63&&(e="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[f]);var h=0;vo()&&(h|=1);yr()==="1"&&(h|=2);tr()&&(h|=4);var m;var n=qr();m=n.enableAdvertiserConsentMode!==
void 0?n.enableAdvertiserConsentMode?"1":"0":void 0;m==="1"&&(h|=8);bn().waitPeriodTimedOut&&(h|=16);return"1"+a+b+e+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[h]}function Qr(){return to()==="US-CO"};var Rr;function Sr(){if(xc===null)return 0;var a=ad();if(!a)return 0;var b=a.getEntriesByName(xc,"resource")[0];if(!b)return 0;switch(b.deliveryType){case "":return 1;case "cache":return 2;case "navigational-prefetch":return 3;default:return 0}}var Tr={UA:1,AW:2,DC:3,G:4,GF:5,GT:12,GTM:14,HA:6,MC:7};
function Ur(a){a=a===void 0?{}:a;var b=lg.ctid.split("-")[0].toUpperCase(),c={ctid:lg.ctid,Aj:dk,Ej:ck,gm:Jm.qe?2:1,Eq:a.Em,we:lg.canonicalContainerId};if(E(210)){var d;c.uq=(d=Qm())==null?void 0:d.canonicalContainerId}if(E(204)){var e;c.Po=(e=Rr)!=null?e:Rr=Sr()}c.we!==a.Pa&&(c.Pa=a.Pa);var f=Om();c.rm=f?f.canonicalContainerId:void 0;lk?(c.Uc=Tr[b],c.Uc||(c.Uc=0)):c.Uc=mk?13:10;Xj.C?(c.Bh=0,c.Sl=2):c.Bh=Xj.N?1:3;var g={6:!1};Xj.H===2?g[7]=!0:Xj.H===1&&(g[2]=!0);if(xc){var h=Xk(cl(xc),"host");h&&
(g[8]=h.match(/^(www\.)?googletagmanager\.com$/)===null)}c.Ul=g;return hf(c,a.ph)}
function Vr(){if(!E(192))return Ur();if(E(193))return hf({Aj:dk,Ej:ck});var a=lg.ctid.split("-")[0].toUpperCase(),b={ctid:lg.ctid,Aj:dk,Ej:ck,gm:Jm.qe?2:1,we:lg.canonicalContainerId},c=Om();b.rm=c?c.canonicalContainerId:void 0;lk?(b.Uc=Tr[a],b.Uc||(b.Uc=0)):b.Uc=mk?13:10;Xj.C?(b.Bh=0,b.Sl=2):b.Bh=Xj.N?1:3;var d={6:!1};Xj.H===2?d[7]=!0:Xj.H===1&&(d[2]=!0);if(xc){var e=Xk(cl(xc),"host");e&&(d[8]=e.match(/^(www\.)?googletagmanager\.com$/)===null)}b.Ul=d;return hf(b)};function Wr(a,b,c,d){var e,f=Number(a.Cc!=null?a.Cc:void 0);f!==0&&(e=new Date((b||Ab())+1E3*(f||7776E3)));return{path:a.path,domain:a.domain,flags:a.flags,encode:!!c,expires:e,Ec:d}};var Xr=["ad_storage","ad_user_data"];function Yr(a,b){if(!a)return fb("TAGGING",32),10;if(b===null||b===void 0||b==="")return fb("TAGGING",33),11;var c=Zr(!1);if(c.error!==0)return fb("TAGGING",34),c.error;if(!c.value)return fb("TAGGING",35),2;c.value[a]=b;var d=$r(c);d!==0&&fb("TAGGING",36);return d}
function as(a){if(!a)return fb("TAGGING",27),{error:10};var b=Zr();if(b.error!==0)return fb("TAGGING",29),b;if(!b.value)return fb("TAGGING",30),{error:2};if(!(a in b.value))return fb("TAGGING",31),{value:void 0,error:15};var c=b.value[a];return c===null||c===void 0||c===""?(fb("TAGGING",28),{value:void 0,error:11}):{value:c,error:0}}
function Zr(a){a=a===void 0?!0:a;if(!kn(Xr))return fb("TAGGING",43),{error:3};try{if(!x.localStorage)return fb("TAGGING",44),{error:1}}catch(f){return fb("TAGGING",45),{error:14}}var b={schema:"gcl",version:1},c=void 0;try{c=x.localStorage.getItem("_gcl_ls")}catch(f){return fb("TAGGING",46),{error:13}}try{if(c){var d=JSON.parse(c);if(d&&typeof d==="object")b=d;else return fb("TAGGING",47),{error:12}}}catch(f){return fb("TAGGING",48),{error:8}}if(b.schema!=="gcl")return fb("TAGGING",49),{error:4};
if(b.version!==1)return fb("TAGGING",50),{error:5};try{var e=bs(b);a&&e&&$r({value:b,error:0})}catch(f){return fb("TAGGING",48),{error:8}}return{value:b,error:0}}
function bs(a){if(!a||typeof a!=="object")return!1;if("expires"in a&&"value"in a){var b;typeof a.expires==="number"?b=a.expires:b=typeof a.expires==="string"?Number(a.expires):NaN;if(isNaN(b)||!(Date.now()<=b))return a.value=null,a.error=9,fb("TAGGING",54),!0}else{for(var c=!1,d=l(Object.keys(a)),e=d.next();!e.done;e=d.next())c=bs(a[e.value])||c;return c}return!1}
function $r(a){if(a.error)return a.error;if(!a.value)return fb("TAGGING",42),2;var b=a.value,c;try{c=JSON.stringify(b)}catch(d){return fb("TAGGING",52),6}try{x.localStorage.setItem("_gcl_ls",c)}catch(d){return fb("TAGGING",53),7}return 0};var cs={qj:"value",tb:"conversionCount"},ds={fm:9,ym:10,qj:"timeouts",tb:"timeouts"},es=[cs,ds];function fs(a){if(!gs(a))return{};var b=hs(es),c=b[a.tb];if(c===void 0||c===-1)return b;var d={},e=ma(Object,"assign").call(Object,{},b,(d[a.tb]=c+1,d));return is(e)?e:b}
function hs(a){var b;a:{var c=as("gcl_ctr");if(c.error===0&&c.value&&typeof c.value==="object"){var d=c.value;try{b="value"in d&&typeof d.value==="object"?d.value:void 0;break a}catch(p){}}b=void 0}for(var e=b,f={},g=l(a),h=g.next();!h.done;h=g.next()){var m=h.value;if(e&&gs(m)){var n=e[m.qj];n===void 0||Number.isNaN(n)?f[m.tb]=-1:f[m.tb]=Number(n)}else f[m.tb]=-1}return f}
function js(){var a=fs(cs),b=a[cs.tb];if(b===void 0||b<=0)return"";var c=a[ds.tb];return c===void 0||c<0?b.toString():[b.toString(),c.toString()].join("~")}function is(a,b){b=b||{};for(var c=Ab(),d=Wr(b,c,!0),e={},f=l(es),g=f.next();!g.done;g=f.next()){var h=g.value,m=a[h.tb];m!==void 0&&m!==-1&&(e[h.qj]=m)}e.creationTimeMs=c;return Yr("gcl_ctr",{value:e,expires:Number(d.expires)})===0?!0:!1}function gs(a){return kn(["ad_storage","ad_user_data"])?!a.ym||Ra(a.ym):!1}
function ks(a){return kn(["ad_storage","ad_user_data"])?!a.fm||Ra(a.fm):!1};function ls(a){var b=1,c,d,e;if(a)for(b=0,d=a.length-1;d>=0;d--)e=a.charCodeAt(d),b=(b<<6&268435455)+e+(e<<14),c=b&266338304,b=c!==0?b^c>>21:b;return b};var ms={O:{ro:0,Kj:1,tg:2,Qj:3,Kh:4,Oj:5,Pj:6,Rj:7,Lh:8,Vk:9,Uk:10,wi:11,Wk:12,Wg:13,Zk:14,Mf:15,po:16,ue:17,Oi:18,Pi:19,Qi:20,Jl:21,Ri:22,Nh:23,Yj:24}};ms.O[ms.O.ro]="RESERVED_ZERO";ms.O[ms.O.Kj]="ADS_CONVERSION_HIT";ms.O[ms.O.tg]="CONTAINER_EXECUTE_START";ms.O[ms.O.Qj]="CONTAINER_SETUP_END";ms.O[ms.O.Kh]="CONTAINER_SETUP_START";ms.O[ms.O.Oj]="CONTAINER_BLOCKING_END";ms.O[ms.O.Pj]="CONTAINER_EXECUTE_END";ms.O[ms.O.Rj]="CONTAINER_YIELD_END";ms.O[ms.O.Lh]="CONTAINER_YIELD_START";ms.O[ms.O.Vk]="EVENT_EXECUTE_END";
ms.O[ms.O.Uk]="EVENT_EVALUATION_END";ms.O[ms.O.wi]="EVENT_EVALUATION_START";ms.O[ms.O.Wk]="EVENT_SETUP_END";ms.O[ms.O.Wg]="EVENT_SETUP_START";ms.O[ms.O.Zk]="GA4_CONVERSION_HIT";ms.O[ms.O.Mf]="PAGE_LOAD";ms.O[ms.O.po]="PAGEVIEW";ms.O[ms.O.ue]="SNIPPET_LOAD";ms.O[ms.O.Oi]="TAG_CALLBACK_ERROR";ms.O[ms.O.Pi]="TAG_CALLBACK_FAILURE";ms.O[ms.O.Qi]="TAG_CALLBACK_SUCCESS";ms.O[ms.O.Jl]="TAG_EXECUTE_END";ms.O[ms.O.Ri]="TAG_EXECUTE_START";ms.O[ms.O.Nh]="CUSTOM_PERFORMANCE_START";ms.O[ms.O.Yj]="CUSTOM_PERFORMANCE_END";var ns=[],os={},ps={};var qs=["2"];function rs(a){return a.origin!=="null"};function ss(a,b,c){for(var d={},e=b.split(";"),f=function(r){return Ra(11)?r.trim():r.replace(/^\s*|\s*$/g,"")},g=0;g<e.length;g++){var h=e[g].split("="),m=f(h[0]);if(m&&a(m)){var n=f(h.slice(1).join("="));n&&c&&(n=decodeURIComponent(n));var p=void 0,q=void 0;((p=d)[q=m]||(p[q]=[])).push(n)}}return d};var ts;function us(a,b,c,d){var e;return(e=vs(function(f){return f===a},b,c,d)[a])!=null?e:[]}function vs(a,b,c,d){return ws(d)?ss(a,String(b||xs()),c):{}}function ys(a,b,c,d,e){if(ws(e)){var f=zs(a,d,e);if(f.length===1)return f[0];if(f.length!==0){f=As(f,function(g){return g.ap},b);if(f.length===1)return f[0];f=As(f,function(g){return g.fq},c);return f[0]}}}function Bs(a,b,c,d){var e=xs(),f=window;rs(f)&&(f.document.cookie=a);var g=xs();return e!==g||c!==void 0&&us(b,g,!1,d).indexOf(c)>=0}
function Cs(a,b,c,d){function e(w,y,z){if(z==null)return delete h[y],w;h[y]=z;return w+"; "+y+"="+z}function f(w,y){if(y==null)return w;h[y]=!0;return w+"; "+y}if(!ws(c.Ec))return 2;var g;b==null?g=a+"=deleted; expires="+(new Date(0)).toUTCString():(c.encode&&(b=encodeURIComponent(b)),b=Ds(b),g=a+"="+b);var h={};g=e(g,"path",c.path);var m;c.expires instanceof Date?m=c.expires.toUTCString():c.expires!=null&&(m=""+c.expires);g=e(g,"expires",m);g=e(g,"max-age",c.Yp);g=e(g,"samesite",c.wq);c.secure&&
(g=f(g,"secure"));var n=c.domain;if(n&&n.toLowerCase()==="auto"){for(var p=Es(),q=void 0,r=!1,t=0;t<p.length;++t){var u=p[t]!=="none"?p[t]:void 0,v=e(g,"domain",u);v=f(v,c.flags);try{d&&d(a,h)}catch(w){q=w;continue}r=!0;if(!Fs(u,c.path)&&Bs(v,a,b,c.Ec))return Ra(15)&&(ts=u),0}if(q&&!r)throw q;return 1}n&&n.toLowerCase()!=="none"&&(g=e(g,"domain",n));g=f(g,c.flags);d&&d(a,h);return Fs(n,c.path)?1:Bs(g,a,b,c.Ec)?0:1}
function Gs(a,b,c){c.path==null&&(c.path="/");c.domain||(c.domain="auto");if(ns.includes("2")){var d;(d=ad())==null||d.mark("2-"+ms.O.Nh+"-"+(ps["2"]||0))}var e=Cs(a,b,c);if(ns.includes("2")){var f="2-"+ms.O.Yj+"-"+(ps["2"]||0),g={start:"2-"+ms.O.Nh+"-"+(ps["2"]||0),end:f},h;(h=ad())==null||h.mark(f);var m,n,p=(n=(m=ad())==null?void 0:m.measure(f,g))==null?void 0:n.duration;p!==void 0&&(ps["2"]=(ps["2"]||0)+1,os["2"]=p+(os["2"]||0))}return e}
function As(a,b,c){for(var d=[],e=[],f,g=0;g<a.length;g++){var h=a[g],m=b(h);m===c?d.push(h):f===void 0||m<f?(e=[h],f=m):m===f&&e.push(h)}return d.length>0?d:e}function zs(a,b,c){for(var d=[],e=us(a,void 0,void 0,c),f=0;f<e.length;f++){var g=e[f].split("."),h=g.shift();if(!b||!h||b.indexOf(h)!==-1){var m=g.shift();if(m){var n=m.split("-");d.push({Ro:e[f],So:g.join("."),ap:Number(n[0])||1,fq:Number(n[1])||1})}}}return d}function Ds(a){a&&a.length>1200&&(a=a.substring(0,1200));return a}
var Hs=/^(www\.)?google(\.com?)?(\.[a-z]{2})?$/,Is=/(^|\.)doubleclick\.net$/i;function Fs(a,b){return a!==void 0&&(Is.test(window.document.location.hostname)||b==="/"&&Hs.test(a))}function Js(a){if(!a)return 1;var b=a;Ra(6)&&a==="none"&&(b=window.document.location.hostname);b=b.indexOf(".")===0?b.substring(1):b;return b.split(".").length}function Ks(a){if(!a||a==="/")return 1;a[0]!=="/"&&(a="/"+a);a[a.length-1]!=="/"&&(a+="/");return a.split("/").length-1}
function Ls(a,b){var c=""+Js(a),d=Ks(b);d>1&&(c+="-"+d);return c}
var xs=function(){return rs(window)?window.document.cookie:""},ws=function(a){return a&&Ra(7)?(Array.isArray(a)?a:[a]).every(function(b){return mn(b)&&kn(b)}):!0},Es=function(){var a=ts,b=[];a&&b.push(a);var c=window.document.location.hostname.split(".");if(c.length===4){var d=c[c.length-1];if(Number(d).toString()===d)return["none"]}for(var e=c.length-2;e>=0;e--){var f=c.slice(e).join(".");f!==a&&b.push(f)}var g=window.document.location.hostname;Is.test(g)||Hs.test(g)||b.push("none");return b};function Ms(a){var b=Math.round(Math.random()*2147483647);return a?String(b^ls(a)&2147483647):String(b)}function Ns(a){return[Ms(a),Math.round(Ab()/1E3)].join(".")}function Os(a,b,c,d,e){var f=Js(b),g;return(g=ys(a,f,Ks(c),d,e))==null?void 0:g.So};var Ps;function Qs(){function a(g){c(g.target||g.srcElement||{})}function b(g){d(g.target||g.srcElement||{})}var c=Rs,d=Ss,e=Ts();if(!e.init){Lc(A,"mousedown",a);Lc(A,"keyup",a);Lc(A,"submit",b);var f=HTMLFormElement.prototype.submit;HTMLFormElement.prototype.submit=function(){d(this);f.call(this)};e.init=!0}}function Us(a,b,c,d,e){var f={callback:a,domains:b,fragment:c===2,placement:c,forms:d,sameHost:e};Ts().decorators.push(f)}
function Vs(a,b,c){for(var d=Ts().decorators,e={},f=0;f<d.length;++f){var g=d[f],h;if(h=!c||g.forms)a:{var m=g.domains,n=a,p=!!g.sameHost;if(m&&(p||n!==A.location.hostname))for(var q=0;q<m.length;q++)if(m[q]instanceof RegExp){if(m[q].test(n)){h=!0;break a}}else if(n.indexOf(m[q])>=0||p&&m[q].indexOf(n)>=0){h=!0;break a}h=!1}if(h){var r=g.placement;r===void 0&&(r=g.fragment?2:1);r===b&&Eb(e,g.callback())}}return e}
function Ts(){var a=yc("google_tag_data",{}),b=a.gl;b&&b.decorators||(b={decorators:[]},a.gl=b);return b};var Ws=/(.*?)\*(.*?)\*(.*)/,Xs=/^https?:\/\/([^\/]*?)\.?cdn\.ampproject\.org\/?(.*)/,Ys=/^(?:www\.|m\.|amp\.)+/,Zs=/([^?#]+)(\?[^#]*)?(#.*)?/;function $s(a){var b=Zs.exec(a);if(b)return{wj:b[1],query:b[2],fragment:b[3]}}function at(a){return new RegExp("(.*?)(^|&)"+a+"=([^&]*)&?(.*)")}
function bt(a,b){var c=[uc.userAgent,(new Date).getTimezoneOffset(),uc.userLanguage||uc.language,Math.floor(Ab()/60/1E3)-(b===void 0?0:b),a].join("*"),d;if(!(d=Ps)){for(var e=Array(256),f=0;f<256;f++){for(var g=f,h=0;h<8;h++)g=g&1?g>>>1^3988292384:g>>>1;e[f]=g}d=e}Ps=d;for(var m=4294967295,n=0;n<c.length;n++)m=m>>>8^Ps[(m^c.charCodeAt(n))&255];return((m^-1)>>>0).toString(36)}
function ct(a){return function(b){var c=cl(x.location.href),d=c.search.replace("?",""),e=Uk(d,"_gl",!1,!0)||"";b.query=dt(e)||{};var f=Xk(c,"fragment"),g;var h=-1;if(Gb(f,"_gl="))h=4;else{var m=f.indexOf("&_gl=");m>0&&(h=m+3+2)}if(h<0)g=void 0;else{var n=f.indexOf("&",h);g=n<0?f.substring(h):f.substring(h,n)}b.fragment=dt(g||"")||{};a&&et(c,d,f)}}function ft(a,b){var c=at(a).exec(b),d=b;if(c){var e=c[2],f=c[4];d=c[1];f&&(d=d+e+f)}return d}
function et(a,b,c){function d(g,h){var m=ft("_gl",g);m.length&&(m=h+m);return m}if(tc&&tc.replaceState){var e=at("_gl");if(e.test(b)||e.test(c)){var f=Xk(a,"path");b=d(b,"?");c=d(c,"#");tc.replaceState({},"",""+f+b+c)}}}function gt(a,b){var c=ct(!!b),d=Ts();d.data||(d.data={query:{},fragment:{}},c(d.data));var e={},f=d.data;f&&(Eb(e,f.query),a&&Eb(e,f.fragment));return e}
var dt=function(a){try{var b=ht(a,3);if(b!==void 0){for(var c={},d=b?b.split("*"):[],e=0;e+1<d.length;e+=2){var f=d[e],g=db(d[e+1]);c[f]=g}fb("TAGGING",6);return c}}catch(h){fb("TAGGING",8)}};function ht(a,b){if(a){var c;a:{for(var d=a,e=0;e<3;++e){var f=Ws.exec(d);if(f){c=f;break a}d=Wk(d)||""}c=void 0}var g=c;if(g&&g[1]==="1"){var h=g[3],m;a:{for(var n=g[2],p=0;p<b;++p)if(n===bt(h,p)){m=!0;break a}m=!1}if(m)return h;fb("TAGGING",7)}}}
function it(a,b,c,d,e){function f(p){p=ft(a,p);var q=p.charAt(p.length-1);p&&q!=="&"&&(p+="&");return p+n}d=d===void 0?!1:d;e=e===void 0?!1:e;var g=$s(c);if(!g)return"";var h=g.query||"",m=g.fragment||"",n=a+"="+b;d?m.substring(1).length!==0&&e||(m="#"+f(m.substring(1))):h="?"+f(h.substring(1));return""+g.wj+h+m}
function jt(a,b){function c(n,p,q){var r;a:{for(var t in n)if(n.hasOwnProperty(t)){r=!0;break a}r=!1}if(r){var u,v=[],w;for(w in n)if(n.hasOwnProperty(w)){var y=n[w];y!==void 0&&y===y&&y!==null&&y.toString()!=="[object Object]"&&(v.push(w),v.push(cb(String(y))))}var z=v.join("*");u=["1",bt(z),z].join("*");d?(Ra(3)||Ra(1)||!p)&&kt("_gl",u,a,p,q):lt("_gl",u,a,p,q)}}var d=(a.tagName||"").toUpperCase()==="FORM",e=Vs(b,1,d),f=Vs(b,2,d),g=Vs(b,4,d),h=Vs(b,3,d);c(e,!1,!1);c(f,!0,!1);Ra(1)&&c(g,!0,!0);for(var m in h)h.hasOwnProperty(m)&&
mt(m,h[m],a)}function mt(a,b,c){c.tagName.toLowerCase()==="a"?lt(a,b,c):c.tagName.toLowerCase()==="form"&&kt(a,b,c)}function lt(a,b,c,d,e){d=d===void 0?!1:d;e=e===void 0?!1:e;var f;if(f=c.href){var g;if(!(g=!Ra(4)||d)){var h=x.location.href,m=$s(c.href),n=$s(h);g=!(m&&n&&m.wj===n.wj&&m.query===n.query&&m.fragment)}f=g}if(f){var p=it(a,b,c.href,d,e);jc.test(p)&&(c.href=p)}}
function kt(a,b,c,d,e){d=d===void 0?!1:d;e=e===void 0?!1:e;if(c){var f=c.getAttribute("action")||"";if(f){var g=(c.method||"").toLowerCase();if(g!=="get"||d){if(g==="get"||g==="post"){var h=it(a,b,f,d,e);jc.test(h)&&(c.action=h)}}else{for(var m=c.childNodes||[],n=!1,p=0;p<m.length;p++){var q=m[p];if(q.name===a){q.setAttribute("value",b);n=!0;break}}if(!n){var r=A.createElement("input");r.setAttribute("type","hidden");r.setAttribute("name",a);r.setAttribute("value",b);c.appendChild(r)}}}}}
function Rs(a){try{var b;a:{for(var c=a,d=100;c&&d>0;){if(c.href&&c.nodeName.match(/^a(?:rea)?$/i)){b=c;break a}c=c.parentNode;d--}b=null}var e=b;if(e){var f=e.protocol;f!=="http:"&&f!=="https:"||jt(e,e.hostname)}}catch(g){}}function Ss(a){try{var b=a.getAttribute("action");if(b){var c=Xk(cl(b),"host");jt(a,c)}}catch(d){}}function nt(a,b,c,d){Qs();var e=c==="fragment"?2:1;d=!!d;Us(a,b,e,d,!1);e===2&&fb("TAGGING",23);d&&fb("TAGGING",24)}
function ot(a,b){Qs();Us(a,[Zk(x.location,"host",!0)],b,!0,!0)}function pt(){var a=A.location.hostname,b=Xs.exec(A.referrer);if(!b)return!1;var c=b[2],d=b[1],e="";if(c){var f=c.split("/"),g=f[1];e=g==="s"?Wk(f[2])||"":Wk(g)||""}else if(d){if(d.indexOf("xn--")===0)return!1;e=d.replace(/-/g,".").replace(/\.\./g,"-")}var h=a.replace(Ys,""),m=e.replace(Ys,""),n;if(!(n=h===m)){var p="."+m;n=h.length>=p.length&&h.substring(h.length-p.length,h.length)===p}return n}
function qt(a,b){return a===!1?!1:a||b||pt()};var rt=["1"],st={},tt={};function ut(a,b){b=b===void 0?!0:b;var c=vt(a.prefix);if(st[c])wt(a);else if(xt(c,a.path,a.domain)){var d=tt[vt(a.prefix)]||{id:void 0,Ah:void 0};b&&zt(a,d.id,d.Ah);wt(a)}else{var e=el("auiddc");if(e)fb("TAGGING",17),st[c]=e;else if(b){var f=vt(a.prefix),g=Ns();At(f,g,a);xt(c,a.path,a.domain);wt(a,!0)}}}
function wt(a,b){if((b===void 0?0:b)&&gs(cs)){var c=Zr(!1);c.error!==0?fb("TAGGING",38):c.value?"gcl_ctr"in c.value?(delete c.value.gcl_ctr,$r(c)!==0&&fb("TAGGING",41)):fb("TAGGING",40):fb("TAGGING",39)}if(ks(cs)&&hs([cs])[cs.tb]===-1){for(var d={},e=(d[cs.tb]=0,d),f=l(es),g=f.next();!g.done;g=f.next()){var h=g.value;h!==cs&&ks(h)&&(e[h.tb]=0)}is(e,a)}}
function zt(a,b,c){var d=vt(a.prefix),e=st[d];if(e){var f=e.split(".");if(f.length===2){var g=Number(f[1])||0;if(g){var h=e;b&&(h=e+"."+b+"."+(c?c:Math.floor(Ab()/1E3)));At(d,h,a,g*1E3)}}}}function At(a,b,c,d){var e;e=["1",Ls(c.domain,c.path),b].join(".");var f=Wr(c,d);f.Ec=Bt();Gs(a,e,f)}function xt(a,b,c){var d=Os(a,b,c,rt,Bt());if(!d)return!1;Ct(a,d);return!0}
function Ct(a,b){var c=b.split(".");c.length===5?(st[a]=c.slice(0,2).join("."),tt[a]={id:c.slice(2,4).join("."),Ah:Number(c[4])||0}):c.length===3?tt[a]={id:c.slice(0,2).join("."),Ah:Number(c[2])||0}:st[a]=b}function vt(a){return(a||"_gcl")+"_au"}function Dt(a){function b(){kn(c)&&a()}var c=Bt();qn(function(){b();kn(c)||rn(b,c)},c)}
function Et(a){var b=gt(!0),c=vt(a.prefix);Dt(function(){var d=b[c];if(d){Ct(c,d);var e=Number(st[c].split(".")[1])*1E3;if(e){fb("TAGGING",16);var f=Wr(a,e);f.Ec=Bt();var g=["1",Ls(a.domain,a.path),d].join(".");Gs(c,g,f)}}})}function Ft(a,b,c,d,e){e=e||{};var f=function(){var g={},h=Os(a,e.path,e.domain,rt,Bt());h&&(g[a]=h);return g};Dt(function(){nt(f,b,c,d)})}function Bt(){return["ad_storage","ad_user_data"]};function Gt(a){for(var b=[],c=A.cookie.split(";"),d=new RegExp("^\\s*"+(a||"_gac")+"_(UA-\\d+-\\d+)=\\s*(.+?)\\s*$"),e=0;e<c.length;e++){var f=c[e].match(d);f&&b.push({Hj:f[1],value:f[2],timestamp:Number(f[2].split(".")[1])||0})}b.sort(function(g,h){return h.timestamp-g.timestamp});return b}
function Ht(a,b){var c=Gt(a),d={};if(!c||!c.length)return d;for(var e=0;e<c.length;e++){var f=c[e].value.split(".");if(!(f[0]!=="1"||b&&f.length<3||!b&&f.length!==3)&&Number(f[1])){d[c[e].Hj]||(d[c[e].Hj]=[]);var g={version:f[0],timestamp:Number(f[1])*1E3,gclid:f[2]};b&&f.length>3&&(g.labels=f.slice(3));d[c[e].Hj].push(g)}}return d};var It={},Jt=(It.k={ba:/^[\w-]+$/},It.b={ba:/^[\w-]+$/,Bj:!0},It.i={ba:/^[1-9]\d*$/},It.h={ba:/^\d+$/},It.t={ba:/^[1-9]\d*$/},It.d={ba:/^[A-Za-z0-9_-]+$/},It.j={ba:/^\d+$/},It.u={ba:/^[1-9]\d*$/},It.l={ba:/^[01]$/},It.o={ba:/^[1-9]\d*$/},It.g={ba:/^[01]$/},It.s={ba:/^.+$/},It);var Kt={},Ot=(Kt[5]={Hh:{2:Lt},pj:"2",qh:["k","i","b","u"]},Kt[4]={Hh:{2:Lt,GCL:Mt},pj:"2",qh:["k","i","b"]},Kt[2]={Hh:{GS2:Lt,GS1:Nt},pj:"GS2",qh:"sogtjlhd".split("")},Kt);function Pt(a,b,c){var d=Ot[b];if(d){var e=a.split(".")[0];c==null||c(e);if(e){var f=d.Hh[e];if(f)return f(a,b)}}}
function Lt(a,b){var c=a.split(".");if(c.length===3){var d=c[2];if(d.indexOf("$")===-1&&d.indexOf("%24")!==-1)try{d=decodeURIComponent(d)}catch(t){}var e={},f=Ot[b];if(f){for(var g=f.qh,h=l(d.split("$")),m=h.next();!m.done;m=h.next()){var n=m.value,p=n[0];if(g.indexOf(p)!==-1)try{var q=decodeURIComponent(n.substring(1)),r=Jt[p];r&&(r.Bj?(e[p]=e[p]||[],e[p].push(q)):e[p]=q)}catch(t){}}return e}}}function Qt(a,b,c){var d=Ot[b];if(d)return[d.pj,c||"1",Rt(a,b)].join(".")}
function Rt(a,b){var c=Ot[b];if(c){for(var d=[],e=l(c.qh),f=e.next();!f.done;f=e.next()){var g=f.value,h=Jt[g];if(h){var m=a[g];if(m!==void 0)if(h.Bj&&Array.isArray(m))for(var n=l(m),p=n.next();!p.done;p=n.next())d.push(encodeURIComponent(""+g+p.value));else d.push(encodeURIComponent(""+g+m))}}return d.join("$")}}function Mt(a){var b=a.split(".");b.shift();var c=b.shift(),d=b.shift(),e={};return e.k=d,e.i=c,e.b=b,e}
function Nt(a){var b=a.split(".").slice(2);if(!(b.length<5||b.length>7)){var c={};return c.s=b[0],c.o=b[1],c.g=b[2],c.t=b[3],c.j=b[4],c.l=b[5],c.h=b[6],c}};var St=new Map([[5,"ad_storage"],[4,["ad_storage","ad_user_data"]],[2,"analytics_storage"]]);function Tt(a,b,c){if(Ot[b]){for(var d=[],e=us(a,void 0,void 0,St.get(b)),f=l(e),g=f.next();!g.done;g=f.next()){var h=Pt(g.value,b,c);h&&d.push(Ut(h))}return d}}function Vt(a,b,c,d,e){d=d||{};var f=Ls(d.domain,d.path),g=Qt(b,c,f);if(!g)return 1;var h=Wr(d,e,void 0,St.get(c));return Gs(a,g,h)}function Wt(a,b){var c=b.ba;return typeof c==="function"?c(a):c.test(a)}
function Ut(a){for(var b=l(Object.keys(a)),c=b.next(),d={};!c.done;d={Uf:void 0},c=b.next()){var e=c.value,f=a[e];d.Uf=Jt[e];d.Uf?d.Uf.Bj?a[e]=Array.isArray(f)?f.filter(function(g){return function(h){return Wt(h,g.Uf)}}(d)):void 0:typeof f==="string"&&Wt(f,d.Uf)||(a[e]=void 0):a[e]=void 0}return a};var Xt=function(){this.value=0};Xt.prototype.set=function(a){return this.value|=1<<a};var Yt=function(a,b){b<=0||(a.value|=1<<b-1)};Xt.prototype.get=function(){return this.value};Xt.prototype.clear=function(a){this.value&=~(1<<a)};Xt.prototype.clearAll=function(){this.value=0};Xt.prototype.equals=function(a){return this.value===a.value};function Zt(a){if(a)try{return new Uint8Array(atob(a.replace(/-/g,"+").replace(/_/g,"/")).split("").map(function(b){return b.charCodeAt(0)}))}catch(b){}}function $t(a,b){var c=0,d=0,e,f=b;do{if(f>=a.length)return;e=a[f++];c|=(e&127)<<d;d+=7}while(e&128);return[c,f]};function au(){var a=String,b=x.location.hostname,c=x.location.pathname,d=b=Ob(b);d.split(".").length>2&&(d=d.replace(/^(www[0-9]*|web|ftp|wap|home|m|w|amp|mobile)\./,""));b=d;c=Ob(c);var e=c.split(";")[0];e=e.replace(/\/(ar|slp|web|index)?\/?$/,"");return a(ls((""+b+e).toLowerCase()))};var bu={},cu=(bu.gclid=!0,bu.dclid=!0,bu.gbraid=!0,bu.wbraid=!0,bu),du=/^\w+$/,eu=/^[\w-]+$/,fu={},gu=(fu.aw="_aw",fu.dc="_dc",fu.gf="_gf",fu.gp="_gp",fu.gs="_gs",fu.ha="_ha",fu.ag="_ag",fu.gb="_gb",fu),hu=/^(?:www\.)?google(?:\.com?)?(?:\.[a-z]{2}t?)?$/,iu=/^www\.googleadservices\.com$/;function ju(){return["ad_storage","ad_user_data"]}function ku(a){return!Ra(7)||kn(a)}function lu(a,b){function c(){var d=ku(b);d&&a();return d}qn(function(){c()||rn(c,b)},b)}
function mu(a){return nu(a).map(function(b){return b.gclid})}function ou(a){return pu(a).filter(function(b){return b.gclid}).map(function(b){return b.gclid})}function pu(a){var b=qu(a.prefix),c=ru("gb",b),d=ru("ag",b);if(!d||!c)return[];var e=function(h){return function(m){m.type=h;return m}},f=nu(c).map(e("gb")),g=su(d).map(e("ag"));return f.concat(g).sort(function(h,m){return m.timestamp-h.timestamp})}
function tu(a,b,c,d,e,f){var g=pb(a,function(h){return h.gclid===c});g?(g.timestamp<d&&(g.timestamp=d,g.Id=f),g.labels=uu(g.labels||[],e||[])):a.push({version:b,gclid:c,timestamp:d,labels:e,Id:f})}function su(a){for(var b=Tt(a,5)||[],c=[],d=l(b),e=d.next();!e.done;e=d.next()){var f=e.value,g=f,h=vu(f);h&&tu(c,"2",g.k,h,g.b||[],f.u)}return c.sort(function(m,n){return n.timestamp-m.timestamp})}
function nu(a){for(var b=[],c=us(a,A.cookie,void 0,ju()),d=l(c),e=d.next();!e.done;e=d.next()){var f=wu(e.value);if(f!=null){var g=f;tu(b,g.version,g.gclid,g.timestamp,g.labels)}}b.sort(function(h,m){return m.timestamp-h.timestamp});return xu(b)}function yu(a,b){for(var c=[],d=l(a),e=d.next();!e.done;e=d.next()){var f=e.value;c.includes(f)||c.push(f)}for(var g=l(b),h=g.next();!h.done;h=g.next()){var m=h.value;c.includes(m)||c.push(m)}return c}
function zu(a,b,c){c=c===void 0?!1:c;for(var d,e,f=l(a),g=f.next();!g.done;g=f.next()){var h=g.value;if(h.gclid===b.gclid){d=h;break}h.Ja&&b.Ja&&h.Ja.equals(b.Ja)&&(e=h)}if(d){var m,n,p=(m=d.Ja)!=null?m:new Xt,q=(n=b.Ja)!=null?n:new Xt;p.value|=q.value;d.Ja=p;d.timestamp<b.timestamp&&(d.timestamp=b.timestamp,d.Id=b.Id);d.labels=yu(d.labels||[],b.labels||[]);d.Eb=yu(d.Eb||[],b.Eb||[])}else c&&e?ma(Object,"assign").call(Object,e,b):a.push(b)}
function Au(a){if(!a)return new Xt;var b=new Xt;if(a===1)return Yt(b,2),Yt(b,3),b;Yt(b,a);return b}
function Bu(){var a=as("gclid");if(!a||a.error||!a.value||typeof a.value!=="object")return null;var b=a.value;try{if(!("value"in b&&b.value)||typeof b.value!=="object")return null;var c=b.value,d=c.value;if(!d||!d.match(eu))return null;var e=c.linkDecorationSource,f=c.linkDecorationSources,g=new Xt;typeof e==="number"?g=Au(e):typeof f==="number"&&(g.value=f);return{version:"",gclid:d,timestamp:Number(c.creationTimeMs)||0,labels:[],Ja:g,Eb:[2]}}catch(h){return null}}
function Cu(){var a=as("gcl_aw");if(a.error!==0)return null;try{return a.value.reduce(function(b,c){if(!c.value||typeof c.value!=="object")return b;var d=c.value,e=d.value;if(!e||!e.match(eu))return b;var f=new Xt,g=d.linkDecorationSources;typeof g==="number"&&(f.value=g);b.push({version:"",gclid:e,timestamp:Number(d.creationTimeMs)||0,expires:Number(c.expires)||0,labels:[],Ja:f,Eb:[2]});return b},[])}catch(b){return null}}
function Du(a){for(var b=[],c=us(a,A.cookie,void 0,ju()),d=l(c),e=d.next();!e.done;e=d.next()){var f=wu(e.value);f!=null&&(f.Id=void 0,f.Ja=new Xt,f.Eb=[1],zu(b,f))}var g=Bu();g&&(g.Id=void 0,g.Eb=g.Eb||[2],zu(b,g));if(Ra(13)){var h=Cu();if(h)for(var m=l(h),n=m.next();!n.done;n=m.next()){var p=n.value;p.Id=void 0;p.Eb=p.Eb||[2];zu(b,p)}}b.sort(function(q,r){return r.timestamp-q.timestamp});return xu(b)}
function uu(a,b){if(!a.length)return b;if(!b.length)return a;var c={};return a.concat(b).filter(function(d){return c.hasOwnProperty(d)?!1:c[d]=!0})}function qu(a){return a&&typeof a==="string"&&a.match(du)?a:"_gcl"}function Eu(a,b){if(a){var c={value:a,Ja:new Xt};Yt(c.Ja,b);return c}}
function Fu(a,b,c){var d=cl(a),e=Xk(d,"query",!1,void 0,"gclsrc"),f=Eu(Xk(d,"query",!1,void 0,"gclid"),c?4:2);if(b&&(!f||!e)){var g=d.hash.replace("#","");f||(f=Eu(Uk(g,"gclid",!1),3));e||(e=Uk(g,"gclsrc",!1))}return!f||e!==void 0&&e!=="aw"&&e!=="aw.ds"?[]:[f]}
function Gu(a,b){var c=cl(a),d=Xk(c,"query",!1,void 0,"gclid"),e=Xk(c,"query",!1,void 0,"gclsrc"),f=Xk(c,"query",!1,void 0,"wbraid");f=Mb(f);var g=Xk(c,"query",!1,void 0,"gbraid"),h=Xk(c,"query",!1,void 0,"gad_source"),m=Xk(c,"query",!1,void 0,"dclid");if(b&&!(d&&e&&f&&g)){var n=c.hash.replace("#","");d=d||Uk(n,"gclid",!1);e=e||Uk(n,"gclsrc",!1);f=f||Uk(n,"wbraid",!1);g=g||Uk(n,"gbraid",!1);h=h||Uk(n,"gad_source",!1)}return Hu(d,e,m,f,g,h)}function Iu(){return Gu(x.location.href,!0)}
function Hu(a,b,c,d,e,f){var g={},h=function(m,n){g[n]||(g[n]=[]);g[n].push(m)};g.gclid=a;g.gclsrc=b;g.dclid=c;if(a!==void 0&&a.match(eu))switch(b){case void 0:h(a,"aw");break;case "aw.ds":h(a,"aw");h(a,"dc");break;case "ds":h(a,"dc");break;case "3p.ds":h(a,"dc");break;case "gf":h(a,"gf");break;case "ha":h(a,"ha")}c&&h(c,"dc");d!==void 0&&eu.test(d)&&(g.wbraid=d,h(d,"gb"));e!==void 0&&eu.test(e)&&(g.gbraid=e,h(e,"ag"));f!==void 0&&eu.test(f)&&(g.gad_source=f,h(f,"gs"));return g}
function Ju(a){for(var b=Iu(),c=!0,d=l(Object.keys(b)),e=d.next();!e.done;e=d.next())if(b[e.value]!==void 0){c=!1;break}c&&(b=Gu(x.document.referrer,!1),b.gad_source=void 0);Ku(b,!1,a)}
function Lu(a){Ju(a);var b=Fu(x.location.href,!0,!1);b.length||(b=Fu(x.document.referrer,!1,!0));a=a||{};Mu(a);if(b.length){var c=b[0],d=Ab(),e=Wr(a,d,!0),f=ju(),g=function(){ku(f)&&e.expires!==void 0&&Yr("gclid",{value:{value:c.value,creationTimeMs:d,linkDecorationSources:c.Ja.get()},expires:Number(e.expires)})};qn(function(){g();ku(f)||rn(g,f)},f)}}
function Mu(a){var b;if(b=Ra(14)){var c=Nu();b=hu.test(c)||iu.test(c)||Ou()}if(b){var d;a:{for(var e=cl(x.location.href),f=Vk(Xk(e,"query")),g=l(Object.keys(f)),h=g.next();!h.done;h=g.next()){var m=h.value;if(!cu[m]){var n=f[m][0]||"",p;if(!n||n.length<50||n.length>200)p=!1;else{var q=Zt(n),r;if(q)c:{var t=q;if(t&&t.length!==0){var u=0;try{for(;u<t.length;){var v=$t(t,u);if(v===void 0)break;var w=l(v),y=w.next().value,z=w.next().value,C=y,D=z,G=C&7;if(C>>3===16382){if(G!==0)break;var I=$t(t,D);if(I===
void 0)break;r=l(I).next().value===1;break c}var M;d:{var T=void 0,da=t,N=D;switch(G){case 0:M=(T=$t(da,N))==null?void 0:T[1];break d;case 1:M=N+8;break d;case 2:var W=$t(da,N);if(W===void 0)break;var ia=l(W),ka=ia.next().value;M=ia.next().value+ka;break d;case 5:M=N+4;break d}M=void 0}if(M===void 0||M>t.length)break;u=M}}catch(X){}}r=!1}else r=!1;p=r}if(p){d=n;break a}}}d=void 0}var Y=d;Y&&Pu(Y,7,a)}}
function Pu(a,b,c){c=c||{};var d=Ab(),e=Wr(c,d,!0),f=ju(),g=function(){if(ku(f)&&e.expires!==void 0){var h=Cu()||[];zu(h,{version:"",gclid:a,timestamp:d,expires:Number(e.expires),Ja:Au(b)},!0);Yr("gcl_aw",h.map(function(m){return{value:{value:m.gclid,creationTimeMs:m.timestamp,linkDecorationSources:m.Ja?m.Ja.get():0},expires:Number(m.expires)}}))}};qn(function(){ku(f)?g():rn(g,f)},f)}
function Ku(a,b,c,d,e){c=c||{};e=e||[];var f=qu(c.prefix),g=d||Ab(),h=Math.round(g/1E3),m=ju(),n=!1,p=!1,q=function(){if(ku(m)){var r=Wr(c,g,!0);r.Ec=m;for(var t=function(T,da){var N=ru(T,f);N&&(Gs(N,da,r),T!=="gb"&&(n=!0))},u=function(T){var da=["GCL",h,T];e.length>0&&da.push(e.join("."));return da.join(".")},v=l(["aw","dc","gf","ha","gp"]),w=v.next();!w.done;w=v.next()){var y=w.value;a[y]&&t(y,u(a[y][0]))}if(!n&&a.gb){var z=a.gb[0],C=ru("gb",f);!b&&nu(C).some(function(T){return T.gclid===z&&T.labels&&
T.labels.length>0})||t("gb",u(z))}}if(!p&&a.gbraid&&ku("ad_storage")&&(p=!0,!n)){var D=a.gbraid,G=ru("ag",f);if(b||!su(G).some(function(T){return T.gclid===D&&T.labels&&T.labels.length>0})){var I={},M=(I.k=D,I.i=""+h,I.b=e,I);Vt(G,M,5,c,g)}}Qu(a,f,g,c)};qn(function(){q();ku(m)||rn(q,m)},m)}
function Qu(a,b,c,d){if(a.gad_source!==void 0&&ku("ad_storage")){var e=$c();if(e!=="r"&&e!=="h"){var f=a.gad_source,g=ru("gs",b);if(g){var h=Math.floor((Ab()-(Zc()||0))/1E3),m,n=au(),p={};m=(p.k=f,p.i=""+h,p.u=n,p);Vt(g,m,5,d,c)}}}}
function Ru(a,b){var c=gt(!0);lu(function(){for(var d=qu(b.prefix),e=0;e<a.length;++e){var f=a[e];if(gu[f]!==void 0){var g=ru(f,d),h=c[g];if(h){var m=Math.min(Su(h),Ab()),n;b:{for(var p=m,q=us(g,A.cookie,void 0,ju()),r=0;r<q.length;++r)if(Su(q[r])>p){n=!0;break b}n=!1}if(!n){var t=Wr(b,m,!0);t.Ec=ju();Gs(g,h,t)}}}}Ku(Hu(c.gclid,c.gclsrc),!1,b)},ju())}
function Tu(a){var b=["ag"],c=gt(!0),d=qu(a.prefix);lu(function(){for(var e=0;e<b.length;++e){var f=ru(b[e],d);if(f){var g=c[f];if(g){var h=Pt(g,5);if(h){var m=vu(h);m||(m=Ab());var n;a:{for(var p=m,q=Tt(f,5),r=0;r<q.length;++r)if(vu(q[r])>p){n=!0;break a}n=!1}if(n)break;h.i=""+Math.round(m/1E3);Vt(f,h,5,a,m)}}}}},["ad_storage"])}function ru(a,b){var c=gu[a];if(c!==void 0)return b+c}function Su(a){return Uu(a.split(".")).length!==0?(Number(a.split(".")[1])||0)*1E3:0}
function vu(a){return a?(Number(a.i)||0)*1E3:0}function wu(a){var b=Uu(a.split("."));return b.length===0?null:{version:b[0],gclid:b[2],timestamp:(Number(b[1])||0)*1E3,labels:b.slice(3)}}function Uu(a){return a.length<3||a[0]!=="GCL"&&a[0]!=="1"||!/^\d+$/.test(a[1])||!eu.test(a[2])?[]:a}
function Vu(a,b,c,d,e){if(Array.isArray(b)&&rs(x)){var f=qu(e),g=function(){for(var h={},m=0;m<a.length;++m){var n=ru(a[m],f);if(n){var p=us(n,A.cookie,void 0,ju());p.length&&(h[n]=p.sort()[p.length-1])}}return h};lu(function(){nt(g,b,c,d)},ju())}}
function Wu(a,b,c,d){if(Array.isArray(a)&&rs(x)){var e=["ag"],f=qu(d),g=function(){for(var h={},m=0;m<e.length;++m){var n=ru(e[m],f);if(!n)return{};var p=Tt(n,5);if(p.length){var q=p.sort(function(r,t){return vu(t)-vu(r)})[0];h[n]=Qt(q,5)}}return h};lu(function(){nt(g,a,b,c)},["ad_storage"])}}function xu(a){return a.filter(function(b){return eu.test(b.gclid)})}
function Xu(a,b){if(rs(x)){for(var c=qu(b.prefix),d={},e=0;e<a.length;e++)gu[a[e]]&&(d[a[e]]=gu[a[e]]);lu(function(){tb(d,function(f,g){var h=us(c+g,A.cookie,void 0,ju());h.sort(function(t,u){return Su(u)-Su(t)});if(h.length){var m=h[0],n=Su(m),p=Uu(m.split(".")).length!==0?m.split(".").slice(3):[],q={},r;r=Uu(m.split(".")).length!==0?m.split(".")[2]:void 0;q[f]=[r];Ku(q,!0,b,n,p)}})},ju())}}
function Yu(a){var b=["ag"],c=["gbraid"];lu(function(){for(var d=qu(a.prefix),e=0;e<b.length;++e){var f=ru(b[e],d);if(!f)break;var g=Tt(f,5);if(g.length){var h=g.sort(function(q,r){return vu(r)-vu(q)})[0],m=vu(h),n=h.b,p={};p[c[e]]=h.k;Ku(p,!0,a,m,n)}}},["ad_storage"])}function Zu(a,b){for(var c=0;c<b.length;++c)if(a[b[c]])return!0;return!1}
function $u(a){function b(h,m,n){n&&(h[m]=n)}if(nn()){var c=Iu(),d;a.includes("gad_source")&&(d=c.gad_source!==void 0?c.gad_source:gt(!1)._gs);if(Zu(c,a)||d){var e={};b(e,"gclid",c.gclid);b(e,"dclid",c.dclid);b(e,"gclsrc",c.gclsrc);b(e,"wbraid",c.wbraid);b(e,"gbraid",c.gbraid);ot(function(){return e},3);var f={},g=(f._up="1",f);b(g,"_gs",d);ot(function(){return g},1)}}}function Ou(){var a=cl(x.location.href);return Xk(a,"query",!1,void 0,"gad_source")}
function av(a){if(!Ra(1))return null;var b=gt(!0).gad_source;if(b!=null)return x.location.hash="",b;if(Ra(2)){b=Ou();if(b!=null)return b;var c=Iu();if(Zu(c,a))return"0"}return null}function bv(a){var b=av(a);b!=null&&ot(function(){var c={};return c.gad_source=b,c},4)}function cv(a,b,c){var d=[];if(b.length===0)return d;for(var e={},f=0;f<b.length;f++){var g=b[f],h=g.type?g.type:"gcl";(g.labels||[]).indexOf(c)===-1?(a.push(0),e[h]||d.push(g)):a.push(1);e[h]=!0}return d}
function dv(a,b,c,d){var e=[];c=c||{};if(!ku(ju()))return e;var f=nu(a),g=cv(e,f,b);if(g.length&&!d)for(var h=l(g),m=h.next();!m.done;m=h.next()){var n=m.value,p=n.timestamp,q=[n.version,Math.round(p/1E3),n.gclid].concat(n.labels||[],[b]).join("."),r=Wr(c,p,!0);r.Ec=ju();Gs(a,q,r)}return e}
function ev(a,b){var c=[];b=b||{};var d=pu(b),e=cv(c,d,a);if(e.length)for(var f=l(e),g=f.next();!g.done;g=f.next()){var h=g.value,m=qu(b.prefix),n=ru(h.type,m);if(!n)break;var p=h,q=p.version,r=p.gclid,t=p.labels,u=p.timestamp,v=Math.round(u/1E3);if(h.type==="ag"){var w={},y=(w.k=r,w.i=""+v,w.b=(t||[]).concat([a]),w);Vt(n,y,5,b,u)}else if(h.type==="gb"){var z=[q,v,r].concat(t||[],[a]).join("."),C=Wr(b,u,!0);C.Ec=ju();Gs(n,z,C)}}return c}
function fv(a,b){var c=qu(b),d=ru(a,c);if(!d)return 0;var e;e=a==="ag"?su(d):nu(d);for(var f=0,g=0;g<e.length;g++)f=Math.max(f,e[g].timestamp);return f}function gv(a){for(var b=0,c=l(Object.keys(a)),d=c.next();!d.done;d=c.next())for(var e=a[d.value],f=0;f<e.length;f++)b=Math.max(b,Number(e[f].timestamp));return b}function hv(a){var b=Math.max(fv("aw",a),gv(ku(ju())?Ht():{})),c=Math.max(fv("gb",a),gv(ku(ju())?Ht("_gac_gb",!0):{}));c=Math.max(c,fv("ag",a));return c>b}
function Nu(){return A.referrer?Xk(cl(A.referrer),"host"):""};
var iv=function(a,b){b=b===void 0?!1:b;var c=Ep("ads_pageview",function(){return{}});if(c[a])return!1;b||(c[a]=!0);return!0},jv=function(a){return dl(a,"gclid dclid gbraid wbraid gclaw gcldc gclha gclgf gclgb _gl".split(" "),"0")},qv=function(a,b,c,d,e){var f=qu(a.prefix);if(iv(f,!0)){var g=Iu(),h=[],m=g.gclid,n=g.dclid,p=g.gclsrc||"aw",q=kv(),r=q.Yf,t=q.am;!m||p!=="aw.ds"&&p!=="aw"&&p!=="ds"&&p!=="3p.ds"||h.push({gclid:m,Ed:p});n&&h.push({gclid:n,Ed:"ds"});h.length===2&&L(147);h.length===0&&g.wbraid&&
h.push({gclid:g.wbraid,Ed:"gb"});h.length===0&&p==="aw.ds"&&h.push({gclid:"",Ed:"aw.ds"});lv(function(){var u=P(mv());if(u){ut(a);var v=[],w=u?st[vt(a.prefix)]:void 0;w&&v.push("auid="+w);if(P(J.m.V)){e&&v.push("userId="+e);var y=Gn(Cn.Z.Dl);if(y===void 0)Fn(Cn.Z.El,!0);else{var z=Gn(Cn.Z.kh);v.push("ga_uid="+z+"."+y)}}var C=Nu(),D=u||!d?h:[];D.length===0&&(hu.test(C)||iu.test(C))&&D.push({gclid:"",Ed:""});if(D.length!==0||r!==void 0){C&&v.push("ref="+encodeURIComponent(C));var G=nv();v.push("url="+
encodeURIComponent(G));v.push("tft="+Ab());var I=Zc();I!==void 0&&v.push("tfd="+Math.round(I));var M=Sl(!0);v.push("frm="+M);r!==void 0&&v.push("gad_source="+encodeURIComponent(r));t!==void 0&&v.push("gad_source_src="+encodeURIComponent(t.toString()));if(!c){var T={};c=rq(hq(new gq(0),(T[J.m.Ga]=Oq.C[J.m.Ga],T)))}v.push("gtm="+Ur({Pa:b}));Gr()&&v.push("gcs="+Hr());v.push("gcd="+Lr(c));Or()&&v.push("dma_cps="+Mr());v.push("dma="+Nr());Fr(c)?v.push("npa=0"):v.push("npa=1");Qr()&&v.push("_ng=1");jr(rr())&&
v.push("tcfd="+Pr());var da=yr();da&&v.push("gdpr="+da);var N=xr();N&&v.push("gdpr_consent="+N);E(23)&&v.push("apve=0");E(123)&&gt(!1)._up&&v.push("gtm_up=1");uk()&&v.push("tag_exp="+uk());if(D.length>0)for(var W=0;W<D.length;W++){var ia=D[W],ka=ia.gclid,Y=ia.Ed;if(!ov(a.prefix,Y+"."+ka,w!==void 0)){var X=pv+"?"+v.join("&");ka!==""?X=Y==="gb"?X+"&wbraid="+ka:X+"&gclid="+ka+"&gclsrc="+Y:Y==="aw.ds"&&(X+="&gclsrc=aw.ds");Sc(X)}}else if(r!==void 0&&!ov(a.prefix,"gad",w!==void 0)){var ja=pv+"?"+v.join("&");
Sc(ja)}}}})}},ov=function(a,b,c){var d=Ep("joined_auid",function(){return{}}),e=(c?a||"_gcl":"")+"."+b;if(d[e])return!0;d[e]=!0;return!1},kv=function(){var a=cl(x.location.href),b=void 0,c=void 0,d=Xk(a,"query",!1,void 0,"gad_source"),e=Xk(a,"query",!1,void 0,"gad_campaignid"),f,g=a.hash.replace("#","").match(rv);f=g?g[1]:void 0;d&&f?(b=d,c=1):d?(b=d,c=2):f&&(b=f,c=3);return{Yf:b,am:c,bj:e}},nv=function(){var a=Sl(!1)===1?x.top.location.href:x.location.href;return a=a.replace(/[\?#].*$/,"")},sv=function(a){var b=
[];tb(a,function(c,d){d=xu(d);for(var e=[],f=0;f<d.length;f++)e.push(d[f].gclid);e.length&&b.push(c+":"+e.join(","))});return b.join(";")},uv=function(a,b){return tv("dc",a,b)},vv=function(a,b){return tv("aw",a,b)},tv=function(a,b,c){if(a==="aw"||a==="dc"||a==="gb"){var d=el("gcl"+a);if(d)return d.split(".")}var e=qu(b);if(e==="_gcl"){var f=!P(mv())&&c,g;g=Iu()[a]||[];if(g.length>0)return f?["0"]:g}var h=ru(a,e);return h?mu(h):[]},lv=function(a){var b=mv();wp(function(){a();P(b)||rn(a,b)},b)},mv=
function(){return[J.m.U,J.m.V]},pv=Zi(36,'https://adservice.google.com/pagead/regclk'),rv=/^gad_source[_=](\d+)$/;function wv(){return Ep("dedupe_gclid",function(){return Ns()})};var xv=/^(www\.)?google(\.com?)?(\.[a-z]{2}t?)?$/,yv=/^www.googleadservices.com$/;function zv(a){a||(a=Av());return a.Nq?!1:a.Fp||a.Hp||a.Kp||a.Ip||a.Yf||a.bj||a.pp||a.Jp||a.vp?!0:!1}function Av(){var a={},b=gt(!0);a.Nq=!!b._up;var c=Iu(),d=kv();a.Fp=c.aw!==void 0;a.Hp=c.dc!==void 0;a.Kp=c.wbraid!==void 0;a.Ip=c.gbraid!==void 0;a.Jp=c.gclsrc==="aw.ds";a.Yf=d.Yf;a.bj=d.bj;var e=A.referrer?Xk(cl(A.referrer),"host"):"";a.vp=xv.test(e);a.pp=yv.test(e);return a};function Bv(a){var b=window,c=b.webkit;delete b.webkit;a(b.webkit);b.webkit=c}function Cv(a){var b={action:"gcl_setup"};if("CWVWebViewMessage"in a.messageHandlers)return a.messageHandlers.CWVWebViewMessage.postMessage({command:"awb",payload:b}),!0;var c=a.messageHandlers.awb;return c?(c.postMessage(b),!0):!1};function Dv(){return["ad_storage","ad_user_data"]}function Ev(a){if(E(38)&&!Gn(Cn.Z.ql)&&"webkit"in window&&window.webkit.messageHandlers){var b=function(){try{Bv(function(c){c&&("CWVWebViewMessage"in c.messageHandlers||"awb"in c.messageHandlers)&&(Fn(Cn.Z.ql,function(d){d.gclid&&Pu(d.gclid,5,a)}),Cv(c)||L(178))})}catch(c){L(177)}};qn(function(){ku(Dv())?b():rn(b,Dv())},Dv())}};var Fv=["https://www.google.com","https://www.youtube.com","https://m.youtube.com"];function Gv(a){a.data.action==="gcl_transfer"&&a.data.gadSource?Fn(Cn.Z.Of,{gadSource:a.data.gadSource}):L(173)}
function Hv(a,b){if(E(a)){if(Gn(Cn.Z.Of))return L(176),Cn.Z.Of;if(Gn(Cn.Z.tl))return L(170),Cn.Z.Of;var c=Ul();if(!c)L(171);else if(c.opener){var d=function(g){if(Fv.includes(g.origin)){a===119?Gv(g):a===200&&(Gv(g),g.data.gclid&&Pu(String(g.data.gclid),6,b));var h;(h=g.stopImmediatePropagation)==null||h.call(g);br(c,"message",d)}else L(172)};if(ar(c,"message",d)){Fn(Cn.Z.tl,!0);for(var e=l(Fv),f=e.next();!f.done;f=e.next())c.opener.postMessage({action:"gcl_setup"},f.value);L(174);return Cn.Z.Of}L(175)}}}
;var Iv=function(){this.C=this.gppString=void 0};Iv.prototype.reset=function(){this.C=this.gppString=void 0};var Jv=new Iv;var Kv=RegExp("^UA-\\d+-\\d+%3A[\\w-]+(?:%2C[\\w-]+)*(?:%3BUA-\\d+-\\d+%3A[\\w-]+(?:%2C[\\w-]+)*)*$"),Lv=/^~?[\w-]+(?:\.~?[\w-]+)*$/,Mv=/^\d+\.fls\.doubleclick\.net$/,Nv=/;gac=([^;?]+)/,Ov=/;gacgb=([^;?]+)/;
function Pv(a,b){if(Mv.test(A.location.host)){var c=A.location.href.match(b);return c&&c.length===2&&c[1].match(Kv)?Wk(c[1])||"":""}for(var d=[],e=l(Object.keys(a)),f=e.next();!f.done;f=e.next()){for(var g=f.value,h=[],m=a[g],n=0;n<m.length;n++)h.push(m[n].gclid);d.push(g+":"+h.join(","))}return d.length>0?d.join(";"):""}
function Qv(a,b,c){for(var d=ku(ju())?Ht("_gac_gb",!0):{},e=[],f=!1,g=l(Object.keys(d)),h=g.next();!h.done;h=g.next()){var m=h.value,n=dv("_gac_gb_"+m,a,b,c);f=f||n.length!==0&&n.some(function(p){return p===1});e.push(m+":"+n.join(","))}return{op:f?e.join(";"):"",np:Pv(d,Ov)}}function Rv(a){var b=A.location.href.match(new RegExp(";"+a+"=([^;?]+)"));return b&&b.length===2&&b[1].match(Lv)?b[1]:void 0}
function Sv(a){var b={},c,d,e;Mv.test(A.location.host)&&(c=Rv("gclgs"),d=Rv("gclst"),e=Rv("gcllp"));if(c&&d&&e)b.th=c,b.wh=d,b.uh=e;else{var f=Ab(),g=su((a||"_gcl")+"_gs"),h=g.map(function(p){return p.gclid}),m=g.map(function(p){return f-p.timestamp}),n=g.map(function(p){return p.Id});h.length>0&&m.length>0&&n.length>0&&(b.th=h.join("."),b.wh=m.join("."),b.uh=n.join("."))}return b}
function Tv(a,b,c,d){d=d===void 0?!1:d;if(Mv.test(A.location.host)){var e=Rv(c);if(e){if(d){var f=new Xt;Yt(f,2);Yt(f,3);return e.split(".").map(function(h){return{gclid:h,Ja:f,Eb:[1]}})}return e.split(".").map(function(h){return{gclid:h}})}}else{if(b==="gclid"){var g=(a||"_gcl")+"_aw";return d?Du(g):nu(g)}if(b==="wbraid")return nu((a||"_gcl")+"_gb");if(b==="braids")return pu({prefix:a})}return[]}function Uv(a){return Mv.test(A.location.host)?!(Rv("gclaw")||Rv("gac")):hv(a)}
function Vv(a,b,c){var d;d=c?ev(a,b):dv((b&&b.prefix||"_gcl")+"_gb",a,b);return d.length===0||d.every(function(e){return e===0})?"":d.join(".")};function Wv(){var a=x.__uspapi;if(lb(a)){var b="";try{a("getUSPData",1,function(c,d){if(d&&c){var e=c.uspString;e&&RegExp("^[\\da-zA-Z-]{1,20}$").test(e)&&(b=e)}})}catch(c){}return b}};
var aw=function(a){if(a.eventName===J.m.qa&&R(a,Q.A.ia)===K.J.Ha)if(E(24)){S(a,Q.A.te,O(a.D,J.m.ya)!=null&&O(a.D,J.m.ya)!==!1&&!P([J.m.U,J.m.V]));var b=Xv(a),c=O(a.D,J.m.Ra)!==!1;c||U(a,J.m.Sh,"1");var d=qu(b.prefix),e=R(a,Q.A.fh);if(!R(a,Q.A.da)&&!R(a,Q.A.Rf)&&!R(a,Q.A.se)){var f=O(a.D,J.m.Hb),g=O(a.D,J.m.Ta)||{};Yv({xe:c,De:g,He:f,Rc:b});if(!e&&!iv(d)){a.isAborted=!0;return}}if(e)a.isAborted=!0;else{U(a,J.m.jd,J.m.Yc);if(R(a,Q.A.da))U(a,J.m.jd,J.m.kn),U(a,J.m.da,"1");else if(R(a,Q.A.Rf))U(a,J.m.jd,
J.m.un);else if(R(a,Q.A.se))U(a,J.m.jd,J.m.rn);else{var h=Iu();U(a,J.m.Zc,h.gclid);U(a,J.m.gd,h.dclid);U(a,J.m.hk,h.gclsrc);Zv(a,J.m.Zc)||Zv(a,J.m.gd)||(U(a,J.m.ae,h.wbraid),U(a,J.m.Qe,h.gbraid));U(a,J.m.Ya,Nu());U(a,J.m.Ca,nv());if(E(27)&&xc){var m=Xk(cl(xc),"host");m&&U(a,J.m.Pk,m)}if(!R(a,Q.A.se)){var n=kv();U(a,J.m.Oe,n.Yf);U(a,J.m.Pe,n.am)}U(a,J.m.Jc,Sl(!0));var p=Av();zv(p)&&U(a,J.m.ld,"1");U(a,J.m.jk,wv());gt(!1)._up==="1"&&U(a,J.m.Fk,"1")}co=!0;U(a,J.m.Gb);U(a,J.m.Qb);var q=P([J.m.U,J.m.V]);
q&&(U(a,J.m.Gb,$v()),c&&(ut(b),U(a,J.m.Qb,st[vt(b.prefix)])));U(a,J.m.nc);U(a,J.m.nb);if(!Zv(a,J.m.Zc)&&!Zv(a,J.m.gd)&&Uv(d)){var r=ou(b);r.length>0&&U(a,J.m.nc,r.join("."))}else if(!Zv(a,J.m.ae)&&q){var t=mu(d+"_aw");t.length>0&&U(a,J.m.nb,t.join("."))}U(a,J.m.Ik,$c());a.D.isGtmEvent&&(a.D.C[J.m.Ga]=Oq.C[J.m.Ga]);Fr(a.D)?U(a,J.m.xc,!1):U(a,J.m.xc,!0);S(a,Q.A.rg,!0);var u=Wv();u!==void 0&&U(a,J.m.Bf,u||"error");var v=yr();v&&U(a,J.m.kd,v);if(E(137))try{var w=Intl.DateTimeFormat().resolvedOptions().timeZone;
U(a,J.m.ki,w||"-")}catch(D){U(a,J.m.ki,"e")}var y=xr();y&&U(a,J.m.sd,y);var z=Jv.gppString;z&&U(a,J.m.jf,z);var C=Jv.C;C&&U(a,J.m.hf,C);S(a,Q.A.Ba,!1)}}else a.isAborted=!0},Xv=function(a){var b={prefix:O(a.D,J.m.cb)||O(a.D,J.m.eb),domain:O(a.D,J.m.pb),Cc:O(a.D,J.m.qb),flags:O(a.D,J.m.Ab)};a.D.isGtmEvent&&(b.path=O(a.D,J.m.Sb));return b},bw=function(a,b){var c,d,e,f,g,h,m,n;c=a.xe;d=a.De;e=a.He;f=a.Pa;g=a.D;h=a.Ee;m=a.Jr;n=a.Lm;Yv({xe:c,De:d,He:e,Rc:b});c&&m!==!0&&(n!=null?n=String(n):n=void 0,qv(b,
f,g,h,n))},cw=function(a,b){if(!R(a,Q.A.se)){var c=Hv(119);if(c){var d=Gn(c),e=function(g){S(a,Q.A.se,!0);var h=Zv(a,J.m.Oe),m=Zv(a,J.m.Pe);U(a,J.m.Oe,String(g.gadSource));U(a,J.m.Pe,6);S(a,Q.A.da);S(a,Q.A.Rf);U(a,J.m.da);b();U(a,J.m.Oe,h);U(a,J.m.Pe,m);S(a,Q.A.se,!1)};if(d)e(d);else{var f=void 0;f=In(c,function(g,h){e(h);Jn(c,f)})}}}},Yv=function(a){var b,c,d,e;b=a.xe;c=a.De;d=a.He;e=a.Rc;b&&(qt(c[J.m.he],!!c[J.m.la])&&(Ru(dw,e),Tu(e),Et(e)),Sl()!==2?(Lu(e),Ev(e),Hv(200,e)):Ju(e),Xu(dw,e),Yu(e));
c[J.m.la]&&(Vu(dw,c[J.m.la],c[J.m.Mc],!!c[J.m.uc],e.prefix),Wu(c[J.m.la],c[J.m.Mc],!!c[J.m.uc],e.prefix),Ft(vt(e.prefix),c[J.m.la],c[J.m.Mc],!!c[J.m.uc],e),Ft("FPAU",c[J.m.la],c[J.m.Mc],!!c[J.m.uc],e));d&&(E(101)?$u(ew):$u(fw));bv(fw)},gw=function(a,b,c,d){var e,f,g;e=a.Mm;f=a.callback;g=a.im;if(typeof f==="function")if(e===J.m.nb&&g===void 0){var h=d(b.prefix,c);h.length===0?f(void 0):h.length===1?f(h[0]):f(h)}else e===J.m.Qb?(L(65),ut(b,!1),f(st[vt(b.prefix)])):f(g)},hw=function(a,b){Array.isArray(b)||
(b=[b]);var c=R(a,Q.A.ia);return b.indexOf(c)>=0},dw=["aw","dc","gb"],fw=["aw","dc","gb","ag"],ew=["aw","dc","gb","ag","gad_source"];function iw(a){var b=O(a.D,J.m.Lc),c=O(a.D,J.m.Kc);b&&!c?(a.eventName!==J.m.qa&&a.eventName!==J.m.Wd&&L(131),a.isAborted=!0):!b&&c&&(L(132),a.isAborted=!0)}function jw(a){var b=P(J.m.U)?Dp.pscdl:"denied";b!=null&&U(a,J.m.Gg,b)}function kw(a){var b=Sl(!0);U(a,J.m.Jc,b)}function lw(a){Qr()&&U(a,J.m.ee,1)}
function $v(){var a=A.title;if(a===void 0||a==="")return"";a=encodeURIComponent(a);for(var b=256;b>0&&Wk(a.substring(0,b))===void 0;)b--;return Wk(a.substring(0,b))||""}function mw(a){nw(a,Lp.Cf.Vm,O(a.D,J.m.qb))}function nw(a,b,c){Zv(a,J.m.wd)||U(a,J.m.wd,{});Zv(a,J.m.wd)[b]=c}function ow(a){S(a,Q.A.Qf,an.X.Fa)}function pw(a){var b=ib("GTAG_EVENT_FEATURE_CHANNEL");b&&(U(a,J.m.kf,b),gb())}function qw(a){var b=a.D.getMergedValues(J.m.sc);b&&a.mergeHitDataForKey(J.m.sc,b)}
function rw(a,b){b=b===void 0?!1:b;var c=R(a,Q.A.Pf);if(c)if(c.indexOf(a.target.destinationId)<0){if(S(a,Q.A.Jj,!1),b||!sw(a,"custom_event_accept_rules",!1))a.isAborted=!0}else S(a,Q.A.Jj,!0)}function tw(a){sl&&(co=!0,a.eventName===J.m.qa?jo(a.D,a.target.id):(R(a,Q.A.Le)||(go[a.target.id]=!0),Kp(R(a,Q.A.ib))))};function Dw(a,b,c,d){var e=Hc(),f;if(e===1)a:{var g=ok;g=g.toLowerCase();for(var h="https://"+g,m="http://"+g,n=1,p=A.getElementsByTagName("script"),q=0;q<p.length&&q<100;q++){var r=p[q].src;if(r){r=r.toLowerCase();if(r.indexOf(m)===0){f=3;break a}n===1&&r.indexOf(h)===0&&(n=2)}}f=n}else f=e;return(f===2||d||"http:"!==x.location.protocol?a:b)+c};function Pw(a){return{getDestinationId:function(){return a.target.destinationId},getEventName:function(){return a.eventName},setEventName:function(b){a.eventName=b},getHitData:function(b){return Zv(a,b)},setHitData:function(b,c){U(a,b,c)},setHitDataIfNotDefined:function(b,c){Zv(a,b)===void 0&&U(a,b,c)},copyToHitData:function(b,c){a.copyToHitData(b,c)},getMetadata:function(b){return R(a,b)},setMetadata:function(b,c){S(a,b,c)},isAborted:function(){return a.isAborted},abort:function(){a.isAborted=!0},
getFromEventContext:function(b){return O(a.D,b)},Cb:function(){return a},getHitKeys:function(){return Object.keys(a.C)},getMergedValues:function(b){return a.D.getMergedValues(b,3)},mergeHitDataForKey:function(b,c){return ld(c)?a.mergeHitDataForKey(b,c):!1}}};var Rw=function(a){var b=Qw[a.target.destinationId];if(!a.isAborted&&b)for(var c=Pw(a),d=0;d<b.length;++d){try{b[d](c)}catch(e){a.isAborted=!0}if(a.isAborted)break}},Sw=function(a,b){var c=Qw[a];c||(c=Qw[a]=[]);c.push(b)},Qw={};function Uw(a,b){return arguments.length===1?Vw("set",a):Vw("set",a,b)}function Ww(a,b){return arguments.length===1?Vw("config",a):Vw("config",a,b)}function Xw(a,b,c){c=c||{};c[J.m.pd]=a;return Vw("event",b,c)}function Vw(){return arguments};var Zw=function(){this.messages=[];this.C=[]};Zw.prototype.enqueue=function(a,b,c){var d=this.messages.length+1;a["gtm.uniqueEventId"]=b;a["gtm.priorityId"]=d;var e=ma(Object,"assign").call(Object,{},c,{eventId:b,priorityId:d,fromContainerExecution:!0}),f={message:a,notBeforeEventId:b,priorityId:d,messageContext:e};this.messages.push(f);for(var g=0;g<this.C.length;g++)try{this.C[g](f)}catch(h){}};Zw.prototype.listen=function(a){this.C.push(a)};
Zw.prototype.get=function(){for(var a={},b=0;b<this.messages.length;b++){var c=this.messages[b],d=a[c.notBeforeEventId];d||(d=[],a[c.notBeforeEventId]=d);d.push(c)}return a};Zw.prototype.prune=function(a){for(var b=[],c=[],d=0;d<this.messages.length;d++){var e=this.messages[d];e.notBeforeEventId===a?b.push(e):c.push(e)}this.messages=c;return b};function $w(a,b,c){c.eventMetadata=c.eventMetadata||{};c.eventMetadata[Q.A.ib]=lg.canonicalContainerId;ax().enqueue(a,b,c)}
function bx(){var a=cx;ax().listen(a)}function ax(){return Ep("mb",function(){return new Zw})};var dx,ex=!1;function fx(){ex=!0;dx=dx||{}}function gx(a){ex||fx();return dx[a]};function hx(){var a=x.screen;return{width:a?a.width:0,height:a?a.height:0}}
function ix(a){if(A.hidden)return!0;var b=a.getBoundingClientRect();if(b.top===b.bottom||b.left===b.right||!x.getComputedStyle)return!0;var c=x.getComputedStyle(a,null);if(c.visibility==="hidden")return!0;for(var d=a,e=c;d;){if(e.display==="none")return!0;var f=e.opacity,g=e.filter;if(g){var h=g.indexOf("opacity(");h>=0&&(g=g.substring(h+8,g.indexOf(")",h)),g.charAt(g.length-1)==="%"&&(g=g.substring(0,g.length-1)),f=String(Math.min(Number(g),Number(f))))}if(f!==void 0&&Number(f)<=0)return!0;(d=d.parentElement)&&
(e=x.getComputedStyle(d,null))}return!1}
var kx=function(a){var b=jx(),c=b.height,d=b.width,e=a.getBoundingClientRect(),f=e.bottom-e.top,g=e.right-e.left;return f&&g?(1-Math.min((Math.max(0-e.left,0)+Math.max(e.right-d,0))/g,1))*(1-Math.min((Math.max(0-e.top,0)+Math.max(e.bottom-c,0))/f,1)):0},jx=function(){var a=A.body,b=A.documentElement||a&&a.parentElement,c,d;if(A.compatMode&&A.compatMode!=="BackCompat")c=b?b.clientHeight:0,d=b?b.clientWidth:0;else{var e=function(f,g){return f&&g?Math.min(f,g):Math.max(f,g)};c=e(b?b.clientHeight:0,a?
a.clientHeight:0);d=e(b?b.clientWidth:0,a?a.clientWidth:0)}return{width:d,height:c}};var nx=function(a){if(lx){if(a>=0&&a<mx.length&&mx[a]){var b;(b=mx[a])==null||b.disconnect();mx[a]=void 0}}else x.clearInterval(a)},qx=function(a,b,c){for(var d=0;d<c.length;d++)c[d]>1?c[d]=1:c[d]<0&&(c[d]=0);if(lx){var e=!1;Nc(function(){e||ox(a,b,c)()});return px(function(f){e=!0;for(var g={eg:0};g.eg<f.length;g={eg:g.eg},g.eg++)Nc(function(h){return function(){a(f[h.eg])}}(g))},
b,c)}return x.setInterval(ox(a,b,c),1E3)},ox=function(a,b,c){function d(h,m){var n={top:0,bottom:0,right:0,left:0,width:0,height:0},p={boundingClientRect:h.getBoundingClientRect(),intersectionRatio:m,intersectionRect:n,isIntersecting:m>0,rootBounds:n,target:h,time:Ab()};Nc(function(){a(p)})}for(var e=[],f=[],g=0;g<b.length;g++)e.push(0),f.push(-1);c.sort(function(h,m){return h-m});return function(){for(var h=0;h<b.length;h++){var m=kx(b[h]);if(m>e[h])for(;f[h]<c.length-1&&m>=c[f[h]+1];)d(b[h],m),
f[h]++;else if(m<e[h])for(;f[h]>=0&&m<=c[f[h]];)d(b[h],m),f[h]--;e[h]=m}}},px=function(a,b,c){for(var d=new x.IntersectionObserver(a,{threshold:c}),e=0;e<b.length;e++)d.observe(b[e]);for(var f=0;f<mx.length;f++)if(!mx[f])return mx[f]=d,f;return mx.push(d)-1},mx=[],lx=!(!x.IntersectionObserver||!x.IntersectionObserverEntry);
var sx=function(a){return a.tagName+":"+a.isVisible+":"+a.ka.length+":"+rx.test(a.ka)},Gx=function(a){a=a||{Be:!0,Ce:!0,Gh:void 0};a.Zb=a.Zb||{email:!0,phone:!1,address:!1};var b=tx(a),c=ux[b];if(c&&Ab()-c.timestamp<200)return c.result;var d=vx(),e=d.status,f=[],g,h,m=[];if(!E(33)){if(a.Zb&&a.Zb.email){var n=wx(d.elements);f=xx(n,a&&a.Vf);g=yx(f);n.length>10&&(e="3")}!a.Gh&&g&&(f=[g]);for(var p=0;p<f.length;p++)m.push(zx(f[p],!!a.Be,!!a.Ce));m=m.slice(0,10)}else if(a.Zb){}g&&(h=zx(g,!!a.Be,!!a.Ce));var G={elements:m,
zj:h,status:e};ux[b]={timestamp:Ab(),result:G};return G},Hx=function(a,b){if(a){var c=a.trim().replaceAll(/\s+/g,"").replaceAll(/(\d{2,})\./g,"$1").replaceAll(/-/g,"").replaceAll(/\((\d+)\)/g,"$1");if(b&&c.match(/^\+?\d{3,7}$/))return c;c.charAt(0)!=="+"&&(c="+"+c);if(c.match(/^\+\d{10,15}$/))return c}},Jx=function(a){var b=Ix(/^(\w|[- ])+$/)(a);if(!b)return b;var c=b.replaceAll(/[- ]+/g,"");return c.length>10?void 0:c},Ix=function(a){return function(b){var c=b.match(a);return c?c[0].trim().toLowerCase():
void 0}},Fx=function(a,b,c){var d=a.element,e={ka:a.ka,type:a.wa,tagName:d.tagName};b&&(e.querySelector=Kx(d));c&&(e.isVisible=!ix(d));return e},zx=function(a,b,c){return Fx({element:a.element,ka:a.ka,wa:Ex.jc},b,c)},tx=function(a){var b=!(a==null||!a.Be)+"."+!(a==null||!a.Ce);a&&a.Vf&&a.Vf.length&&(b+="."+a.Vf.join("."));a&&a.Zb&&(b+="."+a.Zb.email+"."+a.Zb.phone+"."+a.Zb.address);return b},yx=function(a){if(a.length!==0){var b;b=Lx(a,function(c){return!Mx.test(c.ka)});b=Lx(b,function(c){return c.element.tagName.toUpperCase()===
"INPUT"});b=Lx(b,function(c){return!ix(c.element)});return b[0]}},xx=function(a,b){if(!b||b.length===0)return a;for(var c=[],d=0;d<a.length;d++){for(var e=!0,f=0;f<b.length;f++){var g=b[f];if(g&&vi(a[d].element,g)){e=!1;break}}e&&c.push(a[d])}return c},Lx=function(a,b){if(a.length<=1)return a;var c=a.filter(b);return c.length===0?a:c},Kx=function(a){var b;if(a===A.body)b="body";else{var c;if(a.id)c="#"+a.id;else{var d;if(a.parentElement){var e;a:{var f=a.parentElement;if(f){for(var g=0;g<f.childElementCount;g++)if(f.children[g]===
a){e=g+1;break a}e=-1}else e=1}d=Kx(a.parentElement)+">:nth-child("+e.toString()+")"}else d="";c=d}b=c}return b},wx=function(a){for(var b=[],c=0;c<a.length;c++){var d=a[c],e=d.textContent;d.tagName.toUpperCase()==="INPUT"&&d.value&&(e=d.value);if(e){var f=e.match(Nx);if(f){var g=f[0],h;if(x.location){var m=Zk(x.location,"host",!0);h=g.toLowerCase().indexOf(m)>=0}else h=!1;h||b.push({element:d,ka:g})}}}return b},vx=function(){var a=[],b=A.body;if(!b)return{elements:a,status:"4"};for(var c=b.querySelectorAll("*"),
d=0;d<c.length&&d<1E4;d++){var e=c[d];if(!(Ox.indexOf(e.tagName.toUpperCase())>=0)&&e.children instanceof HTMLCollection){for(var f=!1,g=0;g<e.childElementCount&&g<1E4;g++)if(!(Px.indexOf(e.children[g].tagName.toUpperCase())>=0)){f=!0;break}(!f||E(33)&&Qx.indexOf(e.tagName)!==-1)&&a.push(e)}}return{elements:a,status:c.length>1E4?"2":"1"}},Nx=/[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}/i,rx=/@(gmail|googlemail)\./i,Mx=/support|noreply/i,Ox="SCRIPT STYLE IMG SVG PATH BR NOSCRIPT TEXTAREA".split(" "),Px=
["BR"],Rx=wg('',2),Ex={jc:"1",Cd:"2",vd:"3",Bd:"4",Ke:"5",Nf:"6",gh:"7",Ni:"8",Jh:"9",Ii:"10"},ux={},Qx=["INPUT","SELECT"],Sx=Ix(/^([^\x00-\x40\x5b-\x60\x7b-\xff]|[.-]|\s)+$/);
var qy=function(a,b,c){var d={};a.mergeHitDataForKey(J.m.Ki,(d[b]=c,d))},ry=function(a,b){var c=sw(a,J.m.Kg,a.D.H[J.m.Kg]);if(c&&c[b||a.eventName]!==void 0)return c[b||a.eventName]},sy=function(a){var b=R(a,Q.A.jb);if(ld(b))return b},ty=function(a){if(R(a,Q.A.zd)||!kl(a.D))return!1;if(!O(a.D,J.m.rd)){var b=O(a.D,J.m.ce);return b===!0||b==="true"}return!0},uy=function(a){return sw(a,J.m.fe,O(a.D,J.m.fe))||!!sw(a,"google_ng",!1)};var hg;var vy=Number('')||5,wy=Number('')||50,xy=qb();
var zy=function(a,b){a&&(yy("sid",a.targetId,b),yy("cc",a.clientCount,b),yy("tl",a.totalLifeMs,b),yy("hc",a.heartbeatCount,b),yy("cl",a.clientLifeMs,b))},yy=function(a,b,c){b!=null&&c.push(a+"="+b)},Ay=function(){var a=A.referrer;if(a){var b;return Xk(cl(a),"host")===((b=x.location)==null?void 0:b.host)?1:2}return 0},By="https://"+Zi(21,"www.googletagmanager.com")+"/a?",Dy=function(){this.R=Cy;this.N=0};Dy.prototype.H=function(a,b,c,d){var e=Ay(),f,
g=[];f=x===x.top&&e!==0&&b?(b==null?void 0:b.clientCount)>1?e===2?1:2:e===2?0:3:4;a&&yy("si",a.gg,g);yy("m",0,g);yy("iss",f,g);yy("if",c,g);zy(b,g);d&&yy("fm",encodeURIComponent(d.substring(0,wy)),g);this.P(g);};Dy.prototype.C=function(a,b,c,d,e){var f=[];yy("m",1,f);yy("s",a,f);yy("po",Ay(),f);b&&(yy("st",b.state,f),yy("si",b.gg,f),yy("sm",b.mg,f));zy(c,f);yy("c",d,f);e&&yy("fm",encodeURIComponent(e.substring(0,
wy)),f);this.P(f);};Dy.prototype.P=function(a){a=a===void 0?[]:a;!rl||this.N>=vy||(yy("pid",xy,a),yy("bc",++this.N,a),a.unshift("ctid="+lg.ctid+"&t=s"),this.R(""+By+a.join("&")))};var Ey=Number('')||500,Fy=Number('')||5E3,Gy=Number('20')||10,Hy=Number('')||5E3;function Iy(a){return a.performance&&a.performance.now()||Date.now()}
var Jy=function(a,b){var c=x,d;var e=function(f,g,h){h=h===void 0?{lm:function(){},om:function(){},km:function(){},onFailure:function(){}}:h;this.yo=f;this.C=g;this.N=h;this.fa=this.ma=this.heartbeatCount=this.wo=0;this.hh=!1;this.H={};this.id=String(Math.floor(Number.MAX_SAFE_INTEGER*Math.random()));this.state=0;this.gg=Iy(this.C);this.mg=Iy(this.C);this.R=10};e.prototype.init=function(){this.P(1);this.Da()};e.prototype.getState=function(){return{state:this.state,
gg:Math.round(Iy(this.C)-this.gg),mg:Math.round(Iy(this.C)-this.mg)}};e.prototype.P=function(f){this.state!==f&&(this.state=f,this.mg=Iy(this.C))};e.prototype.Il=function(){return String(this.wo++)};e.prototype.Da=function(){var f=this;this.heartbeatCount++;this.Va({type:0,clientId:this.id,requestId:this.Il(),maxDelay:this.ih()},function(g){if(g.type===0){var h;if(((h=g.failure)==null?void 0:h.failureType)!=null)if(g.stats&&(f.stats=g.stats),f.fa++,g.isDead||f.fa>Gy){var m=g.isDead&&g.failure.failureType;
f.R=m||10;f.P(4);f.vo();var n,p;(p=(n=f.N).km)==null||p.call(n,{failureType:m||10,data:g.failure.data})}else f.P(3),f.Ml();else{if(f.heartbeatCount>g.stats.heartbeatCount+Gy){f.heartbeatCount=g.stats.heartbeatCount;var q,r;(r=(q=f.N).onFailure)==null||r.call(q,{failureType:13})}f.stats=g.stats;var t=f.state;f.P(2);if(t!==2)if(f.hh){var u,v;(v=(u=f.N).om)==null||v.call(u)}else{f.hh=!0;var w,y;(y=(w=f.N).lm)==null||y.call(w)}f.fa=0;f.zo();f.Ml()}}})};e.prototype.ih=function(){return this.state===2?
Fy:Ey};e.prototype.Ml=function(){var f=this;this.C.setTimeout(function(){f.Da()},Math.max(0,this.ih()-(Iy(this.C)-this.ma)))};e.prototype.Do=function(f,g,h){var m=this;this.Va({type:1,clientId:this.id,requestId:this.Il(),command:f},function(n){if(n.type===1)if(n.result)g(n.result);else{var p,q,r,t={failureType:(r=(p=n.failure)==null?void 0:p.failureType)!=null?r:12,data:(q=n.failure)==null?void 0:q.data},u,v;(v=(u=m.N).onFailure)==null||v.call(u,t);h(t)}})};e.prototype.Va=function(f,g){var h=this;
if(this.state===4)f.failure={failureType:this.R},g(f);else{var m=this.state!==2&&f.type!==0,n=f.requestId,p,q=this.C.setTimeout(function(){var t=h.H[n];t&&h.Lf(t,7)},(p=f.maxDelay)!=null?p:Hy),r={request:f,Cm:g,xm:m,Xp:q};this.H[n]=r;m||this.sendRequest(r)}};e.prototype.sendRequest=function(f){this.ma=Iy(this.C);f.xm=!1;this.yo(f.request)};e.prototype.zo=function(){for(var f=l(Object.keys(this.H)),g=f.next();!g.done;g=f.next()){var h=this.H[g.value];h.xm&&this.sendRequest(h)}};e.prototype.vo=function(){for(var f=
l(Object.keys(this.H)),g=f.next();!g.done;g=f.next())this.Lf(this.H[g.value],this.R)};e.prototype.Lf=function(f,g){this.sb(f);var h=f.request;h.failure={failureType:g};f.Cm(h)};e.prototype.sb=function(f){delete this.H[f.request.requestId];this.C.clearTimeout(f.Xp)};e.prototype.Dp=function(f){this.ma=Iy(this.C);var g=this.H[f.requestId];if(g)this.sb(g),g.Cm(f);else{var h,m;(m=(h=this.N).onFailure)==null||m.call(h,{failureType:14})}};d=new e(a,c,b);return d};var Ky;
var Ly=function(){Ky||(Ky=new Dy);return Ky},Cy=function(a){zn(Bn(an.X.Oc),function(){Kc(a)})},My=function(a){var b=a.substring(0,a.indexOf("/_/service_worker"));return"&1p=1"+(b?"&path="+encodeURIComponent(b):"")},Ny=function(a){var b=a,c=Xj.Da;b?(b.charAt(b.length-1)!=="/"&&(b+="/"),a=b+c):a="https://www.googletagmanager.com/static/service_worker/"+c+"/";var d;try{d=new URL(a)}catch(e){return null}return d.protocol!=="https:"?null:d},Oy=function(a){var b=Gn(Cn.Z.Bl);return b&&b[a]},Py=function(a,
b,c,d,e){var f=this;this.H=d;this.R=this.P=!1;this.fa=null;this.initTime=c;this.C=15;this.N=this.Uo(a);x.setTimeout(function(){f.initialize()},1E3);Nc(function(){f.Op(a,b,e)})};k=Py.prototype;k.delegate=function(a,b,c){this.getState()!==2?(this.H.C(this.C,{state:this.getState(),gg:this.initTime,mg:Math.round(Ab())-this.initTime},void 0,a.commandType),c({failureType:this.C})):this.N.Do(a,b,c)};k.getState=function(){return this.N.getState().state};k.Op=function(a,b,c){var d=x.location.origin,e=this,
f=Ic();try{var g=f.contentDocument.createElement("iframe"),h=a.pathname,m=h[h.length-1]==="/"?a.toString():a.toString()+"/",n=b?My(h):"",p;E(133)&&(p={sandbox:"allow-same-origin allow-scripts"});Ic(m+"sw_iframe.html?origin="+encodeURIComponent(d)+n+(c?"&e=1":""),void 0,p,void 0,g);var q=function(){f.contentDocument.body.appendChild(g);g.addEventListener("load",function(){e.fa=g.contentWindow;f.contentWindow.addEventListener("message",function(r){r.origin===a.origin&&e.N.Dp(r.data)});e.initialize()})};
f.contentDocument.readyState==="complete"?q():f.contentWindow.addEventListener("load",function(){q()})}catch(r){f.parentElement.removeChild(f),this.C=11,this.H.H(void 0,void 0,this.C,r.toString())}};k.Uo=function(a){var b=this,c=Jy(function(d){var e;(e=b.fa)==null||e.postMessage(d,a.origin)},{lm:function(){b.P=!0;b.H.H(c.getState(),c.stats)},om:function(){},km:function(d){b.P?(b.C=(d==null?void 0:d.failureType)||10,b.H.C(b.C,c.getState(),c.stats,void 0,d==null?void 0:d.data)):(b.C=(d==null?void 0:
d.failureType)||4,b.H.H(c.getState(),c.stats,b.C,d==null?void 0:d.data))},onFailure:function(d){b.C=d.failureType;b.H.C(b.C,c.getState(),c.stats,d.command,d.data)}});return c};k.initialize=function(){this.R||this.N.init();this.R=!0};function Qy(){var a=kg(hg.C,"",function(){return{}});try{return a("internal_sw_allowed"),!0}catch(b){return!1}}
function Ry(a,b){var c=Math.round(Ab());b=b===void 0?!1:b;var d=x.location.origin;if(!d||!Qy()||E(168))return;wk()&&(a=""+d+vk()+"/_/service_worker");var e=Ny(a);if(e===null||Oy(e.origin))return;if(!vc()){Ly().H(void 0,void 0,6);return}var f=new Py(e,!!a,c||Math.round(Ab()),Ly(),b);Hn(Cn.Z.Bl)[e.origin]=f;}
var Sy=function(a,b,c,d){var e;if((e=Oy(a))==null||!e.delegate){var f=vc()?16:6;Ly().C(f,void 0,void 0,b.commandType);d({failureType:f});return}Oy(a).delegate(b,c,d);};
function Ty(a,b,c,d,e){var f=Ny();if(f===null){d(vc()?16:6);return}var g,h=(g=Oy(f.origin))==null?void 0:g.initTime,m=Math.round(Ab()),n={commandType:0,params:{url:a,method:0,templates:b,body:"",processResponse:!1,sinceInit:h?m-h:void 0}};e&&(n.params.encryptionKeyString=e);Sy(f.origin,n,function(p){c(p)},function(p){d(p.failureType)});}
function Uy(a,b,c,d){var e=Ny(a);if(e===null){d("_is_sw=f"+(vc()?16:6)+"te");return}var f=b?1:0,g=Math.round(Ab()),h,m=(h=Oy(e.origin))==null?void 0:h.initTime,n=m?g-m:void 0,p=!1;E(169)&&(p=!0);Sy(e.origin,{commandType:0,params:{url:a,method:f,templates:c,body:b||"",processResponse:!0,suppressSuccessCallback:p,sinceInit:n,attributionReporting:!0,referer:x.location.href}},function(){},function(q){var r="_is_sw=f"+q.failureType,t,u=(t=Oy(e.origin))==
null?void 0:t.getState();u!==void 0&&(r+="s"+u);d(n?r+("t"+n):r+"te")});};function Vy(a){if(E(10)||wk()||Xj.N||kl(a.D)||E(168))return;Ry(void 0,E(131));};var Wy="platform platformVersion architecture model uaFullVersion bitness fullVersionList wow64".split(" ");function Xy(a){var b;return(b=a.google_tag_data)!=null?b:a.google_tag_data={}}function Yy(a){var b=a.google_tag_data,c;if(b!=null&&b.uach){var d=b.uach,e=ma(Object,"assign").call(Object,{},d);d.fullVersionList&&(e.fullVersionList=d.fullVersionList.slice(0));c=e}else c=null;return c}function Zy(a){var b,c;return(c=(b=a.google_tag_data)==null?void 0:b.uach_promise)!=null?c:null}
function $y(a){var b,c;return typeof((b=a.navigator)==null?void 0:(c=b.userAgentData)==null?void 0:c.getHighEntropyValues)==="function"}function az(a){if(!$y(a))return null;var b=Xy(a);if(b.uach_promise)return b.uach_promise;var c=a.navigator.userAgentData.getHighEntropyValues(Wy).then(function(d){b.uach!=null||(b.uach=d);return d});return b.uach_promise=c};
var cz=function(a,b){if(a)for(var c=bz(a),d=l(Object.keys(c)),e=d.next();!e.done;e=d.next()){var f=e.value;U(b,f,c[f])}},bz=function(a){var b={};b[J.m.tf]=a.architecture;b[J.m.uf]=a.bitness;a.fullVersionList&&(b[J.m.vf]=a.fullVersionList.map(function(c){return encodeURIComponent(c.brand||"")+";"+encodeURIComponent(c.version||"")}).join("|"));b[J.m.wf]=a.mobile?"1":"0";b[J.m.xf]=a.model;b[J.m.yf]=a.platform;b[J.m.zf]=a.platformVersion;b[J.m.Af]=a.wow64?"1":"0";return b},dz=function(a){var b=0,c=function(h,
m){try{a(h,m)}catch(n){}},d=x,e=Yy(d);if(e)c(e);else{var f=Zy(d);if(f){b=Math.min(Math.max(isFinite(b)?b:0,0),1E3);var g=d.setTimeout(function(){c.hg||(c.hg=!0,L(106),c(null,Error("Timeout")))},b);f.then(function(h){c.hg||(c.hg=!0,L(104),d.clearTimeout(g),c(h))}).catch(function(h){c.hg||(c.hg=!0,L(105),d.clearTimeout(g),c(null,h))})}else c(null)}},fz=function(){var a=x;if($y(a)&&(ez=Ab(),!Zy(a))){var b=az(a);b&&(b.then(function(){L(95)}),b.catch(function(){L(96)}))}},ez;function gz(a){var b=a.location.href;if(a===a.top)return{url:b,Tp:!0};var c=!1,d=a.document;d&&d.referrer&&(b=d.referrer,a.parent===a.top&&(c=!0));var e=a.location.ancestorOrigins;if(e){var f=e[e.length-1];f&&b.indexOf(f)===-1&&(c=!1,b=f)}return{url:b,Tp:c}};var Vz=function(){var a;E(90)&&wo()!==""&&(a=wo());return"https://"+(a?a+".":"")+"analytics.google.com/g/collect"},Wz=function(){var a="www";E(90)&&wo()&&(a=wo());return"https://"+a+".google-analytics.com/g/collect"};function Xz(a,b){var c=!!wk();switch(a){case 45:return"https://www.google.com/ccm/collect";case 46:return c?vk()+"/gs/ccm/collect":"https://pagead2.googlesyndication.com/ccm/collect";case 51:return"https://www.google.com/travel/flights/click/conversion";case 9:return"https://googleads.g.doubleclick.net/pagead/viewthroughconversion";case 17:return c?E(90)&&wo()?Vz():""+vk()+"/ag/g/c":Vz();case 16:return c?E(90)&&wo()?Wz():""+vk()+"/ga/g/c":Wz();case 1:return"https://ad.doubleclick.net/activity;";case 2:return c?
vk()+"/ddm/activity/":"https://ade.googlesyndication.com/ddm/activity/";case 33:return"https://ad.doubleclick.net/activity;register_conversion=1;";case 11:return c?vk()+"/d/pagead/form-data":E(141)?"https://www.google.com/pagead/form-data":"https://google.com/pagead/form-data";case 3:return"https://"+b.Eo+".fls.doubleclick.net/activityi;";case 5:return"https://www.googleadservices.com/pagead/conversion";case 6:return c?vk()+"/gs/pagead/conversion":"https://pagead2.googlesyndication.com/pagead/conversion";
case 8:return"https://www.google.com/pagead/1p-conversion";case 22:return c?vk()+"/as/d/ccm/conversion":"https://www.googleadservices.com/ccm/conversion";case 60:return c?vk()+"/gs/ccm/conversion":"https://pagead2.googlesyndication.com/ccm/conversion";case 23:return c?vk()+"/g/d/ccm/conversion":"https://www.google.com/ccm/conversion";case 55:return c?vk()+"/gs/measurement/conversion/":"https://pagead2.googlesyndication.com/measurement/conversion/";case 54:return E(205)?"https://www.google.com/measurement/conversion/":
c?vk()+"/g/measurement/conversion/":"https://www.google.com/measurement/conversion/";case 21:return c?vk()+"/d/ccm/form-data":E(141)?"https://www.google.com/ccm/form-data":"https://google.com/ccm/form-data";case 7:case 52:case 53:case 39:case 38:case 40:case 37:case 49:case 48:case 14:case 24:case 19:case 27:case 30:case 36:case 62:case 26:case 29:case 32:case 35:case 57:case 58:case 50:case 12:case 13:case 20:case 18:case 59:case 47:case 44:case 43:case 15:case 0:case 61:case 56:case 25:case 28:case 31:case 34:throw Error("Unsupported endpoint");
default:mc(a,"Unknown endpoint")}};function Yz(a){a=a===void 0?[]:a;return Yj(a).join("~")}function Zz(){if(!E(118))return"";var a,b;return(((a=Pm(Em()))==null?void 0:(b=a.context)==null?void 0:b.loadExperiments)||[]).join("~")};function $z(a,b){b&&tb(b,function(c,d){typeof d!=="object"&&d!==void 0&&(a["1p."+c]=String(d))})};
var bA=function(a,b){for(var c={},d=function(p,q){var r;r=q===!0?"1":q===!1?"0":encodeURIComponent(String(q));c[p]=r},e=l(Object.keys(a.C)),f=e.next();!f.done;f=e.next()){var g=f.value,h=Zv(a,g),m=aA[g];m&&h!==void 0&&h!==""&&(!R(a,Q.A.te)||g!==J.m.Zc&&g!==J.m.gd&&g!==J.m.ae&&g!==J.m.Qe||(h="0"),d(m,h))}d("gtm",Ur({Pa:R(a,Q.A.ib)}));Gr()&&d("gcs",Hr());d("gcd",Lr(a.D));Or()&&d("dma_cps",Mr());d("dma",Nr());jr(rr())&&d("tcfd",Pr());Yz()&&d("tag_exp",Yz());Zz()&&d("ptag_exp",Zz());if(R(a,Q.A.rg)){d("tft",
Ab());var n=Zc();n!==void 0&&d("tfd",Math.round(n))}E(24)&&d("apve","1");(E(25)||E(26))&&d("apvf",Wc()?E(26)?"f":"sb":"nf");tn[an.X.Fa]!==$m.Ia.pe||wn[an.X.Fa].isConsentGranted()||(c.limited_ads="1");b(c)},cA=function(a,b,c){var d=b.D;gp({targetId:b.target.destinationId,request:{url:a,parameterEncoding:2,endpoint:c},Oa:{eventId:d.eventId,priorityId:d.priorityId},rh:{eventId:R(b,Q.A.Ie),priorityId:R(b,Q.A.Je)}})},dA=function(a,b,c){var d={destinationId:b.target.destinationId,endpoint:c,eventId:b.D.eventId,
priorityId:b.D.priorityId};cA(a,b,c);um(d,a,void 0,{Eh:!0,method:"GET"},function(){},function(){tm(d,a+"&img=1")})},eA=function(a){var b=Cc()||Ac()?"www.google.com":"www.googleadservices.com",c=[];tb(a,function(d,e){d==="dl"?c.push("url="+e):d==="dr"?c.push("ref="+e):d==="uid"?c.push("userId="+e):c.push(d+"="+e)});return"https://"+b+"/pagead/set_partitioned_cookie?"+c.join("&")},fA=function(a){bA(a,function(b){if(R(a,Q.A.ia)===K.J.Ha){var c=[];a.target.destinationId&&c.push("tid="+a.target.destinationId);
tb(b,function(r,t){c.push(r+"="+t)});var d=P([J.m.U,J.m.V])?45:46,e=Xz(d)+"?"+c.join("&");cA(e,a,d);var f=a.D,g={destinationId:a.target.destinationId,endpoint:d,eventId:f.eventId,priorityId:f.priorityId};if(E(26)&&Wc()){um(g,e,void 0,{Eh:!0},function(){},function(){tm(g,e+"&img=1")});var h=P([J.m.U,J.m.V]),m=Zv(a,J.m.ld)==="1",n=Zv(a,J.m.Sh)==="1";if(h&&m&&!n){var p=eA(b),q=Cc()||Ac()?58:57;dA(p,a,q)}}else sm(g,e)||tm(g,e+"&img=1");if(lb(a.D.onSuccess))a.D.onSuccess()}})},gA={},aA=(gA[J.m.da]="gcu",
gA[J.m.nc]="gclgb",gA[J.m.nb]="gclaw",gA[J.m.Oe]="gad_source",gA[J.m.Pe]="gad_source_src",gA[J.m.Zc]="gclid",gA[J.m.hk]="gclsrc",gA[J.m.Qe]="gbraid",gA[J.m.ae]="wbraid",gA[J.m.Qb]="auid",gA[J.m.jk]="rnd",gA[J.m.Sh]="ncl",gA[J.m.Wh]="gcldc",gA[J.m.gd]="dclid",gA[J.m.Tb]="edid",gA[J.m.jd]="en",gA[J.m.kd]="gdpr",gA[J.m.Ub]="gdid",gA[J.m.ee]="_ng",gA[J.m.hf]="gpp_sid",gA[J.m.jf]="gpp",gA[J.m.kf]="_tu",gA[J.m.Fk]="gtm_up",gA[J.m.Jc]="frm",gA[J.m.ld]="lps",gA[J.m.Qg]="did",gA[J.m.Ik]="navt",gA[J.m.Ca]=
"dl",gA[J.m.Ya]="dr",gA[J.m.Gb]="dt",gA[J.m.Pk]="scrsrc",gA[J.m.rf]="ga_uid",gA[J.m.sd]="gdpr_consent",gA[J.m.ki]="u_tz",gA[J.m.Ma]="uid",gA[J.m.Bf]="us_privacy",gA[J.m.xc]="npa",gA);var hA={};hA.O=ms.O;var iA={lr:"L",so:"S",Cr:"Y",Qq:"B",ar:"E",ir:"I",zr:"TC",hr:"HTC"},jA={so:"S",Zq:"V",Tq:"E",yr:"tag"},kA={},lA=(kA[hA.O.Pi]="6",kA[hA.O.Qi]="5",kA[hA.O.Oi]="7",kA);function mA(){function a(c,d){var e=ib(d);e&&b.push([c,e])}var b=[];a("u","GTM");a("ut","TAGGING");a("h","HEALTH");return b};var nA=!1;
function GA(a){}function HA(a){}
function IA(){}function JA(a){}
function KA(a){}function LA(a){}
function MA(){}function NA(a,b){}
function OA(a,b,c){}
function PA(){};var QA=Object.freeze({cache:"no-store",credentials:"include",method:"GET",keepalive:!0,redirect:"follow"});
function RA(a,b,c,d,e,f,g,h){var m=ma(Object,"assign").call(Object,{},QA);c&&(m.body=c,m.method="POST");ma(Object,"assign").call(Object,m,e);h==null||jm(h);x.fetch(b,m).then(function(n){h==null||km(h);if(!n.ok)g==null||g();else if(n.body){var p=n.body.getReader(),q=new TextDecoder;return new Promise(function(r){function t(){p.read().then(function(u){var v;v=u.done;var w=q.decode(u.value,{stream:!v});SA(d,w);v?(f==null||f(),r()):t()}).catch(function(){r()})}t()})}}).catch(function(){h==null||km(h);
g?g():E(128)&&(b+="&_z=retryFetch",c?sm(a,b,c):rm(a,b))})};var TA=function(a){this.P=a;this.C=""},UA=function(a,b){a.H=b;return a},VA=function(a,b){a.N=b;return a},SA=function(a,b){b=a.C+b;for(var c=b.indexOf("\n\n");c!==-1;){var d=a,e;a:{var f=l(b.substring(0,c).split("\n")),g=f.next().value,h=f.next().value;if(g.indexOf("event: message")===0&&h.indexOf("data: ")===0)try{e=JSON.parse(h.substring(h.indexOf(":")+1));break a}catch(m){}e=void 0}WA(d,e);b=b.substring(c+2);c=b.indexOf("\n\n")}a.C=b},XA=function(a,b){return function(){if(b.fallback_url&&b.fallback_url_method){var c=
{};WA(a,(c[b.fallback_url_method]=[b.fallback_url],c.options={},c))}}},WA=function(a,b){b&&(YA(b.send_pixel,b.options,a.P),YA(b.create_iframe,b.options,a.H),YA(b.fetch,b.options,a.N))};function ZA(a){var b=a.search;return a.protocol+"//"+a.hostname+a.pathname+(b?b+"&richsstsse":"?richsstsse")}function YA(a,b,c){if(a&&c){var d=a||[];if(Array.isArray(d))for(var e=ld(b)?b:{},f=l(d),g=f.next();!g.done;g=f.next())c(g.value,e)}};var $A=function(a,b){this.bq=a;this.timeoutMs=b;this.Xa=void 0},jm=function(a){a.Xa||(a.Xa=setTimeout(function(){a.bq();a.Xa=void 0},a.timeoutMs))},km=function(a){a.Xa&&(clearTimeout(a.Xa),a.Xa=void 0)};var NB=new RegExp(/^(.*\.)?(google|youtube|blogger|withgoogle)(\.com?)?(\.[a-z]{2})?\.?$/),OB={cl:["ecl"],customPixels:["nonGooglePixels"],ecl:["cl"],ehl:["hl"],gaawc:["googtag"],hl:["ehl"],html:["customScripts","customPixels","nonGooglePixels","nonGoogleScripts","nonGoogleIframes"],customScripts:["html","customPixels","nonGooglePixels","nonGoogleScripts","nonGoogleIframes"],nonGooglePixels:[],nonGoogleScripts:["nonGooglePixels"],nonGoogleIframes:["nonGooglePixels"]},PB={cl:["ecl"],customPixels:["customScripts",
"html"],ecl:["cl"],ehl:["hl"],gaawc:["googtag"],hl:["ehl"],html:["customScripts"],customScripts:["html"],nonGooglePixels:["customPixels","customScripts","html","nonGoogleScripts","nonGoogleIframes"],nonGoogleScripts:["customScripts","html"],nonGoogleIframes:["customScripts","html","nonGoogleScripts"]},QB="google customPixels customScripts html nonGooglePixels nonGoogleScripts nonGoogleIframes".split(" ");
function RB(){var a=Ck("gtm.allowlist")||Ck("gtm.whitelist");a&&L(9);lk&&!E(212)?a=["google","gtagfl","lcl","zone","cmpPartners"]:E(212)&&(a=void 0);NB.test(x.location&&x.location.hostname)&&(lk?L(116):(L(117),SB&&(a=[],window.console&&window.console.log&&window.console.log("GTM blocked. See go/13687728."))));var b=a&&Fb(xb(a),OB),c=Ck("gtm.blocklist")||Ck("gtm.blacklist");c||(c=Ck("tagTypeBlacklist"))&&L(3);c?L(8):c=[];NB.test(x.location&&x.location.hostname)&&(c=xb(c),c.push("nonGooglePixels","nonGoogleScripts",
"sandboxedScripts"));xb(c).indexOf("google")>=0&&L(2);var d=c&&Fb(xb(c),PB),e={};return function(f){var g=f&&f[jf.Ua];if(!g||typeof g!=="string")return!0;g=g.replace(/^_*/,"");if(e[g]!==void 0)return e[g];var h=sk[g]||[],m=!0;if(a){var n;if(n=m)a:{if(b.indexOf(g)<0){if(lk&&h.indexOf("cmpPartners")>=0){n=!0;break a}if(h&&h.length>0)for(var p=0;p<h.length;p++){if(b.indexOf(h[p])<0){L(11);n=!1;break a}}else{n=!1;break a}}n=!0}m=n}var q=!1;if(c){var r=d.indexOf(g)>=0;if(r)q=r;else{var t=rb(d,h||[]);t&&
L(10);q=t}}var u=!m||q;!u&&(h.indexOf("sandboxedScripts")===-1?0:lk&&h.indexOf("cmpPartners")>=0?!TB():b&&b.indexOf("sandboxedScripts")!==-1?0:rb(d,QB))&&(u=!0);return e[g]=u}}function TB(){var a=kg(hg.C,lg.ctid,function(){return{}});try{return a("inject_cmp_banner"),!0}catch(b){return!1}}var SB=!1;SB=!0;function UB(a,b,c,d,e){if(!Um(a)){d.loadExperiments=Zj();Dm(a,d,e);var f=VB(a),g=function(){Fm().container[a]&&(Fm().container[a].state=3);WB()},h={destinationId:a,endpoint:0};if(wk())vm(h,vk()+"/"+f,void 0,g);else{var m=Gb(a,"GTM-"),n=jl(),p=c?"/gtag/js":"/gtm.js",q=il(b,p+f);if(!q){var r=bk.xg+p;n&&xc&&m&&(r=xc.replace(/^(?:https?:\/\/)?/i,"").split(/[?#]/)[0]);q=Dw("https://","http://",r+f)}vm(h,q,void 0,g)}}}function WB(){Wm()||tb(Xm(),function(a,b){XB(a,b.transportUrl,b.context);L(92)})}
function XB(a,b,c,d){if(!Vm(a))if(c.loadExperiments||(c.loadExperiments=Zj()),Wm()){var e;(e=Fm().destination)[a]!=null||(e[a]={state:0,transportUrl:b,context:c,parent:Em()});Fm().destination[a].state=0;Gm({ctid:a,isDestination:!0},d);L(91)}else{var f;(f=Fm().destination)[a]!=null||(f[a]={context:c,state:1,parent:Em()});Fm().destination[a].state=1;Gm({ctid:a,isDestination:!0},d);var g={destinationId:a,endpoint:0};if(wk())vm(g,vk()+("/gtd"+VB(a,!0)));else{var h="/gtag/destination"+VB(a,!0),m=il(b,
h);m||(m=Dw("https://","http://",bk.xg+h));vm(g,m)}}}function VB(a,b){b=b===void 0?!1:b;var c="?id="+encodeURIComponent(a);ek!=="dataLayer"&&(c+="&l="+ek);if(!Gb(a,"GTM-")||b)c=E(130)?c+(wk()?"&sc=1":"&cx=c"):c+"&cx=c";c+="&gtm="+Vr();jl()&&(c+="&sign="+bk.Li);var d=Xj.H;d===1?c+="&fps=fc":d===2&&(c+="&fps=fe");!E(191)&&Zj().join("~")&&(c+="&tag_exp="+Zj().join("~"));return c};var YB=function(){this.H=0;this.C={}};YB.prototype.addListener=function(a,b,c){var d=++this.H;this.C[a]=this.C[a]||{};this.C[a][String(d)]={listener:b,Ge:c};return d};YB.prototype.removeListener=function(a,b){var c=this.C[a],d=String(b);if(!c||!c[d])return!1;delete c[d];return!0};var $B=function(a,b){var c=[];tb(ZB.C[a],function(d,e){c.indexOf(e.listener)<0&&(e.Ge===void 0||b.indexOf(e.Ge)>=0)&&c.push(e.listener)});return c};function aC(a,b,c){return{entityType:a,indexInOriginContainer:b,nameInOriginContainer:c,originContainerId:lg.ctid}};function bC(a,b){if(data.entities){var c=data.entities[a];if(c)return c[b]}};var dC=function(a,b){this.C=!1;this.P=[];this.eventData={tags:[]};this.R=!1;this.H=this.N=0;cC(this,a,b)},eC=function(a,b,c,d){if(gk.hasOwnProperty(b)||b==="__zone")return-1;var e={};ld(d)&&(e=md(d,e));e.id=c;e.status="timeout";return a.eventData.tags.push(e)-1},fC=function(a,b,c,d){var e=a.eventData.tags[b];e&&(e.status=c,e.executionTime=d)},gC=function(a){if(!a.C){for(var b=a.P,c=0;c<b.length;c++)b[c]();a.C=!0;a.P.length=0}},cC=function(a,b,c){b!==void 0&&a.Sf(b);c&&x.setTimeout(function(){gC(a)},
Number(c))};dC.prototype.Sf=function(a){var b=this,c=Cb(function(){Nc(function(){a(lg.ctid,b.eventData)})});this.C?c():this.P.push(c)};var hC=function(a){a.N++;return Cb(function(){a.H++;a.R&&a.H>=a.N&&gC(a)})},iC=function(a){a.R=!0;a.H>=a.N&&gC(a)};var jC={};function kC(){return x[lC()]}
function lC(){return x.GoogleAnalyticsObject||"ga"}function oC(){var a=lg.ctid;}
function pC(a,b){return function(){var c=kC(),d=c&&c.getByName&&c.getByName(a);if(d){var e=d.get("sendHitTask");d.set("sendHitTask",function(f){var g=f.get("hitPayload"),h=f.get("hitCallback"),m=g.indexOf("&tid="+b)<0;m&&(f.set("hitPayload",g.replace(/&tid=UA-[0-9]+-[0-9]+/,"&tid="+b),!0),f.set("hitCallback",void 0,!0));e(f);m&&(f.set("hitPayload",g,!0),f.set("hitCallback",h,!0),f.set("_x_19",void 0,!0),e(f))})}}};var vC=["es","1"],wC={},xC={};function yC(a,b){if(rl){var c;c=b.match(/^(gtm|gtag)\./)?encodeURIComponent(b):"*";wC[a]=[["e",c],["eid",a]];Gq(a)}}function zC(a){var b=a.eventId,c=a.Qd;if(!wC[b])return[];var d=[];xC[b]||d.push(vC);d.push.apply(d,ya(wC[b]));c&&(xC[b]=!0);return d};var AC={},BC={},CC={};function DC(a,b,c,d){rl&&E(120)&&((d===void 0?0:d)?(CC[b]=CC[b]||0,++CC[b]):c!==void 0?(BC[a]=BC[a]||{},BC[a][b]=Math.round(c)):(AC[a]=AC[a]||{},AC[a][b]=(AC[a][b]||0)+1))}function EC(a){var b=a.eventId,c=a.Qd,d=AC[b]||{},e=[],f;for(f in d)d.hasOwnProperty(f)&&e.push(""+f+d[f]);c&&delete AC[b];return e.length?[["md",e.join(".")]]:[]}
function FC(a){var b=a.eventId,c=a.Qd,d=BC[b]||{},e=[],f;for(f in d)d.hasOwnProperty(f)&&e.push(""+f+d[f]);c&&delete BC[b];return e.length?[["mtd",e.join(".")]]:[]}function GC(){for(var a=[],b=l(Object.keys(CC)),c=b.next();!c.done;c=b.next()){var d=c.value;a.push(""+d+CC[d])}return a.length?[["mec",a.join(".")]]:[]};var HC={},IC={};function JC(a,b,c){if(rl&&b){var d=nl(b);HC[a]=HC[a]||[];HC[a].push(c+d);var e=b[jf.Ua];if(!e)throw Error("Error: No function name given for function call.");var f=(Lf[e]?"1":"2")+d;IC[a]=IC[a]||[];IC[a].push(f);Gq(a)}}function KC(a){var b=a.eventId,c=a.Qd,d=[],e=HC[b]||[];e.length&&d.push(["tr",e.join(".")]);var f=IC[b]||[];f.length&&d.push(["ti",f.join(".")]);c&&(delete HC[b],delete IC[b]);return d};function LC(a,b,c){c=c===void 0?!1:c;MC().addRestriction(0,a,b,c)}function NC(a,b,c){c=c===void 0?!1:c;MC().addRestriction(1,a,b,c)}function OC(){var a=Mm();return MC().getRestrictions(1,a)}var PC=function(){this.container={};this.C={}},QC=function(a,b){var c=a.container[b];c||(c={_entity:{internal:[],external:[]},_event:{internal:[],external:[]}},a.container[b]=c);return c};
PC.prototype.addRestriction=function(a,b,c,d){d=d===void 0?!1:d;if(!d||!this.C[b]){var e=QC(this,b);a===0?d?e._entity.external.push(c):e._entity.internal.push(c):a===1&&(d?e._event.external.push(c):e._event.internal.push(c))}};
PC.prototype.getRestrictions=function(a,b){var c=QC(this,b);if(a===0){var d,e;return[].concat(ya((c==null?void 0:(d=c._entity)==null?void 0:d.internal)||[]),ya((c==null?void 0:(e=c._entity)==null?void 0:e.external)||[]))}if(a===1){var f,g;return[].concat(ya((c==null?void 0:(f=c._event)==null?void 0:f.internal)||[]),ya((c==null?void 0:(g=c._event)==null?void 0:g.external)||[]))}return[]};
PC.prototype.getExternalRestrictions=function(a,b){var c=QC(this,b),d,e;return a===0?(c==null?void 0:(d=c._entity)==null?void 0:d.external)||[]:(c==null?void 0:(e=c._event)==null?void 0:e.external)||[]};PC.prototype.removeExternalRestrictions=function(a){var b=QC(this,a);b._event&&(b._event.external=[]);b._entity&&(b._entity.external=[]);this.C[a]=!0};function MC(){return Ep("r",function(){return new PC})};function RC(a,b,c,d){var e=Jf[a],f=SC(a,b,c,d);if(!f)return null;var g=Xf(e[jf.Cl],c,[]);if(g&&g.length){var h=g[0];f=RC(h.index,{onSuccess:f,onFailure:h.Yl===1?b.terminate:f,terminate:b.terminate},c,d)}return f}
function SC(a,b,c,d){function e(){function w(){ko(3);var M=Ab()-I;JC(c.id,f,"7");fC(c.Pc,D,"exception",M);E(109)&&OA(c,f,hA.O.Oi);G||(G=!0,h())}if(f[jf.lo])h();else{var y=Wf(f,c,[]),z=y[jf.Sm];if(z!=null)for(var C=0;C<z.length;C++)if(!P(z[C])){h();return}var D=eC(c.Pc,String(f[jf.Ua]),Number(f[jf.mh]),y[jf.METADATA]),G=!1;y.vtp_gtmOnSuccess=function(){if(!G){G=!0;var M=Ab()-I;JC(c.id,Jf[a],"5");fC(c.Pc,D,"success",M);E(109)&&OA(c,f,hA.O.Qi);g()}};y.vtp_gtmOnFailure=function(){if(!G){G=!0;var M=Ab()-
I;JC(c.id,Jf[a],"6");fC(c.Pc,D,"failure",M);E(109)&&OA(c,f,hA.O.Pi);h()}};y.vtp_gtmTagId=f.tag_id;y.vtp_gtmEventId=c.id;c.priorityId&&(y.vtp_gtmPriorityId=c.priorityId);JC(c.id,f,"1");E(109)&&NA(c,f);var I=Ab();try{Yf(y,{event:c,index:a,type:1})}catch(M){w(M)}E(109)&&OA(c,f,hA.O.Jl)}}var f=Jf[a],g=b.onSuccess,h=b.onFailure,m=b.terminate;if(c.isBlocked(f))return null;var n=Xf(f[jf.Kl],c,[]);if(n&&n.length){var p=n[0],q=RC(p.index,{onSuccess:g,onFailure:h,terminate:m},c,d);if(!q)return null;g=q;h=p.Yl===
2?m:q}if(f[jf.rl]||f[jf.no]){var r=f[jf.rl]?Kf:c.Gq,t=g,u=h;if(!r[a]){var v=TC(a,r,Cb(e));g=v.onSuccess;h=v.onFailure}return function(){r[a](t,u)}}return e}function TC(a,b,c){var d=[],e=[];b[a]=UC(d,e,c);return{onSuccess:function(){b[a]=VC;for(var f=0;f<d.length;f++)d[f]()},onFailure:function(){b[a]=WC;for(var f=0;f<e.length;f++)e[f]()}}}function UC(a,b,c){return function(d,e){a.push(d);b.push(e);c()}}function VC(a){a()}function WC(a,b){b()};var ZC=function(a,b){for(var c=[],d=0;d<Jf.length;d++)if(a[d]){var e=Jf[d];var f=hC(b.Pc);try{var g=RC(d,{onSuccess:f,onFailure:f,terminate:f},b,d);if(g){var h=e[jf.Ua];if(!h)throw Error("Error: No function name given for function call.");var m=Lf[h];c.push({Im:d,priorityOverride:(m?m.priorityOverride||0:0)||bC(e[jf.Ua],1)||0,execute:g})}else XC(d,b),f()}catch(p){f()}}c.sort(YC);for(var n=0;n<c.length;n++)c[n].execute();
return c.length>0};function $C(a,b){if(!ZB)return!1;var c=a["gtm.triggers"]&&String(a["gtm.triggers"]),d=$B(a.event,c?String(c).split(","):[]);if(!d.length)return!1;for(var e=0;e<d.length;++e){var f=hC(b);try{d[e](a,f)}catch(g){f()}}return!0}function YC(a,b){var c,d=b.priorityOverride,e=a.priorityOverride;c=d>e?1:d<e?-1:0;var f;if(c!==0)f=c;else{var g=a.Im,h=b.Im;f=g>h?1:g<h?-1:0}return f}
function XC(a,b){if(rl){var c=function(d){var e=b.isBlocked(Jf[d])?"3":"4",f=Xf(Jf[d][jf.Cl],b,[]);f&&f.length&&c(f[0].index);JC(b.id,Jf[d],e);var g=Xf(Jf[d][jf.Kl],b,[]);g&&g.length&&c(g[0].index)};c(a)}}var aD=!1,ZB;function bD(){ZB||(ZB=new YB);return ZB}
function cD(a){var b=a["gtm.uniqueEventId"],c=a["gtm.priorityId"],d=a.event;if(E(109)){}if(d==="gtm.js"){if(aD)return!1;aD=!0}var e=!1,f=OC(),g=md(a,null);if(!f.every(function(t){return t({originalEventData:g})})){if(d!=="gtm.js"&&d!=="gtm.init"&&d!=="gtm.init_consent")return!1;e=!0}yC(b,d);var h=a.eventCallback,m=
a.eventTimeout,n={id:b,priorityId:c,name:d,isBlocked:dD(g,e),Gq:[],logMacroError:function(){L(6);ko(0)},cachedModelValues:eD(),Pc:new dC(function(){if(E(109)){}h&&h.apply(h,Array.prototype.slice.call(arguments,0))},m),
originalEventData:g};E(120)&&rl&&(n.reportMacroDiscrepancy=DC);E(109)&&KA(n.id);var p=cg(n);E(109)&&LA(n.id);e&&(p=fD(p));E(109)&&JA(b);var q=ZC(p,n),r=$C(a,n.Pc);iC(n.Pc);d!=="gtm.js"&&d!=="gtm.sync"||oC();return gD(p,q)||r}function eD(){var a={};a.event=Hk("event",1);a.ecommerce=Hk("ecommerce",1);a.gtm=Hk("gtm");a.eventModel=Hk("eventModel");return a}
function dD(a,b){var c=RB();return function(d){if(c(d))return!0;var e=d&&d[jf.Ua];if(!e||typeof e!=="string")return!0;e=e.replace(/^_*/,"");var f,g=Mm();f=MC().getRestrictions(0,g);var h=a;b&&(h=md(a,null),h["gtm.uniqueEventId"]=Number.MAX_SAFE_INTEGER);for(var m=sk[e]||[],n=l(f),p=n.next();!p.done;p=n.next()){var q=p.value;try{if(!q({entityId:e,securityGroups:m,originalEventData:h}))return!0}catch(r){return!0}}return!1}}
function fD(a){for(var b=[],c=0;c<a.length;c++)if(a[c]){var d=String(Jf[c][jf.Ua]);if(fk[d]||Jf[c][jf.oo]!==void 0||bC(d,2))b[c]=!0}return b}function gD(a,b){if(!b)return b;for(var c=0;c<a.length;c++)if(a[c]&&Jf[c]&&!gk[String(Jf[c][jf.Ua])])return!0;return!1};function hD(){bD().addListener("gtm.init",function(a,b){Xj.fa=!0;Wn();b()})};var iD=!1,jD=0,kD=[];function lD(a){if(!iD){var b=A.createEventObject,c=A.readyState==="complete",d=A.readyState==="interactive";if(!a||a.type!=="readystatechange"||c||!b&&d){iD=!0;for(var e=0;e<kD.length;e++)Nc(kD[e])}kD.push=function(){for(var f=Ca.apply(0,arguments),g=0;g<f.length;g++)Nc(f[g]);return 0}}}function mD(){if(!iD&&jD<140){jD++;try{var a,b;(b=(a=A.documentElement).doScroll)==null||b.call(a,"left");lD()}catch(c){x.setTimeout(mD,50)}}}
function nD(){var a=x;iD=!1;jD=0;if(A.readyState==="interactive"&&!A.createEventObject||A.readyState==="complete")lD();else{Lc(A,"DOMContentLoaded",lD);Lc(A,"readystatechange",lD);if(A.createEventObject&&A.documentElement.doScroll){var b=!0;try{b=!a.frameElement}catch(c){}b&&mD()}Lc(a,"load",lD)}}function oD(a){iD?a():kD.push(a)};var pD={},qD={};function rD(a,b){for(var c=[],d=[],e={},f=0;f<a.length;e={yj:void 0,ej:void 0},f++){var g=a[f];if(g.indexOf("-")>=0){if(e.yj=Op(g,b),e.yj){var h=Lm();pb(h,function(r){return function(t){return r.yj.destinationId===t}}(e))?c.push(g):d.push(g)}}else{var m=pD[g]||[];e.ej={};m.forEach(function(r){return function(t){r.ej[t]=!0}}(e));for(var n=Nm(),p=0;p<n.length;p++)if(e.ej[n[p]]){c=c.concat(Lm());break}var q=qD[g]||[];q.length&&(c=c.concat(q))}}return{sj:c,Zp:d}}
function sD(a){tb(pD,function(b,c){var d=c.indexOf(a);d>=0&&c.splice(d,1)})}function tD(a){tb(qD,function(b,c){var d=c.indexOf(a);d>=0&&c.splice(d,1)})};var uD=!1,vD=!1;function wD(a,b){var c={},d=(c.event=a,c);b&&(d.eventModel=md(b,null),b[J.m.ef]&&(d.eventCallback=b[J.m.ef]),b[J.m.Lg]&&(d.eventTimeout=b[J.m.Lg]));return d}function xD(a,b){a.hasOwnProperty("gtm.uniqueEventId")||Object.defineProperty(a,"gtm.uniqueEventId",{value:Hp()});b.eventId=a["gtm.uniqueEventId"];b.priorityId=a["gtm.priorityId"];return{eventId:b.eventId,priorityId:b.priorityId}}
function yD(a,b){var c=a&&a[J.m.pd];c===void 0&&(c=Ck(J.m.pd,2),c===void 0&&(c="default"));if(mb(c)||Array.isArray(c)){var d;d=b.isGtmEvent?mb(c)?[c]:c:c.toString().replace(/\s+/g,"").split(",");var e=rD(d,b.isGtmEvent),f=e.sj,g=e.Zp;if(g.length)for(var h=zD(a),m=0;m<g.length;m++){var n=Op(g[m],b.isGtmEvent);if(n){var p=n.destinationId,q=n.destinationId,r=Fm().destination[q];r&&r.state===0||XB(p,h,{source:3,fromContainerExecution:b.fromContainerExecution})}}var t=f.concat(g);return{sj:Pp(f,b.isGtmEvent),
Ho:Pp(t,b.isGtmEvent)}}}var AD=void 0,BD=void 0;function CD(a,b,c){var d=md(a,null);d.eventId=void 0;d.inheritParentConfig=void 0;Object.keys(b).some(function(f){return b[f]!==void 0})&&L(136);var e=md(b,null);md(c,e);$w(Ww(Nm()[0],e),a.eventId,d)}function zD(a){for(var b=l([J.m.rd,J.m.vc]),c=b.next();!c.done;c=b.next()){var d=c.value,e=a&&a[d]||Oq.C[d];if(e)return e}}
var DD={config:function(a,b){var c=xD(a,b);if(!(a.length<2)&&mb(a[1])){var d={};if(a.length>2){if(a[2]!==void 0&&!ld(a[2])||a.length>3)return;d=a[2]}var e=Op(a[1],b.isGtmEvent);if(e){var f,g,h;a:{if(!Jm.qe){var m=Pm(Em());if(Ym(m)){var n=m.parent,p=n.isDestination;h={cq:Pm(n),Vp:p};break a}}h=void 0}var q=h;q&&(f=q.cq,g=q.Vp);yC(c.eventId,"gtag.config");var r=e.destinationId,t=e.id!==r;if(t?Lm().indexOf(r)===-1:Nm().indexOf(r)===-1){if(!b.inheritParentConfig&&!d[J.m.Lc]){var u=zD(d);if(t)XB(r,u,{source:2,
fromContainerExecution:b.fromContainerExecution});else if(f!==void 0&&f.containers.indexOf(r)!==-1){var v=d;AD?CD(b,v,AD):BD||(BD=md(v,null))}else UB(r,u,!0,{source:2,fromContainerExecution:b.fromContainerExecution})}}else{if(f&&(L(128),g&&L(130),b.inheritParentConfig)){var w;var y=d;BD?(CD(b,BD,y),w=!1):(!y[J.m.ud]&&ik&&AD||(AD=md(y,null)),w=!0);w&&f.containers&&f.containers.join(",");return}sl&&(Jp===1&&(Pn.mcc=!1),Jp=2);if(ik&&!t&&!d[J.m.ud]){var z=vD;vD=!0;if(z)return}uD||L(43);if(!b.noTargetGroup)if(t){tD(e.id);
var C=e.id,D=d[J.m.Og]||"default";D=String(D).split(",");for(var G=0;G<D.length;G++){var I=qD[D[G]]||[];qD[D[G]]=I;I.indexOf(C)<0&&I.push(C)}}else{sD(e.id);var M=e.id,T=d[J.m.Og]||"default";T=T.toString().split(",");for(var da=0;da<T.length;da++){var N=pD[T[da]]||[];pD[T[da]]=N;N.indexOf(M)<0&&N.push(M)}}delete d[J.m.Og];var W=b.eventMetadata||{};W.hasOwnProperty(Q.A.yd)||(W[Q.A.yd]=!b.fromContainerExecution);b.eventMetadata=W;delete d[J.m.ef];for(var ia=t?[e.id]:Lm(),ka=0;ka<ia.length;ka++){var Y=
d,X=ia[ka],ja=md(b,null),wa=Op(X,ja.isGtmEvent);wa&&Oq.push("config",[Y],wa,ja)}}}}},consent:function(a,b){if(a.length===3){L(39);var c=xD(a,b),d=a[1],e={},f=No(a[2]),g;for(g in f)if(f.hasOwnProperty(g)){var h=f[g];e[g]=g===J.m.sg?Array.isArray(h)?NaN:Number(h):g===J.m.hc?(Array.isArray(h)?h:[h]).map(Oo):Po(h)}b.fromContainerExecution||(e[J.m.V]&&L(139),e[J.m.Ka]&&L(140));d==="default"?qp(e):d==="update"?sp(e,c):d==="declare"&&b.fromContainerExecution&&pp(e)}},event:function(a,b){var c=a[1];if(!(a.length<
2)&&mb(c)){var d=void 0;if(a.length>2){if(!ld(a[2])&&a[2]!==void 0||a.length>3)return;d=a[2]}var e=wD(c,d),f=xD(a,b),g=f.eventId,h=f.priorityId;e["gtm.uniqueEventId"]=g;h&&(e["gtm.priorityId"]=h);if(c==="optimize.callback")return e.eventModel=e.eventModel||{},e;var m=yD(d,b);if(m){for(var n=m.sj,p=m.Ho,q=p.map(function(M){return M.id}),r=p.map(function(M){return M.destinationId}),t=n.map(function(M){return M.id}),u=l(Lm()),v=u.next();!v.done;v=u.next()){var w=v.value;r.indexOf(w)<0&&t.push(w)}yC(g,
c);for(var y=l(t),z=y.next();!z.done;z=y.next()){var C=z.value,D=md(b,null),G=md(d,null);delete G[J.m.ef];var I=D.eventMetadata||{};I.hasOwnProperty(Q.A.yd)||(I[Q.A.yd]=!D.fromContainerExecution);I[Q.A.Ji]=q.slice();I[Q.A.Pf]=r.slice();D.eventMetadata=I;Pq(c,G,C,D)}e.eventModel=e.eventModel||{};q.length>0?e.eventModel[J.m.pd]=q.join(","):delete e.eventModel[J.m.pd];uD||L(43);b.noGtmEvent===void 0&&b.eventMetadata&&b.eventMetadata[Q.A.Hl]&&(b.noGtmEvent=!0);e.eventModel[J.m.Kc]&&(b.noGtmEvent=!0);
return b.noGtmEvent?void 0:e}}},get:function(a,b){L(53);if(a.length===4&&mb(a[1])&&mb(a[2])&&lb(a[3])){var c=Op(a[1],b.isGtmEvent),d=String(a[2]),e=a[3];if(c){uD||L(43);var f=zD();if(pb(Lm(),function(h){return c.destinationId===h})){xD(a,b);var g={};md((g[J.m.rc]=d,g[J.m.Ic]=e,g),null);Qq(d,function(h){Nc(function(){e(h)})},c.id,b)}else XB(c.destinationId,f,{source:4,fromContainerExecution:b.fromContainerExecution})}}},js:function(a,b){if(a.length===2&&a[1].getTime){uD=!0;var c=xD(a,b),d=c.eventId,
e=c.priorityId,f={};return f.event="gtm.js",f["gtm.start"]=a[1].getTime(),f["gtm.uniqueEventId"]=d,f["gtm.priorityId"]=e,f}},policy:function(a){if(a.length===3&&mb(a[1])&&lb(a[2])){if(ig(a[1],a[2]),L(74),a[1]==="all"){L(75);var b=!1;try{b=a[2](lg.ctid,"unknown",{})}catch(c){}b||L(76)}}else L(73)},set:function(a,b){var c=void 0;a.length===2&&ld(a[1])?c=md(a[1],null):a.length===3&&mb(a[1])&&(c={},ld(a[2])||Array.isArray(a[2])?c[a[1]]=md(a[2],null):c[a[1]]=a[2]);if(c){var d=xD(a,b),e=d.eventId,f=d.priorityId;
md(c,null);var g=md(c,null);Oq.push("set",[g],void 0,b);c["gtm.uniqueEventId"]=e;f&&(c["gtm.priorityId"]=f);delete c.event;b.overwriteModelFields=!0;return c}}},ED={policy:!0};var GD=function(a){if(FD(a))return a;this.value=a};GD.prototype.getUntrustedMessageValue=function(){return this.value};var FD=function(a){return!a||jd(a)!=="object"||ld(a)?!1:"getUntrustedMessageValue"in a};GD.prototype.getUntrustedMessageValue=GD.prototype.getUntrustedMessageValue;var HD=!1,ID=[];function JD(){if(!HD){HD=!0;for(var a=0;a<ID.length;a++)Nc(ID[a])}}function KD(a){HD?Nc(a):ID.push(a)};var LD=0,MD={},ND=[],OD=[],PD=!1,QD=!1;function RD(a,b){return a.messageContext.eventId-b.messageContext.eventId||a.messageContext.priorityId-b.messageContext.priorityId}function SD(a,b,c){a.eventCallback=b;c&&(a.eventTimeout=c);return TD(a)}function UD(a,b){if(!nb(b)||b<0)b=0;var c=Dp[ek],d=0,e=!1,f=void 0;f=x.setTimeout(function(){e||(e=!0,a());f=void 0},b);return function(){var g=c?c.subscribers:1;++d===g&&(f&&(x.clearTimeout(f),f=void 0),e||(a(),e=!0))}}
function VD(a){if(a==null||typeof a!=="object")return!1;if(a.event)return!0;if(ub(a)){var b=a[0];if(b==="config"||b==="event"||b==="js"||b==="get")return!0}return!1}
function WD(){var a;if(OD.length)a=OD.shift();else if(ND.length)a=ND.shift();else return;var b;var c=a;if(PD||!VD(c.message))b=c;else{PD=!0;var d=c.message["gtm.uniqueEventId"],e,f;typeof d==="number"?(e=d-2,f=d-1):(e=Hp(),f=Hp(),c.message["gtm.uniqueEventId"]=Hp());var g={},h={message:(g.event="gtm.init_consent",g["gtm.uniqueEventId"]=e,g),messageContext:{eventId:e}},m={},n={message:(m.event="gtm.init",m["gtm.uniqueEventId"]=f,m),messageContext:{eventId:f}};ND.unshift(n,c);b=h}return b}
function XD(){for(var a=!1,b;!QD&&(b=WD());){QD=!0;delete zk.eventModel;Bk();var c=b,d=c.message,e=c.messageContext;if(d==null)QD=!1;else{e.fromContainerExecution&&Gk();try{if(lb(d))try{d.call(Dk)}catch(G){}else if(Array.isArray(d)){if(mb(d[0])){var f=d[0].split("."),g=f.pop(),h=d.slice(1),m=Ck(f.join("."),2);if(m!=null)try{m[g].apply(m,h)}catch(G){}}}else{var n=void 0;if(ub(d))a:{if(d.length&&mb(d[0])){var p=DD[d[0]];if(p&&(!e.fromContainerExecution||!ED[d[0]])){n=p(d,e);break a}}n=void 0}else n=
d;if(n){var q;for(var r=n,t=r._clear||e.overwriteModelFields,u=l(Object.keys(r)),v=u.next();!v.done;v=u.next()){var w=v.value;w!=="_clear"&&(t&&Fk(w),Fk(w,r[w]))}pk||(pk=r["gtm.start"]);var y=r["gtm.uniqueEventId"];r.event?(typeof y!=="number"&&(y=Hp(),r["gtm.uniqueEventId"]=y,Fk("gtm.uniqueEventId",y)),q=cD(r)):q=!1;a=q||a}}}finally{e.fromContainerExecution&&Bk(!0);var z=d["gtm.uniqueEventId"];if(typeof z==="number"){for(var C=MD[String(z)]||[],D=0;D<C.length;D++)OD.push(YD(C[D]));C.length&&OD.sort(RD);
delete MD[String(z)];z>LD&&(LD=z)}QD=!1}}}return!a}
function ZD(){if(E(109)){var a=!Xj.ma;}var c=XD();if(E(109)){}try{var e=lg.ctid,f=x[ek].hide;if(f&&f[e]!==void 0&&f.end){f[e]=
!1;var g=!0,h;for(h in f)if(f.hasOwnProperty(h)&&f[h]===!0){g=!1;break}g&&(f.end(),f.end=null)}}catch(m){}return c}function cx(a){if(LD<a.notBeforeEventId){var b=String(a.notBeforeEventId);MD[b]=MD[b]||[];MD[b].push(a)}else OD.push(YD(a)),OD.sort(RD),Nc(function(){QD||XD()})}function YD(a){return{message:a.message,messageContext:a.messageContext}}
function $D(){function a(f){var g={};if(FD(f)){var h=f;f=FD(h)?h.getUntrustedMessageValue():void 0;g.fromContainerExecution=!0}return{message:f,messageContext:g}}var b=yc(ek,[]),c=Dp[ek]=Dp[ek]||{};c.pruned===!0&&L(83);MD=ax().get();bx();oD(function(){if(!c.gtmDom){c.gtmDom=!0;var f={};b.push((f.event="gtm.dom",f))}});KD(function(){if(!c.gtmLoad){c.gtmLoad=!0;var f={};b.push((f.event="gtm.load",f))}});c.subscribers=(c.subscribers||0)+1;var d=b.push;b.push=function(){var f;if(Dp.SANDBOXED_JS_SEMAPHORE>
0){f=[];for(var g=0;g<arguments.length;g++)f[g]=new GD(arguments[g])}else f=[].slice.call(arguments,0);var h=f.map(function(q){return a(q)});ND.push.apply(ND,h);var m=d.apply(b,f),n=Math.max(100,Number("1000")||300);if(this.length>n)for(L(4),c.pruned=!0;this.length>n;)this.shift();var p=typeof m!=="boolean"||m;return XD()&&p};var e=b.slice(0).map(function(f){return a(f)});ND.push.apply(ND,e);if(!Xj.ma){if(E(109)){}Nc(ZD)}}var TD=function(a){return x[ek].push(a)};function aE(a){TD(a)};function bE(){var a,b=cl(x.location.href);(a=b.hostname+b.pathname)&&Sn("dl",encodeURIComponent(a));var c;var d=lg.ctid;if(d){var e=Jm.qe?1:0,f,g=Pm(Em());f=g&&g.context;c=d+";"+lg.canonicalContainerId+";"+(f&&f.fromContainerExecution?1:0)+";"+(f&&f.source||0)+";"+e}else c=void 0;var h=c;h&&Sn("tdp",h);var m=Sl(!0);m!==void 0&&Sn("frm",String(m))};var cE={},dE=void 0;
function eE(){if($o()||sl)Sn("csp",function(){return Object.keys(cE).join("~")||void 0},!1),x.addEventListener("securitypolicyviolation",function(a){if(a.disposition==="enforce"){L(179);var b=qm(a.effectiveDirective);if(b){var c;var d=om(b,a.blockedURI);c=d?mm[b][d]:void 0;if(c){var e;a:{try{var f=new URL(a.blockedURI),g=f.pathname.indexOf(";");e=g>=0?f.origin+f.pathname.substring(0,g):f.origin+f.pathname;break a}catch(v){}e=void 0}var h=e;if(h){for(var m=l(c),n=m.next();!n.done;n=m.next()){var p=
n.value;if(!p.Bm){p.Bm=!0;if(E(59)){var q={eventId:p.eventId,priorityId:p.priorityId};if($o()){var r=q,t={type:1,blockedUrl:h,endpoint:p.endpoint,violation:a.effectiveDirective};if($o()){var u=fp("TAG_DIAGNOSTICS",{eventId:r==null?void 0:r.eventId,priorityId:r==null?void 0:r.priorityId});u.tagDiagnostics=t;Zo(u)}}}fE(p.endpoint)}}pm(b,a.blockedURI)}}}}})}
function fE(a){var b=String(a);cE.hasOwnProperty(b)||(cE[b]=!0,Tn("csp",!0),dE===void 0&&E(171)&&(dE=x.setTimeout(function(){if(E(171)){var c=Pn.csp;Pn.csp=!0;Pn.seq=!1;var d=Un(!1);Pn.csp=c;Pn.seq=!0;Gc(d+"&script=1")}dE=void 0},500)))};function gE(){var a;var b=Om();if(b)if(b.canonicalContainerId)a=b.canonicalContainerId;else{var c,d=b.scriptContainerId||((c=b.destinations)==null?void 0:c[0]);a=d?"_"+d:void 0}else a=void 0;var e=a;e&&Sn("pcid",e)};var hE=/^(https?:)?\/\//;
function iE(){var a=Qm();if(a){var b;a:{var c,d=(c=a.scriptElement)==null?void 0:c.src;if(d){var e;try{var f;e=(f=ad())==null?void 0:f.getEntriesByType("resource")}catch(q){}if(e){for(var g=-1,h=l(e),m=h.next();!m.done;m=h.next()){var n=m.value;if(n.initiatorType==="script"&&(g+=1,n.name.replace(hE,"")===d.replace(hE,""))){b=g;break a}}L(146)}else L(145)}b=void 0}var p=b;p!==void 0&&(a.canonicalContainerId&&Sn("rtg",String(a.canonicalContainerId)),Sn("slo",String(p)),Sn("hlo",a.htmlLoadOrder||"-1"),
Sn("lst",String(a.loadScriptType||"0")))}else L(144)};function jE(){var a=[],b=Number('')||0,c=Number('0.0')||0;c||(c=b/100);var d=function(){var w=!1;return w}();a.push({ng:21,studyId:21,experimentId:105102050,controlId:105102051,controlId2:105102052,probability:c,active:d,ye:0});var e=Number('')||
0,f=Number('0.001')||0;f||(f=e/100);var g=function(){var w=!1;return w}();a.push({ng:219,studyId:219,experimentId:104948811,controlId:104948812,controlId2:0,probability:f,active:g,ye:0});var h=Number('')||
0,m=Number('1')||0;m||(m=h/100);var n=function(){var w=!1;return w}();a.push({ng:220,studyId:220,experimentId:104948813,controlId:104948814,controlId2:0,probability:m,active:n,ye:0});var p=
Number('')||0,q=Number('0.01')||0;q||(q=p/100);var r=function(){var w=!1;return w}();a.push({ng:195,studyId:195,experimentId:104527906,controlId:104527907,controlId2:104898015,probability:q,active:r,ye:1});var t=Number('')||0,u=Number('0.01')||
0;u||(u=t/100);var v=function(){var w=!1;return w}();a.push({ng:196,studyId:196,experimentId:104528500,controlId:104528501,controlId2:104898016,probability:u,active:v,ye:0});return a};var kE={};function lE(a){for(var b=l(Object.keys(a.exp||{})),c=b.next();!c.done;c=b.next())Xj.R.H.add(Number(c.value))}function mE(a){var b=Hn(Cn.Z.sl);return!!mi[a].active||mi[a].probability>.5||!!(b.exp||{})[mi[a].experimentId]||!!mi[a].active||mi[a].probability>.5||!!(kE.exp||{})[mi[a].experimentId]}
function nE(){for(var a=l(jE()),b=a.next();!b.done;b=a.next()){var c=b.value,d=c.ng;mi[d]=c;if(c.ye===1){var e=d,f=Hn(Cn.Z.sl);qi(f,e);lE(f);mE(e)&&B(e)}else if(c.ye===0){var g=d,h=kE;qi(h,g);lE(h);mE(g)&&B(g)}}};

function IE(){};var JE=function(){};JE.prototype.toString=function(){return"undefined"};var KE=new JE;function RE(){E(212)&&lk&&(ig("all",function(a,b,c){var d=c.options;switch(b){case "detect_link_click_events":case "detect_form_submit_events":return(d==null?void 0:d.waitForTags)!==!0;case "detect_youtube_activity_events":return(d==null?void 0:d.fixMissingApi)!==!0;default:return!0}}),LC(Mm(),function(a){var b,c;b=a.entityId;c=a.securityGroups;var d="__"+b;return bC(d,5)||!(!Lf[d]||!Lf[d][5])||c.includes("cmpPartners")}))};function SE(a,b){function c(g){var h=cl(g),m=Xk(h,"protocol"),n=Xk(h,"host",!0),p=Xk(h,"port"),q=Xk(h,"path").toLowerCase().replace(/\/$/,"");if(m===void 0||m==="http"&&p==="80"||m==="https"&&p==="443")m="web",p="default";return[m,n,p,q]}for(var d=c(String(a)),e=c(String(b)),f=0;f<d.length;f++)if(d[f]!==e[f])return!1;return!0}
function TE(a){return UE(a)?1:0}
function UE(a){var b=a.arg0,c=a.arg1;if(a.any_of&&Array.isArray(c)){for(var d=0;d<c.length;d++){var e=md(a,{});md({arg1:c[d],any_of:void 0},e);if(TE(e))return!0}return!1}switch(a["function"]){case "_cn":return Rg(b,c);case "_css":var f;a:{if(b)try{for(var g=0;g<Mg.length;g++){var h=Mg[g];if(b[h]!=null){f=b[h](c);break a}}}catch(m){}f=!1}return f;case "_ew":return Ng(b,c);case "_eq":return Sg(b,c);case "_ge":return Tg(b,c);case "_gt":return Vg(b,c);case "_lc":return Og(b,c);case "_le":return Ug(b,
c);case "_lt":return Wg(b,c);case "_re":return Qg(b,c,a.ignore_case);case "_sw":return Xg(b,c);case "_um":return SE(b,c)}return!1};[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});var VE=function(a,b,c,d){fr.call(this);this.hh=b;this.Lf=c;this.sb=d;this.Va=new Map;this.ih=0;this.ma=new Map;this.Da=new Map;this.R=void 0;this.H=a};va(VE,fr);VE.prototype.N=function(){delete this.C;this.Va.clear();this.ma.clear();this.Da.clear();this.R&&(br(this.H,"message",this.R),delete this.R);delete this.H;delete this.sb;fr.prototype.N.call(this)};
var WE=function(a){if(a.C)return a.C;a.Lf&&a.Lf(a.H)?a.C=a.H:a.C=Rl(a.H,a.hh);var b;return(b=a.C)!=null?b:null},YE=function(a,b,c){if(WE(a))if(a.C===a.H){var d=a.Va.get(b);d&&d(a.C,c)}else{var e=a.ma.get(b);if(e&&e.rj){XE(a);var f=++a.ih;a.Da.set(f,{Fh:e.Fh,Yo:e.hm(c),persistent:b==="addEventListener"});a.C.postMessage(e.rj(c,f),"*")}}},XE=function(a){a.R||(a.R=function(b){try{var c;c=a.sb?a.sb(b):void 0;if(c){var d=c.hq,e=a.Da.get(d);if(e){e.persistent||a.Da.delete(d);var f;(f=e.Fh)==null||f.call(e,
e.Yo,c.payload)}}}catch(g){}},ar(a.H,"message",a.R))};var ZE=function(a,b){var c=b.listener,d=(0,a.__gpp)("addEventListener",c);d&&c(d,!0)},$E=function(a,b){(0,a.__gpp)("removeEventListener",b.listener,b.listenerId)},aF={hm:function(a){return a.listener},rj:function(a,b){var c={};return c.__gppCall={callId:b,command:"addEventListener",version:"1.1"},c},Fh:function(a,b){var c=b.__gppReturn;a(c.returnValue,c.success)}},bF={hm:function(a){return a.listener},rj:function(a,b){var c={};return c.__gppCall={callId:b,command:"removeEventListener",version:"1.1",
parameter:a.listenerId},c},Fh:function(a,b){var c=b.__gppReturn,d=c.returnValue.data;a==null||a(d,c.success)}};function cF(a){var b={};typeof a.data==="string"?b=JSON.parse(a.data):b=a.data;return{payload:b,hq:b.__gppReturn.callId}}
var dF=function(a,b){var c;c=(b===void 0?{}:b).timeoutMs;fr.call(this);this.caller=new VE(a,"__gppLocator",function(d){return typeof d.__gpp==="function"},cF);this.caller.Va.set("addEventListener",ZE);this.caller.ma.set("addEventListener",aF);this.caller.Va.set("removeEventListener",$E);this.caller.ma.set("removeEventListener",bF);this.timeoutMs=c!=null?c:500};va(dF,fr);dF.prototype.N=function(){this.caller.dispose();fr.prototype.N.call(this)};
dF.prototype.addEventListener=function(a){var b=this,c=ul(function(){a(eF,!0)}),d=this.timeoutMs===-1?void 0:setTimeout(function(){c()},this.timeoutMs);YE(this.caller,"addEventListener",{listener:function(e,f){clearTimeout(d);try{var g;var h;((h=e.pingData)==null?void 0:h.gppVersion)===void 0||e.pingData.gppVersion==="1"||e.pingData.gppVersion==="1.0"?(b.removeEventListener(e.listenerId),g={eventName:"signalStatus",data:"ready",pingData:{internalErrorState:1,gppString:"GPP_ERROR_STRING_IS_DEPRECATED_SPEC",
applicableSections:[-1]}}):Array.isArray(e.pingData.applicableSections)?g=e:(b.removeEventListener(e.listenerId),g={eventName:"signalStatus",data:"ready",pingData:{internalErrorState:2,gppString:"GPP_ERROR_STRING_EXPECTED_APPLICATION_SECTION_ARRAY",applicableSections:[-1]}});a(g,f)}catch(m){if(e==null?0:e.listenerId)try{b.removeEventListener(e.listenerId)}catch(n){a(fF,!0);return}a(gF,!0)}}})};
dF.prototype.removeEventListener=function(a){YE(this.caller,"removeEventListener",{listener:function(){},listenerId:a})};
var gF={eventName:"signalStatus",data:"ready",pingData:{internalErrorState:2,gppString:"GPP_ERROR_STRING_UNAVAILABLE",applicableSections:[-1]},listenerId:-1},eF={eventName:"signalStatus",data:"ready",pingData:{gppString:"GPP_ERROR_STRING_LISTENER_REGISTRATION_TIMEOUT",internalErrorState:2,applicableSections:[-1]},listenerId:-1},fF={eventName:"signalStatus",data:"ready",pingData:{gppString:"GPP_ERROR_STRING_REMOVE_EVENT_LISTENER_ERROR",internalErrorState:2,applicableSections:[-1]},listenerId:-1};function hF(a){var b;if(!(b=a.pingData.signalStatus==="ready")){var c=a.pingData.applicableSections;b=!c||c.length===1&&c[0]===-1}if(b){Jv.gppString=a.pingData.gppString;var d=a.pingData.applicableSections.join(",");Jv.C=d}}function iF(){try{var a=new dF(x,{timeoutMs:-1});WE(a.caller)&&a.addEventListener(hF)}catch(b){}};function jF(){var a=[["cv",$i(1)],["rv",ck],["tc",Jf.filter(function(b){return b}).length]];dk&&a.push(["x",dk]);uk()&&a.push(["tag_exp",uk()]);return a};var kF={},lF={};function cj(a){kF[a]=(kF[a]||0)+1}function dj(a){lF[a]=(lF[a]||0)+1}function mF(a,b){for(var c=[],d=l(Object.keys(b)),e=d.next();!e.done;e=d.next()){var f=e.value;c.push(f+"."+b[f])}return c.length===0?[]:[[a,c.join("~")]]}function nF(){return mF("bdm",kF)}function oF(){return mF("vcm",lF)};var pF={},qF={};function rF(a){var b=a.eventId,c=a.Qd,d=[],e=pF[b]||[];e.length&&d.push(["hf",e.join(".")]);var f=qF[b]||[];f.length&&d.push(["ht",f.join(".")]);c&&(delete pF[b],delete qF[b]);return d};function sF(){return!1}function tF(){var a={};return function(b,c,d){}};function uF(){var a=vF;return function(b,c,d){var e=d&&d.event;wF(c);var f=Ch(b)?void 0:1,g=new Ya;tb(c,function(r,t){var u=Cd(t,void 0,f);u===void 0&&t!==void 0&&L(44);g.set(r,u)});a.Ob(ag());var h={Rl:pg(b),eventId:e==null?void 0:e.id,priorityId:e!==void 0?e.priorityId:void 0,Sf:e!==void 0?function(r){e.Pc.Sf(r)}:void 0,Kb:function(){return b},log:function(){},kp:{index:d==null?void 0:d.index,type:d==null?void 0:d.type,name:d==null?void 0:d.name},rq:!!bC(b,3),originalEventData:e==null?void 0:e.originalEventData};
e&&e.cachedModelValues&&(h.cachedModelValues={gtm:e.cachedModelValues.gtm,ecommerce:e.cachedModelValues.ecommerce});if(sF()){var m=tF(),n,p;h.yb={Gj:[],Tf:{},bc:function(r,t,u){t===1&&(n=r);t===7&&(p=u);m(r,t,u)},Dh:Uh()};h.log=function(r){var t=Ca.apply(1,arguments);n&&m(n,4,{level:r,source:p,message:t})}}var q=$e(a,h,[b,g]);a.Ob();q instanceof Fa&&(q.type==="return"?q=q.data:q=void 0);return Bd(q,void 0,f)}}function wF(a){var b=a.gtmOnSuccess,c=a.gtmOnFailure;lb(b)&&(a.gtmOnSuccess=function(){Nc(b)});lb(c)&&(a.gtmOnFailure=function(){Nc(c)})};function xF(a){}xF.M="internal.addAdsClickIds";function yF(a,b){var c=this;}yF.publicName="addConsentListener";var zF=!1;function AF(a){for(var b=0;b<a.length;++b)if(zF)try{a[b]()}catch(c){L(77)}else a[b]()}function BF(a,b,c){var d=this,e;if(!nh(a)||!jh(b)||!oh(c))throw F(this.getName(),["string","function","string|undefined"],arguments);AF([function(){H(d,"listen_data_layer",a)}]);e=bD().addListener(a,Bd(b),c===null?void 0:c);return e}BF.M="internal.addDataLayerEventListener";function CF(a,b,c){}CF.publicName="addDocumentEventListener";function DF(a,b,c,d){}DF.publicName="addElementEventListener";function EF(a){return a.K.wb()};function FF(a){}FF.publicName="addEventCallback";
var GF=function(a){return typeof a==="string"?a:String(Hp())},JF=function(a,b){HF(a,"init",!1)||(IF(a,"init",!0),b())},HF=function(a,b,c){var d=KF(a);return Bb(d,b,c)},LF=function(a,b,c,d){var e=KF(a),f=Bb(e,b,d);e[b]=c(f)},IF=function(a,b,c){KF(a)[b]=c},KF=function(a){var b=Ep("autoEventsSettings",function(){return{}});b.hasOwnProperty(a)||(b[a]={});return b[a]},MF=function(a,b,c){var d={event:b,"gtm.element":a,"gtm.elementClasses":Yc(a,"className"),"gtm.elementId":a.for||Oc(a,"id")||"","gtm.elementTarget":a.formTarget||
Yc(a,"target")||""};c&&(d["gtm.triggers"]=c.join(","));d["gtm.elementUrl"]=(a.attributes&&a.attributes.formaction?a.formAction:"")||a.action||Yc(a,"href")||a.src||a.code||a.codebase||"";return d};
var PF=function(a,b,c){if(!a.elements)return 0;for(var d=b.dataset[c],e=0,f=1;e<a.elements.length;e++){var g=a.elements[e],h=g.tagName.toLowerCase();if(!(NF.indexOf(h)<0||h==="input"&&OF.indexOf(g.type.toLowerCase())>=0)){if(g.dataset[c]===d)return f;f++}}return 0},QF=function(a){if(a.form){var b;return((b=a.form)==null?0:b.tagName)?a.form:A.getElementById(a.form)}return Rc(a,["form"],100)},NF=["input","select","textarea"],OF=["button","hidden","image","reset","submit"];
function UF(a){}UF.M="internal.addFormAbandonmentListener";function VF(a,b,c,d){}
VF.M="internal.addFormData";var WF={},XF=[],YF={},ZF=0,$F=0;
var bG=function(){Lc(A,"change",function(a){for(var b=0;b<XF.length;b++)XF[b](a)});Lc(x,"pagehide",function(){aG()})},aG=function(){tb(YF,function(a,b){var c=WF[a];c&&tb(b,function(d,e){cG(e,c)})})},fG=function(a,b){var c=""+a;if(WF[c])WF[c].push(b);else{var d=[b];WF[c]=d;var e=YF[c];e||(e={},YF[c]=e);XF.push(function(f){var g=f.target;if(g){var h=QF(g);if(h){var m=dG(h,"gtmFormInteractId",function(){return ZF++}),n=dG(g,"gtmFormInteractFieldId",function(){return $F++}),p=e[m];p?(p.Xa&&(x.clearTimeout(p.Xa),
p.fc.dataset.gtmFormInteractFieldId!==n&&cG(p,d)),p.fc=g,eG(p,d,a)):(e[m]={form:h,fc:g,sequenceNumber:0,Xa:null},eG(e[m],d,a))}}})}},cG=function(a,b){var c=a.form,d=a.fc,e=MF(c,"gtm.formInteract"),f=c.action;f&&f.tagName&&(f=c.cloneNode(!1).action);e["gtm.elementUrl"]=f;e["gtm.interactedFormName"]=c.getAttribute("name");e["gtm.interactedFormLength"]=c.length;e["gtm.interactedFormField"]=d;e["gtm.interactedFormFieldPosition"]=PF(c,d,"gtmFormInteractFieldId");e["gtm.interactSequenceNumber"]=a.sequenceNumber;
e["gtm.interactedFormFieldId"]=d.id;e["gtm.interactedFormFieldName"]=d.getAttribute("name");e["gtm.interactedFormFieldType"]=d.getAttribute("type");for(var g=0;g<b.length;g++)b[g](e);a.sequenceNumber++;a.Xa=null},eG=function(a,b,c){c?a.Xa=x.setTimeout(function(){cG(a,b)},c):cG(a,b)},dG=function(a,b,c){var d=a.dataset[b];if(d)return d;d=String(c());return a.dataset[b]=d};
function gG(a,b){if(!jh(a)||!hh(b))throw F(this.getName(),["function","Object|undefined"],arguments);var c=Bd(b)||{},d=Number(c.interval);if(!d||d<0)d=0;var e=Bd(a),f;HF("pix.fil","init")?f=HF("pix.fil","reg"):(bG(),f=fG,IF("pix.fil","reg",fG),IF("pix.fil","init",!0));f(d,e);}gG.M="internal.addFormInteractionListener";
var iG=function(a,b,c){var d=MF(a,"gtm.formSubmit");d["gtm.interactedFormName"]=a.getAttribute("name");d["gtm.interactedFormLength"]=a.length;d["gtm.willOpenInCurrentWindow"]=!b&&hG(a);c&&c.value&&(d["gtm.formSubmitButtonText"]=c.value);var e=a.action;e&&e.tagName&&(e=a.cloneNode(!1).action);d["gtm.elementUrl"]=e;d["gtm.formCanceled"]=b;return d},jG=function(a,b){var c=HF("pix.fsl",a?"nv.mwt":"mwt",0);x.setTimeout(b,c)},kG=function(a,b,c,d,e){var f=HF("pix.fsl",c?"nv.mwt":"mwt",0),g=HF("pix.fsl",
c?"runIfCanceled":"runIfUncanceled",[]);if(!g.length)return!0;var h=iG(a,c,e);L(121);if(h["gtm.elementUrl"]==="https://www.facebook.com/tr/")return L(122),!0;if(d&&f){for(var m=Lb(b,g.length),n=0;n<g.length;++n)g[n](h,m);return m.done}for(var p=0;p<g.length;++p)g[p](h,function(){});return!0},lG=function(){var a=[],b=function(c){return pb(a,function(d){return d.form===c})};return{store:function(c,d){var e=b(c);e?e.button=d:a.push({form:c,button:d})},get:function(c){var d=b(c);return d?d.button:null}}},
hG=function(a){var b=Yc(a,"target");return b&&b!=="_self"&&b!=="_parent"&&b!=="_top"?!1:!0},mG=function(){var a=lG(),b=HTMLFormElement.prototype.submit;Lc(A,"click",function(c){var d=c.target;if(d){var e=Rc(d,["button","input"],100);if(e&&(e.type==="submit"||e.type==="image")&&e.name&&Oc(e,"value")){var f=QF(e);f&&a.store(f,e)}}},!1);Lc(A,"submit",function(c){var d=c.target;if(!d)return c.returnValue;var e=c.defaultPrevented||c.returnValue===!1,f=hG(d)&&!e,g=a.get(d),h=!0,m=function(){if(h){var n,
p={};g&&(n=A.createElement("input"),n.type="hidden",n.name=g.name,n.value=g.value,d.appendChild(n),g.getAttribute("formaction")&&(p.action=d.getAttribute("action"),lc(d,g.getAttribute("formaction"))),g.hasAttribute("formenctype")&&(p.enctype=d.getAttribute("enctype"),d.setAttribute("enctype",g.getAttribute("formenctype"))),g.hasAttribute("formmethod")&&(p.method=d.getAttribute("method"),d.setAttribute("method",g.getAttribute("formmethod"))),g.hasAttribute("formvalidate")&&(p.validate=d.getAttribute("validate"),
d.setAttribute("validate",g.getAttribute("formvalidate"))),g.hasAttribute("formtarget")&&(p.target=d.getAttribute("target"),d.setAttribute("target",g.getAttribute("formtarget"))));b.call(d);n&&(d.removeChild(n),p.hasOwnProperty("action")&&lc(d,p.action),p.hasOwnProperty("enctype")&&d.setAttribute("enctype",p.enctype),p.hasOwnProperty("method")&&d.setAttribute("method",p.method),p.hasOwnProperty("validate")&&d.setAttribute("validate",p.validate),p.hasOwnProperty("target")&&d.setAttribute("target",
p.target))}};if(kG(d,m,e,f,g))return h=!1,c.returnValue;jG(e,m);e||(c.preventDefault&&c.preventDefault(),c.returnValue=!1);return!1},!1);HTMLFormElement.prototype.submit=function(){var c=this,d=!0,e=function(){d&&b.call(c)};kG(c,e,!1,hG(c))?(b.call(c),d=!1):jG(!1,e)}};
function nG(a,b){if(!jh(a)||!hh(b))throw F(this.getName(),["function","Object|undefined"],arguments);var c=Bd(b,this.K,1)||{},d=c.waitForCallbacks,e=c.waitForCallbacksTimeout,f=c.checkValidation;e=e&&e>0?e:2E3;var g=Bd(a,this.K,1);if(d){var h=function(n){return Math.max(e,n)};LF("pix.fsl","mwt",h,0);f||LF("pix.fsl","nv.mwt",h,0)}var m=function(n){n.push(g);return n};LF("pix.fsl","runIfUncanceled",m,[]);f||LF("pix.fsl","runIfCanceled",
m,[]);HF("pix.fsl","init")||(mG(),IF("pix.fsl","init",!0));}nG.M="internal.addFormSubmitListener";
function sG(a){}sG.M="internal.addGaSendListener";function tG(a){if(!a)return{};var b=a.kp;return aC(b.type,b.index,b.name)}function uG(a){return a?{originatingEntity:tG(a)}:{}};function CG(a){var b=Dp.zones;return b?b.getIsAllowedFn(Nm(),a):function(){return!0}}function DG(){var a=Dp.zones;a&&a.unregisterChild(Nm())}
function EG(){NC(Mm(),function(a){var b=Dp.zones;return b?b.isActive(Nm(),a.originalEventData["gtm.uniqueEventId"]):!0});LC(Mm(),function(a){var b,c;b=a.entityId;c=a.securityGroups;return CG(Number(a.originalEventData["gtm.uniqueEventId"]))(b,c)})};var FG=function(a,b){this.tagId=a;this.we=b};
function GG(a,b){var c=this;return a}GG.M="internal.loadGoogleTag";function HG(a){return new td("",function(b){var c=this.evaluate(b);if(c instanceof td)return new td("",function(){var d=Ca.apply(0,arguments),e=this,f=md(EF(this),null);f.eventId=a.eventId;f.priorityId=a.priorityId;f.originalEventData=a.originalEventData;var g=d.map(function(m){return e.evaluate(m)}),h=this.K.ub();h.Od(f);return c.Mb.apply(c,[h].concat(ya(g)))})})};function IG(a,b,c){var d=this;}IG.M="internal.addGoogleTagRestriction";var JG={},KG=[];
function RG(a,b){}
RG.M="internal.addHistoryChangeListener";function SG(a,b,c){}SG.publicName="addWindowEventListener";function TG(a,b){return!0}TG.publicName="aliasInWindow";function UG(a,b,c){}UG.M="internal.appendRemoteConfigParameter";function VG(a){var b;return b}
VG.publicName="callInWindow";function WG(a){}WG.publicName="callLater";function XG(a){}XG.M="callOnDomReady";function YG(a){}YG.M="callOnWindowLoad";function ZG(a,b){var c;return c}ZG.M="internal.computeGtmParameter";function $G(a,b){var c=this;}$G.M="internal.consentScheduleFirstTry";function aH(a,b){var c=this;}aH.M="internal.consentScheduleRetry";function bH(a){var b;return b}bH.M="internal.copyFromCrossContainerData";function cH(a,b){var c;var d=Cd(c,this.K,Ch(EF(this).Kb())?2:1);d===void 0&&c!==void 0&&L(45);return d}cH.publicName="copyFromDataLayer";
function dH(a){var b=void 0;return b}dH.M="internal.copyFromDataLayerCache";function eH(a){var b;return b}eH.publicName="copyFromWindow";function fH(a){var b=void 0;return Cd(b,this.K,1)}fH.M="internal.copyKeyFromWindow";var gH=function(a){return a===an.X.Fa&&tn[a]===$m.Ia.pe&&!P(J.m.U)};var hH=function(){return"0"},iH=function(a){if(typeof a!=="string")return"";var b=["gclid","dclid","wbraid","_gl"];E(102)&&b.push("gbraid");return dl(a,b,"0")};var jH={},kH={},lH={},mH={},nH={},oH={},pH={},qH={},rH={},sH={},tH={},uH={},vH={},wH={},xH={},yH={},zH={},AH={},BH={},CH={},DH={},EH={},FH={},GH={},HH={},IH={},JH=(IH[J.m.Ma]=(jH[2]=[gH],jH),IH[J.m.rf]=(kH[2]=[gH],kH),IH[J.m.ff]=(lH[2]=[gH],lH),IH[J.m.ni]=(mH[2]=[gH],mH),IH[J.m.oi]=(nH[2]=[gH],nH),IH[J.m.ri]=(oH[2]=[gH],oH),IH[J.m.si]=(pH[2]=[gH],pH),IH[J.m.ui]=(qH[2]=[gH],qH),IH[J.m.wc]=(rH[2]=[gH],rH),IH[J.m.tf]=(sH[2]=[gH],sH),IH[J.m.uf]=(tH[2]=[gH],tH),IH[J.m.vf]=(uH[2]=[gH],uH),IH[J.m.wf]=(vH[2]=
[gH],vH),IH[J.m.xf]=(wH[2]=[gH],wH),IH[J.m.yf]=(xH[2]=[gH],xH),IH[J.m.zf]=(yH[2]=[gH],yH),IH[J.m.Af]=(zH[2]=[gH],zH),IH[J.m.nb]=(AH[1]=[gH],AH),IH[J.m.Zc]=(BH[1]=[gH],BH),IH[J.m.gd]=(CH[1]=[gH],CH),IH[J.m.ae]=(DH[1]=[gH],DH),IH[J.m.Qe]=(EH[1]=[function(a){return E(102)&&gH(a)}],EH),IH[J.m.hd]=(FH[1]=[gH],FH),IH[J.m.Ca]=(GH[1]=[gH],GH),IH[J.m.Ya]=(HH[1]=[gH],HH),IH),KH={},LH=(KH[J.m.nb]=hH,KH[J.m.Zc]=hH,KH[J.m.gd]=hH,KH[J.m.ae]=hH,KH[J.m.Qe]=hH,KH[J.m.hd]=function(a){if(!ld(a))return{};var b=md(a,
null);delete b.match_id;return b},KH[J.m.Ca]=iH,KH[J.m.Ya]=iH,KH),MH={},NH={},OH=(NH[Q.A.jb]=(MH[2]=[gH],MH),NH),PH={};var QH=function(a,b,c,d){this.C=a;this.N=b;this.P=c;this.R=d};QH.prototype.getValue=function(a){a=a===void 0?an.X.Ib:a;if(!this.N.some(function(b){return b(a)}))return this.P.some(function(b){return b(a)})?this.R(this.C):this.C};QH.prototype.H=function(){return jd(this.C)==="array"||ld(this.C)?md(this.C,null):this.C};
var RH=function(){},SH=function(a,b){this.conditions=a;this.C=b},TH=function(a,b,c){var d,e=((d=a.conditions[b])==null?void 0:d[2])||[],f,g=((f=a.conditions[b])==null?void 0:f[1])||[];return new QH(c,e,g,a.C[b]||RH)},UH,VH;var WH=function(a,b,c){this.eventName=b;this.D=c;this.C={};this.isAborted=!1;this.target=a;this.metadata={};for(var d=c.eventMetadata||{},e=l(Object.keys(d)),f=e.next();!f.done;f=e.next()){var g=f.value;S(this,g,d[g])}},Zv=function(a,b){var c,d;return(c=a.C[b])==null?void 0:(d=c.getValue)==null?void 0:d.call(c,R(a,Q.A.Qf))},U=function(a,b,c){var d=a.C,e;c===void 0?e=void 0:(UH!=null||(UH=new SH(JH,LH)),e=TH(UH,b,c));d[b]=e};
WH.prototype.mergeHitDataForKey=function(a,b){var c,d,e;c=(d=this.C[a])==null?void 0:(e=d.H)==null?void 0:e.call(d);if(!c)return U(this,a,b),!0;if(!ld(c))return!1;U(this,a,ma(Object,"assign").call(Object,c,b));return!0};var XH=function(a,b){b=b===void 0?{}:b;for(var c=l(Object.keys(a.C)),d=c.next();!d.done;d=c.next()){var e=d.value,f=void 0,g=void 0,h=void 0;b[e]=(f=a.C[e])==null?void 0:(h=(g=f).H)==null?void 0:h.call(g)}return b};
WH.prototype.copyToHitData=function(a,b,c){var d=O(this.D,a);d===void 0&&(d=b);if(mb(d)&&c!==void 0&&E(92))try{d=c(d)}catch(e){}d!==void 0&&U(this,a,d)};
var R=function(a,b){var c=a.metadata[b];if(b===Q.A.Qf){var d;return c==null?void 0:(d=c.H)==null?void 0:d.call(c)}var e;return c==null?void 0:(e=c.getValue)==null?void 0:e.call(c,R(a,Q.A.Qf))},S=function(a,b,c){var d=a.metadata,e;c===void 0?e=c:(VH!=null||(VH=new SH(OH,PH)),e=TH(VH,b,c));d[b]=e},YH=function(a,b){b=b===void 0?{}:b;for(var c=l(Object.keys(a.metadata)),d=c.next();!d.done;d=c.next()){var e=d.value,f=void 0,g=void 0,h=void 0;b[e]=(f=a.metadata[e])==null?void 0:(h=(g=f).H)==null?void 0:
h.call(g)}return b},sw=function(a,b,c){var d=gx(a.target.destinationId);return d&&d[b]!==void 0?d[b]:c};function ZH(a,b){var c;if(!gh(a)||!hh(b))throw F(this.getName(),["Object","Object|undefined"],arguments);var d=Bd(b)||{},e=Bd(a,this.K,1).Cb(),f=e.D;d.omitEventContext&&(f=rq(new gq(e.D.eventId,e.D.priorityId)));var g=new WH(e.target,e.eventName,f);if(!d.omitHitData)for(var h=XH(e),m=l(Object.keys(h)),n=m.next();!n.done;n=m.next()){var p=n.value;U(g,p,h[p])}if(d.omitMetadata)g.metadata={};else for(var q=YH(e),r=l(Object.keys(q)),t=r.next();!t.done;t=
r.next()){var u=t.value;S(g,u,q[u])}g.isAborted=e.isAborted;c=Cd(Pw(g),this.K,1);return c}ZH.M="internal.copyPreHit";function $H(a,b){var c=null;return Cd(c,this.K,2)}$H.publicName="createArgumentsQueue";function aI(a){return Cd(function(c){var d=kC();if(typeof c==="function")d(function(){c(function(f,g,h){var m=
kC(),n=m&&m.getByName&&m.getByName(f);return(new x.gaplugins.Linker(n)).decorate(g,h)})});else if(Array.isArray(c)){var e=String(c[0]).split(".");b[e.length===1?e[0]:e[1]]&&d.apply(null,c)}else if(c==="isLoaded")return!!d.loaded},this.K,1)}aI.M="internal.createGaCommandQueue";function bI(a){return Cd(function(){if(!lb(e.push))throw Error("Object at "+a+" in window is not an array.");e.push.apply(e,Array.prototype.slice.call(arguments,0))},this.K,
Ch(EF(this).Kb())?2:1)}bI.publicName="createQueue";function cI(a,b){var c=null;if(!nh(a)||!oh(b))throw F(this.getName(),["string","string|undefined"],arguments);try{var d=(b||"").split("").filter(function(e){return"ig".indexOf(e)>=0}).join("");c=new yd(new RegExp(a,d))}catch(e){}return c}cI.M="internal.createRegex";function dI(a){}dI.M="internal.declareConsentState";function eI(a){var b="";return b}eI.M="internal.decodeUrlHtmlEntities";function fI(a,b,c){var d;return d}fI.M="internal.decorateUrlWithGaCookies";function gI(){}gI.M="internal.deferCustomEvents";function hI(a){var b;H(this,"detect_user_provided_data","auto");var c=Bd(a)||{},d=Gx({Be:!!c.includeSelector,Ce:!!c.includeVisibility,Vf:c.excludeElementSelectors,Zb:c.fieldFilters,Gh:!!c.selectMultipleElements});b=new Ya;var e=new pd;b.set("elements",e);for(var f=d.elements,g=0;g<f.length;g++)e.push(iI(f[g]));d.zj!==void 0&&b.set("preferredEmailElement",iI(d.zj));b.set("status",d.status);if(E(129)&&c.performDataLayerSearch&&!/Mobile|iPhone|iPad|iPod|Android|IEMobile/.test(uc&&
uc.userAgent||"")){}return b}
var jI=function(a){switch(a){case Ex.jc:return"email";case Ex.Cd:return"phone_number";case Ex.vd:return"first_name";case Ex.Bd:return"last_name";case Ex.Ni:return"street";case Ex.Jh:return"city";case Ex.Ii:return"region";case Ex.Nf:return"postal_code";case Ex.Ke:return"country"}},iI=function(a){var b=new Ya;b.set("userData",a.ka);b.set("tagName",a.tagName);a.querySelector!==void 0&&b.set("querySelector",a.querySelector);a.isVisible!==void 0&&b.set("isVisible",a.isVisible);if(E(33)){}else switch(a.type){case Ex.jc:b.set("type","email")}return b};hI.M="internal.detectUserProvidedData";
function mI(a,b){return f}mI.M="internal.enableAutoEventOnClick";var pI=function(a){if(!nI){var b=function(){var c=A.body;if(c)if(oI)(new MutationObserver(function(){for(var e=0;e<nI.length;e++)Nc(nI[e])})).observe(c,{childList:!0,subtree:!0});else{var d=!1;Lc(c,"DOMNodeInserted",function(){d||(d=!0,Nc(function(){d=!1;for(var e=0;e<nI.length;e++)Nc(nI[e])}))})}};nI=[];A.body?b():Nc(b)}nI.push(a)},oI=!!x.MutationObserver,nI;
function uI(a,b){return p}uI.M="internal.enableAutoEventOnElementVisibility";function vI(){}vI.M="internal.enableAutoEventOnError";var wI={},xI=[],yI={},zI=0,AI=0;
var CI=function(){tb(yI,function(a,b){var c=wI[a];c&&tb(b,function(d,e){BI(e,c)})})},FI=function(a,b){var c=""+b;if(wI[c])wI[c].push(a);else{var d=[a];wI[c]=d;var e=yI[c];e||(e={},yI[c]=e);xI.push(function(f){var g=f.target;if(g){var h=QF(g);if(h){var m=DI(h,"gtmFormInteractId",function(){return zI++}),n=DI(g,"gtmFormInteractFieldId",function(){return AI++});if(m!==null&&n!==null){var p=e[m];p?(p.Xa&&(x.clearTimeout(p.Xa),p.fc.getAttribute("data-gtm-form-interact-field-id")!==n&&BI(p,d)),p.fc=g,EI(p,
d,b)):(e[m]={form:h,fc:g,sequenceNumber:0,Xa:null},EI(e[m],d,b))}}}})}},BI=function(a,b){var c=a.form,d=a.fc,e=MF(c,"gtm.formInteract",b),f=c.action;f&&f.tagName&&(f=c.cloneNode(!1).action);e["gtm.elementUrl"]=f;e["gtm.interactedFormName"]=c.getAttribute("name")!=null?c.getAttribute("name"):void 0;e["gtm.interactedFormLength"]=c.length;e["gtm.interactedFormField"]=d;e["gtm.interactedFormFieldId"]=d.id;e["gtm.interactedFormFieldName"]=d.getAttribute("name")!=null?d.getAttribute("name"):void 0;e["gtm.interactedFormFieldPosition"]=
PF(c,d,"gtmFormInteractFieldId");e["gtm.interactedFormFieldType"]=d.getAttribute("type")!=null?d.getAttribute("type"):void 0;e["gtm.interactSequenceNumber"]=a.sequenceNumber;TD(e);a.sequenceNumber++;a.Xa=null},EI=function(a,b,c){c?a.Xa=x.setTimeout(function(){BI(a,b)},c):BI(a,b)},DI=function(a,b,c){var d;try{if(d=a.dataset[b])return d;d=String(c());a.dataset[b]=d}catch(e){d=null}return d};
function GI(a,b){var c=this;if(!hh(a))throw F(this.getName(),["Object|undefined","any"],arguments);AF([function(){H(c,"detect_form_interaction_events")}]);var d=GF(b),e=a&&Number(a.get("interval"));e>0&&isFinite(e)||(e=0);if(HF("fil","init",!1)){var f=HF("fil","reg");if(f)f(d,e);else throw Error("Failed to register trigger: "+d);}else Lc(A,"change",function(g){for(var h=0;h<xI.length;h++)xI[h](g)}),Lc(x,"pagehide",function(){CI()}),
FI(d,e),IF("fil","reg",FI),IF("fil","init",!0);return d}GI.M="internal.enableAutoEventOnFormInteraction";
var HI=function(a,b,c,d,e){var f=HF("fsl",c?"nv.mwt":"mwt",0),g;g=c?HF("fsl","nv.ids",[]):HF("fsl","ids",[]);if(!g.length)return!0;var h=MF(a,"gtm.formSubmit",g),m=a.action;m&&m.tagName&&(m=a.cloneNode(!1).action);L(121);if(m==="https://www.facebook.com/tr/")return L(122),!0;h["gtm.elementUrl"]=m;h["gtm.formCanceled"]=c;a.getAttribute("name")!=null&&(h["gtm.interactedFormName"]=a.getAttribute("name"));e&&(h["gtm.formSubmitElement"]=e,h["gtm.formSubmitElementText"]=e.value);if(d&&f){if(!SD(h,UD(b,
f),f))return!1}else SD(h,function(){},f||2E3);return!0},II=function(){var a=[],b=function(c){return pb(a,function(d){return d.form===c})};return{store:function(c,d){var e=b(c);e?e.button=d:a.push({form:c,button:d})},get:function(c){var d=b(c);if(d)return d.button}}},JI=function(a){var b=a.target;return b&&b!=="_self"&&b!=="_parent"&&b!=="_top"?!1:!0},KI=function(){var a=II(),b=HTMLFormElement.prototype.submit;Lc(A,"click",function(c){var d=c.target;if(d){var e=Rc(d,["button","input"],100);if(e&&(e.type===
"submit"||e.type==="image")&&e.name&&Oc(e,"value")){var f=QF(e);f&&a.store(f,e)}}},!1);Lc(A,"submit",function(c){var d=c.target;if(!d)return c.returnValue;var e=c.defaultPrevented||c.returnValue===!1,f=JI(d)&&!e,g=a.get(d),h=!0;if(HI(d,function(){if(h){var m=null,n={};g&&(m=A.createElement("input"),m.type="hidden",m.name=g.name,m.value=g.value,d.appendChild(m),g.hasAttribute("formaction")&&(n.action=d.getAttribute("action"),lc(d,g.getAttribute("formaction"))),g.hasAttribute("formenctype")&&(n.enctype=
d.getAttribute("enctype"),d.setAttribute("enctype",g.getAttribute("formenctype"))),g.hasAttribute("formmethod")&&(n.method=d.getAttribute("method"),d.setAttribute("method",g.getAttribute("formmethod"))),g.hasAttribute("formvalidate")&&(n.validate=d.getAttribute("validate"),d.setAttribute("validate",g.getAttribute("formvalidate"))),g.hasAttribute("formtarget")&&(n.target=d.getAttribute("target"),d.setAttribute("target",g.getAttribute("formtarget"))));b.call(d);m&&(d.removeChild(m),n.hasOwnProperty("action")&&
lc(d,n.action),n.hasOwnProperty("enctype")&&d.setAttribute("enctype",n.enctype),n.hasOwnProperty("method")&&d.setAttribute("method",n.method),n.hasOwnProperty("validate")&&d.setAttribute("validate",n.validate),n.hasOwnProperty("target")&&d.setAttribute("target",n.target))}},e,f,g))h=!1;else return e||(c.preventDefault&&c.preventDefault(),c.returnValue=!1),!1;return c.returnValue},!1);HTMLFormElement.prototype.submit=function(){var c=this,d=!0;HI(c,function(){d&&b.call(c)},!1,JI(c))&&(b.call(c),d=
!1)}};
function LI(a,b){var c=this;if(!hh(a))throw F(this.getName(),["Object|undefined","any"],arguments);var d=a&&a.get("waitForTags");AF([function(){H(c,"detect_form_submit_events",{waitForTags:!!d})}]);var e=a&&a.get("checkValidation"),f=GF(b);if(d){var g=Number(a.get("waitForTagsTimeout"));g>0&&isFinite(g)||(g=2E3);var h=function(n){return Math.max(g,n)};LF("fsl","mwt",h,0);e||LF("fsl","nv.mwt",h,0)}var m=function(n){n.push(f);
return n};LF("fsl","ids",m,[]);e||LF("fsl","nv.ids",m,[]);HF("fsl","init",!1)||(KI(),IF("fsl","init",!0));return f}LI.M="internal.enableAutoEventOnFormSubmit";
function QI(){var a=this;}QI.M="internal.enableAutoEventOnGaSend";var RI={},SI=[];
var UI=function(a,b){var c=""+b;if(RI[c])RI[c].push(a);else{var d=[a];RI[c]=d;var e=TI("gtm.historyChange-v2"),f=-1;SI.push(function(g){f>=0&&x.clearTimeout(f);b?f=x.setTimeout(function(){e(g,d);f=-1},b):e(g,d)})}},TI=function(a){var b=x.location.href,c={source:null,state:x.history.state||null,url:$k(cl(b)),ab:Xk(cl(b),"fragment")};return function(d,e){var f=c,g={};g[f.source]=!0;g[d.source]=!0;if(!g.popstate||!g.hashchange||f.ab!==d.ab){var h={event:a,"gtm.historyChangeSource":d.source,"gtm.oldUrlFragment":c.ab,
"gtm.newUrlFragment":d.ab,"gtm.oldHistoryState":c.state,"gtm.newHistoryState":d.state,"gtm.oldUrl":c.url,"gtm.newUrl":d.url};e&&(h["gtm.triggers"]=e.join(","));c=d;TD(h)}}},VI=function(a,b){var c=x.history,d=c[a];if(lb(d))try{c[a]=function(e,f,g){d.apply(c,[].slice.call(arguments,0));var h=x.location.href;b({source:a,state:e,url:$k(cl(h)),ab:Xk(cl(h),"fragment")})}}catch(e){}},XI=function(a){x.addEventListener("popstate",function(b){var c=WI(b);a({source:"popstate",state:b.state,url:$k(cl(c)),ab:Xk(cl(c),
"fragment")})})},YI=function(a){x.addEventListener("hashchange",function(b){var c=WI(b);a({source:"hashchange",state:null,url:$k(cl(c)),ab:Xk(cl(c),"fragment")})})},WI=function(a){var b,c;return((b=a.target)==null?void 0:(c=b.location)==null?void 0:c.href)||x.location.href};
function ZI(a,b){var c=this;if(!hh(a))throw F(this.getName(),["Object|undefined","any"],arguments);AF([function(){H(c,"detect_history_change_events")}]);var d=a&&a.get("useV2EventName")?"ehl":"hl",e=Number(a&&a.get("interval"));e>0&&isFinite(e)||(e=0);var f;if(!HF(d,"init",!1)){var g;d==="ehl"?(g=function(m){for(var n=0;n<SI.length;n++)SI[n](m)},f=GF(b),UI(f,e),IF(d,"reg",UI)):g=TI("gtm.historyChange");YI(g);XI(g);VI("pushState",
g);VI("replaceState",g);IF(d,"init",!0)}else if(d==="ehl"){var h=HF(d,"reg");h&&(f=GF(b),h(f,e))}d==="hl"&&(f=void 0);return f}ZI.M="internal.enableAutoEventOnHistoryChange";var $I=["http://","https://","javascript:","file://"];
var aJ=function(a,b){if(a.which===2||a.ctrlKey||a.shiftKey||a.altKey||a.metaKey)return!1;var c=Yc(b,"href");if(c.indexOf(":")!==-1&&!$I.some(function(h){return Gb(c,h)}))return!1;var d=c.indexOf("#"),e=Yc(b,"target");if(e&&e!=="_self"&&e!=="_parent"&&e!=="_top"||d===0)return!1;if(d>0){var f=$k(cl(c)),g=$k(cl(x.location.href));return f!==g}return!0},bJ=function(a,b){for(var c=Xk(cl((b.attributes&&b.attributes.formaction?b.formAction:"")||b.action||Yc(b,"href")||b.src||b.code||b.codebase||""),"host"),
d=0;d<a.length;d++)try{if((new RegExp(a[d])).test(c))return!1}catch(e){}return!0},cJ=function(){function a(c){var d=c.target;if(d&&c.which!==3&&!(c.C||c.timeStamp&&c.timeStamp===b)){b=c.timeStamp;d=Rc(d,["a","area"],100);if(!d)return c.returnValue;var e=c.defaultPrevented||c.returnValue===!1,f=HF("lcl",e?"nv.mwt":"mwt",0),g;g=e?HF("lcl","nv.ids",[]):HF("lcl","ids",[]);for(var h=[],m=0;m<g.length;m++){var n=g[m],p=HF("lcl","aff.map",{})[n];p&&!bJ(p,d)||h.push(n)}if(h.length){var q=aJ(c,d),r=MF(d,"gtm.linkClick",
h);r["gtm.elementText"]=Pc(d);r["gtm.willOpenInNewWindow"]=!q;if(q&&!e&&f&&d.href){var t=!!pb(String(Yc(d,"rel")||"").split(" "),function(y){return y.toLowerCase()==="noreferrer"}),u=x[(Yc(d,"target")||"_self").substring(1)],v=!0,w=UD(function(){var y;if(y=v&&u){var z;a:if(t){var C;try{C=new MouseEvent(c.type,{bubbles:!0})}catch(D){if(!A.createEvent){z=!1;break a}C=A.createEvent("MouseEvents");C.initEvent(c.type,!0,!0)}C.C=!0;c.target.dispatchEvent(C);z=!0}else z=!1;y=!z}y&&(u.location.href=Yc(d,
"href"))},f);if(SD(r,w,f))v=!1;else return c.preventDefault&&c.preventDefault(),c.returnValue=!1}else SD(r,function(){},f||2E3);return!0}}}var b=0;Lc(A,"click",a,!1);Lc(A,"auxclick",a,!1)};
function dJ(a,b){var c=this;if(!hh(a))throw F(this.getName(),["Object|undefined","any"],arguments);var d=Bd(a);AF([function(){H(c,"detect_link_click_events",d)}]);var e=d&&!!d.waitForTags,f=d&&!!d.checkValidation,g=d?d.affiliateDomains:void 0,h=GF(b);if(e){var m=Number(d.waitForTagsTimeout);m>0&&isFinite(m)||(m=2E3);var n=function(q){return Math.max(m,q)};LF("lcl","mwt",n,0);f||LF("lcl","nv.mwt",n,0)}var p=function(q){q.push(h);
return q};LF("lcl","ids",p,[]);f||LF("lcl","nv.ids",p,[]);g&&LF("lcl","aff.map",function(q){q[h]=g;return q},{});HF("lcl","init",!1)||(cJ(),IF("lcl","init",!0));return h}dJ.M="internal.enableAutoEventOnLinkClick";var eJ,fJ;
var gJ=function(a){return HF("sdl",a,{})},hJ=function(a,b,c){if(b){var d=Array.isArray(a)?a:[a];LF("sdl",c,function(e){for(var f=0;f<d.length;f++){var g=String(d[f]);e.hasOwnProperty(g)||(e[g]=[]);e[g].push(b)}return e},{})}},kJ=function(){function a(){iJ();jJ(a,!0)}return a},lJ=function(){function a(){f?e=x.setTimeout(a,c):(e=0,iJ(),jJ(b));f=!1}function b(){d&&eJ();e?f=!0:(e=x.setTimeout(a,c),IF("sdl","pending",!0))}var c=250,d=!1;A.scrollingElement&&A.documentElement&&(c=50,d=!0);var e=0,f=!1;return b},
jJ=function(a,b){HF("sdl","init",!1)&&!mJ()&&(b?Mc(x,"scrollend",a):Mc(x,"scroll",a),Mc(x,"resize",a),IF("sdl","init",!1))},iJ=function(){var a=eJ(),b=a.depthX,c=a.depthY,d=b/fJ.scrollWidth*100,e=c/fJ.scrollHeight*100;nJ(b,"horiz.pix","PIXELS","horizontal");nJ(d,"horiz.pct","PERCENT","horizontal");nJ(c,"vert.pix","PIXELS","vertical");nJ(e,"vert.pct","PERCENT","vertical");IF("sdl","pending",!1)},nJ=function(a,b,c,d){var e=gJ(b),f={},g;for(g in e)if(f={Fe:f.Fe},f.Fe=g,e.hasOwnProperty(f.Fe)){var h=
Number(f.Fe);if(!(a<h)){var m={};aE((m.event="gtm.scrollDepth",m["gtm.scrollThreshold"]=h,m["gtm.scrollUnits"]=c.toLowerCase(),m["gtm.scrollDirection"]=d,m["gtm.triggers"]=e[f.Fe].join(","),m));LF("sdl",b,function(n){return function(p){delete p[n.Fe];return p}}(f),{})}}},pJ=function(){LF("sdl","scr",function(a){a||(a=A.scrollingElement||A.body&&A.body.parentNode);return fJ=a},!1);LF("sdl","depth",function(a){a||(a=oJ());return eJ=a},!1)},oJ=function(){var a=0,b=0;return function(){var c=jx(),d=c.height;
a=Math.max(fJ.scrollLeft+c.width,a);b=Math.max(fJ.scrollTop+d,b);return{depthX:a,depthY:b}}},mJ=function(){return!!(Object.keys(gJ("horiz.pix")).length||Object.keys(gJ("horiz.pct")).length||Object.keys(gJ("vert.pix")).length||Object.keys(gJ("vert.pct")).length)};
function qJ(a,b){var c=this;if(!gh(a))throw F(this.getName(),["Object","any"],arguments);AF([function(){H(c,"detect_scroll_events")}]);pJ();if(!fJ)return;var d=GF(b),e=Bd(a);switch(e.horizontalThresholdUnits){case "PIXELS":hJ(e.horizontalThresholds,d,"horiz.pix");break;case "PERCENT":hJ(e.horizontalThresholds,d,"horiz.pct")}switch(e.verticalThresholdUnits){case "PIXELS":hJ(e.verticalThresholds,d,"vert.pix");break;case "PERCENT":hJ(e.verticalThresholds,
d,"vert.pct")}HF("sdl","init",!1)?HF("sdl","pending",!1)||Nc(function(){iJ()}):(IF("sdl","init",!0),IF("sdl","pending",!0),Nc(function(){iJ();if(mJ()){var f=lJ();"onscrollend"in x?(f=kJ(),Lc(x,"scrollend",f)):Lc(x,"scroll",f);Lc(x,"resize",f)}else IF("sdl","init",!1)}));return d}qJ.M="internal.enableAutoEventOnScroll";function rJ(a){return function(){if(a.limit&&a.uj>=a.limit)a.zh&&x.clearInterval(a.zh);else{a.uj++;var b=Ab();TD({event:a.eventName,"gtm.timerId":a.zh,"gtm.timerEventNumber":a.uj,"gtm.timerInterval":a.interval,"gtm.timerLimit":a.limit,"gtm.timerStartTime":a.Hm,"gtm.timerCurrentTime":b,"gtm.timerElapsedTime":b-a.Hm,"gtm.triggers":a.Mq})}}}
function sJ(a,b){
return f}sJ.M="internal.enableAutoEventOnTimer";
var tJ=function(a,b,c){function d(){var g=a();f+=e?(Ab()-e)*g.playbackRate/1E3:0;e=Ab()}var e=0,f=0;return{createEvent:function(g,h,m){var n=a(),p=n.Wl,q=m?Math.round(m):h?Math.round(n.Wl*h):Math.round(n.Wo),r=h!==void 0?Math.round(h*100):p<=0?0:Math.round(q/p*100),t=A.hidden?!1:kx(c)>=.5;d();var u=void 0;b!==void 0&&(u=[b]);var v=MF(c,"gtm.video",u);v["gtm.videoProvider"]="youtube";v["gtm.videoStatus"]=g;v["gtm.videoUrl"]=n.url;v["gtm.videoTitle"]=n.title;v["gtm.videoDuration"]=Math.round(p);v["gtm.videoCurrentTime"]=
Math.round(q);v["gtm.videoElapsedTime"]=Math.round(f);v["gtm.videoPercent"]=r;v["gtm.videoVisible"]=t;return v},tq:function(){e=Ab()},Si:function(){d()}}};var oc=Aa(["data-gtm-yt-inspected-"]),uJ=["www.youtube.com","www.youtube-nocookie.com"],vJ,wJ=!1;
var xJ=function(a,b,c){var d=a.map(function(g){return{Nd:g,Dm:g,sm:void 0}});if(!b.length)return d;var e=b.map(function(g){return{Nd:g*c,Dm:void 0,sm:g}});if(!d.length)return e;var f=d.concat(e);f.sort(function(g,h){return g.Nd-h.Nd});return f},yJ=function(a){a=a===void 0?[]:a;for(var b=[],c=0;c<a.length;c++)a[c]<0||b.push(a[c]);b.sort(function(d,e){return d-e});return b},zJ=function(a){a=a===void 0?[]:a;for(var b=[],c=0;c<a.length;c++)a[c]>100||a[c]<0||(b[c]=a[c]/100);b.sort(function(d,e){return d-
e});return b},AJ=function(a,b){var c,d;function e(){t=tJ(function(){return{url:w,title:y,Wl:v,Wo:a.getCurrentTime(),playbackRate:z}},b.Ge,a.getIframe());v=0;y=w="";z=1;return f}function f(I){switch(I){case 1:v=Math.round(a.getDuration());w=a.getVideoUrl();if(a.getVideoData){var M=a.getVideoData();y=M?M.title:""}z=a.getPlaybackRate();if(b.No){var T=t.createEvent("start");TD(T)}else t.Si();u=xJ(b.nq,b.mq,a.getDuration());return g(I);default:return f}}function g(){C=a.getCurrentTime();D=zb().getTime();
t.tq();r();return h}function h(I){var M;switch(I){case 0:return n(I);case 2:M="pause";case 3:var T=a.getCurrentTime()-C;M=Math.abs((zb().getTime()-D)/1E3*z-T)>1?"seek":M||"buffering";if(a.getCurrentTime())if(b.Mo){var da=t.createEvent(M);TD(da)}else t.Si();q();return m;case -1:return e(I);default:return h}}function m(I){switch(I){case 0:return n(I);case 1:return g(I);case -1:return e(I);default:return m}}function n(){for(;d;){var I=c;x.clearTimeout(d);I()}if(b.Lo){var M=t.createEvent("complete",1);
TD(M)}return e(-1)}function p(){}function q(){d&&(x.clearTimeout(d),d=0,c=p)}function r(){if(u.length&&z!==0){var I=-1,M;do{M=u[0];if(M.Nd>a.getDuration())return;I=(M.Nd-a.getCurrentTime())/z;if(I<0&&(u.shift(),u.length===0))return}while(I<0);c=function(){d=0;c=p;if(u.length>0&&u[0].Nd===M.Nd){u.shift();var T=t.createEvent("progress",M.sm,M.Dm);TD(T)}r()};d=x.setTimeout(c,I*1E3)}}var t,u=[],v,w,y,z,C,D,G=e(-1);d=0;c=p;return{onStateChange:function(I){G=G(I)},onPlaybackRateChange:function(I){C=a.getCurrentTime();
D=zb().getTime();t.Si();z=I;q();r()}}},CJ=function(a){Nc(function(){function b(){for(var d=c.getElementsByTagName("iframe"),e=d.length,f=0;f<e;f++)BJ(d[f],a)}var c=A;b();pI(b)})},BJ=function(a,b){if(!a.getAttribute("data-gtm-yt-inspected-"+b.Ge)&&(qc(a,"data-gtm-yt-inspected-"+b.Ge),DJ(a,b.Zl))){a.id||(a.id=EJ());var c=x.YT,d=c.get(a.id);d||(d=new c.Player(a.id));var e=AJ(d,b),f={},g;for(g in e)f={ig:f.ig},f.ig=g,e.hasOwnProperty(f.ig)&&d.addEventListener(f.ig,function(h){return function(m){return e[h.ig](m.data)}}(f))}},
DJ=function(a,b){var c=a.getAttribute("src");if(FJ(c,"embed/")){if(c.indexOf("enablejsapi=1")>0)return!0;if(b){var d;var e=c.indexOf("?")!==-1?"&":"?";c.indexOf("origin=")>-1?d=c+e+"enablejsapi=1":(vJ||(vJ=A.location.protocol+"//"+A.location.hostname,A.location.port&&(vJ+=":"+A.location.port)),d=c+e+"enablejsapi=1&origin="+encodeURIComponent(vJ));var f;f=Wb(d);a.src=Xb(f).toString();return!0}}return!1},FJ=function(a,b){if(!a)return!1;for(var c=0;c<uJ.length;c++)if(a.indexOf("//"+uJ[c]+"/"+b)>=0)return!0;
return!1},EJ=function(){var a=""+Math.round(Math.random()*1E9);return A.getElementById(a)?EJ():a};
function GJ(a,b){var c=this;var d=function(){CJ(q)};if(!gh(a))throw F(this.getName(),["Object","any"],arguments);AF([function(){H(c,"detect_youtube_activity_events",{fixMissingApi:!!a.get("fixMissingApi")})}]);var e=GF(b),f=!!a.get("captureStart"),g=!!a.get("captureComplete"),h=!!a.get("capturePause"),m=zJ(Bd(a.get("progressThresholdsPercent"))),n=yJ(Bd(a.get("progressThresholdsTimeInSeconds"))),p=!!a.get("fixMissingApi");
if(!(f||g||h||m.length||n.length))return;var q={No:f,Lo:g,Mo:h,mq:m,nq:n,Zl:p,Ge:e},r=x.YT;if(r)return r.ready&&r.ready(d),e;var t=x,u=t.onYouTubeIframeAPIReady;t.onYouTubeIframeAPIReady=function(){u&&u();d()};Nc(function(){for(var v=A.getElementsByTagName("script"),w=v.length,y=0;y<w;y++){var z=v[y].getAttribute("src");if(FJ(z,"iframe_api")||FJ(z,"player_api"))return e}for(var C=A.getElementsByTagName("iframe"),D=C.length,G=0;G<D;G++)if(!wJ&&DJ(C[G],q.Zl))return Gc("https://www.youtube.com/iframe_api"),
wJ=!0,e});return e}GJ.M="internal.enableAutoEventOnYouTubeActivity";wJ=!1;function HJ(a,b){if(!nh(a)||!hh(b))throw F(this.getName(),["string","Object|undefined"],arguments);var c=b?Bd(b):{},d=a,e=!1;var f=JSON.parse(d);if(!f)throw Error("Invalid boolean expression string was given.");e=Jh(f,c);return e}HJ.M="internal.evaluateBooleanExpression";var IJ;function JJ(a){var b=!1;return b}JJ.M="internal.evaluateMatchingRules";function sK(){return zr(7)&&zr(9)&&zr(10)};function nL(a,b,c,d){}nL.M="internal.executeEventProcessor";function oL(a){var b;return Cd(b,this.K,1)}oL.M="internal.executeJavascriptString";function pL(a){var b;return b};function qL(a){var b="";return b}qL.M="internal.generateClientId";function rL(a){var b={};return Cd(b)}rL.M="internal.getAdsCookieWritingOptions";function sL(a,b){var c=!1;return c}sL.M="internal.getAllowAdPersonalization";function tL(){var a;return a}tL.M="internal.getAndResetEventUsage";function uL(a,b){b=b===void 0?!0:b;var c;return c}uL.M="internal.getAuid";var vL=null;
function wL(){var a=new Ya;H(this,"read_container_data"),E(49)&&vL?a=vL:(a.set("containerId",'G-SMNVEQYP6G'),a.set("version",'1'),a.set("environmentName",''),a.set("debugMode",qg),a.set("previewMode",rg.Jm),a.set("environmentMode",rg.fp),a.set("firstPartyServing",wk()||Xj.N),a.set("containerUrl",xc),a.Wa(),E(49)&&(vL=a));return a}
wL.publicName="getContainerVersion";function xL(a,b){b=b===void 0?!0:b;var c;return c}xL.publicName="getCookieValues";function yL(){var a="";return a}yL.M="internal.getCorePlatformServicesParam";function zL(){return so()}zL.M="internal.getCountryCode";function AL(){var a=[];a=Lm();return Cd(a)}AL.M="internal.getDestinationIds";function BL(a){var b=new Ya;return b}BL.M="internal.getDeveloperIds";function CL(a){var b;return b}CL.M="internal.getEcsidCookieValue";function DL(a,b){var c=null;return c}DL.M="internal.getElementAttribute";function EL(a){var b=null;return b}EL.M="internal.getElementById";function FL(a){var b="";return b}FL.M="internal.getElementInnerText";function GL(a,b){var c=null;return Cd(c)}GL.M="internal.getElementProperty";function HL(a){var b;return b}HL.M="internal.getElementValue";function IL(a){var b=0;return b}IL.M="internal.getElementVisibilityRatio";function JL(a){var b=null;return b}JL.M="internal.getElementsByCssSelector";
function KL(a){var b;if(!nh(a))throw F(this.getName(),["string"],arguments);H(this,"read_event_data",a);var c;a:{var d=a,e=EF(this).originalEventData;if(e){for(var f=e,g={},h={},m={},n=[],p=d.split("\\\\"),q=0;q<p.length;q++){for(var r=p[q].split("\\."),t=0;t<r.length;t++){for(var u=r[t].split("."),v=0;v<u.length;v++)n.push(u[v]),v!==u.length-1&&n.push(m);t!==r.length-1&&n.push(h)}q!==p.length-1&&n.push(g)}for(var w=[],y="",z=l(n),C=z.next();!C.done;C=
z.next()){var D=C.value;D===m?(w.push(y),y=""):y=D===g?y+"\\":D===h?y+".":y+D}y&&w.push(y);for(var G=l(w),I=G.next();!I.done;I=G.next()){if(f==null){c=void 0;break a}f=f[I.value]}c=f}else c=void 0}b=Cd(c,this.K,1);return b}KL.M="internal.getEventData";var LL={};LL.disableUserDataWithoutCcd=E(223);LL.enableDecodeUri=E(92);LL.enableGaAdsConversions=E(122);LL.enableGaAdsConversionsClientId=E(121);LL.enableOverrideAdsCps=E(170);LL.enableUrlDecodeEventUsage=E(139);function ML(){return Cd(LL)}ML.M="internal.getFlags";function NL(){var a;return a}NL.M="internal.getGsaExperimentId";function OL(){return new yd(KE)}OL.M="internal.getHtmlId";function PL(a){var b;return b}PL.M="internal.getIframingState";function QL(a,b){var c={};return Cd(c)}QL.M="internal.getLinkerValueFromLocation";function RL(){var a=new Ya;return a}RL.M="internal.getPrivacyStrings";function SL(a,b){var c;if(!nh(a)||!nh(b))throw F(this.getName(),["string","string"],arguments);var d=gx(a)||{};c=Cd(d[b],this.K);return c}SL.M="internal.getProductSettingsParameter";function TL(a,b){var c;if(!nh(a)||!rh(b))throw F(this.getName(),["string","boolean|undefined"],arguments);H(this,"get_url","query",a);var d=Xk(cl(x.location.href),"query"),e=Uk(d,a,b);c=Cd(e,this.K);return c}TL.publicName="getQueryParameters";function UL(a,b){var c;return c}UL.publicName="getReferrerQueryParameters";function VL(a){var b="";return b}VL.publicName="getReferrerUrl";function WL(){return to()}WL.M="internal.getRegionCode";function XL(a,b){var c;if(!nh(a)||!nh(b))throw F(this.getName(),["string","string"],arguments);var d=Rq(a);c=Cd(d[b],this.K);return c}XL.M="internal.getRemoteConfigParameter";function YL(){var a=new Ya;a.set("width",0);a.set("height",0);return a}YL.M="internal.getScreenDimensions";function ZL(){var a="";return a}ZL.M="internal.getTopSameDomainUrl";function $L(){var a="";return a}$L.M="internal.getTopWindowUrl";function aM(a){var b="";if(!oh(a))throw F(this.getName(),["string|undefined"],arguments);H(this,"get_url",a);b=Xk(cl(x.location.href),a);return b}aM.publicName="getUrl";function bM(){H(this,"get_user_agent");return uc.userAgent}bM.M="internal.getUserAgent";function cM(){var a;return a?Cd(bz(a)):a}cM.M="internal.getUserAgentClientHints";var eM=function(a){var b=a.eventName===J.m.Yc&&nn()&&ty(a),c=R(a,Q.A.pl),d=R(a,Q.A.Lj),e=R(a,Q.A.Gf),f=R(a,Q.A.oe),g=R(a,Q.A.ug),h=R(a,Q.A.Rd),m=R(a,Q.A.vg),n=R(a,Q.A.wg),p=!!sy(a)||!!R(a,Q.A.Ph);return!(!Wc()&&uc.sendBeacon===void 0||e||p||f||g||h||n||m||b||c||!d&&dM)},dM=!1;
var fM=function(a){var b=0,c=0;return{start:function(){b=Ab()},stop:function(){c=this.get()},get:function(){var d=0;a.lj()&&(d=Ab()-b);return d+c}}},gM=function(){this.C=void 0;this.H=0;this.isActive=this.isVisible=this.N=!1;this.R=this.P=void 0};k=gM.prototype;k.io=function(a){var b=this;if(!this.C){this.N=A.hasFocus();this.isVisible=!A.hidden;this.isActive=!0;var c=function(e,f,g){Lc(e,f,function(h){b.C.stop();g(h);b.lj()&&b.C.start()})},d=x;c(d,"focus",function(){b.N=!0});c(d,"blur",function(){b.N=
!1});c(d,"pageshow",function(e){b.isActive=!0;e.persisted&&L(56);b.R&&b.R()});c(d,"pagehide",function(){b.isActive=!1;b.P&&b.P()});c(A,"visibilitychange",function(){b.isVisible=!A.hidden});ty(a)&&!Ac()&&c(d,"beforeunload",function(){dM=!0});this.Cj(!0);this.H=0}};k.Cj=function(a){if((a===void 0?0:a)||this.C)this.H+=this.xh(),this.C=fM(this),this.lj()&&this.C.start()};k.Kq=function(a){var b=this.xh();b>0&&U(a,J.m.Ig,b)};k.Ep=function(a){U(a,J.m.Ig);this.Cj();this.H=0};k.lj=function(){return this.N&&
this.isVisible&&this.isActive};k.up=function(){return this.H+this.xh()};k.xh=function(){return this.C&&this.C.get()||0};k.qq=function(a){this.P=a};k.Am=function(a){this.R=a};var hM=function(a){fb("GA4_EVENT",a)};var iM=function(a){var b=R(a,Q.A.Xk);if(Array.isArray(b))for(var c=0;c<b.length;c++)hM(b[c]);var d=ib("GA4_EVENT");d&&U(a,"_eu",d)},jM=function(){delete eb.GA4_EVENT};function kM(){var a=x;return a.gaGlobal=a.gaGlobal||{}}function lM(){var a=kM();a.hid=a.hid||qb();return a.hid}function mM(a,b){var c=kM();if(c.vid===void 0||b&&!c.from_cookie)c.vid=a,c.from_cookie=b};var nM=["GA1"];
var oM=function(a,b,c){var d=R(a,Q.A.Nj);if(d===void 0||c<=d)U(a,J.m.Rb,b),S(a,Q.A.Nj,c)},qM=function(a,b){var c=Zv(a,J.m.Rb);if(O(a.D,J.m.Lc)&&O(a.D,J.m.Kc)||b&&c===b)return c;if(c){c=""+c;if(!pM(c,a))return L(31),a.isAborted=!0,"";mM(c,P(J.m.ja));return c}L(32);a.isAborted=!0;return""},rM=function(a){var b=R(a,Q.A.xa),c=b.prefix+"_ga",d=Os(b.prefix+"_ga",b.domain,b.path,nM,J.m.ja);if(!d){var e=String(O(a.D,J.m.ed,""));e&&e!==c&&(d=Os(e,b.domain,b.path,nM,J.m.ja))}return d},pM=function(a,b){var c;
var d=R(b,Q.A.xa),e=d.prefix+"_ga",f=Wr(d,void 0,void 0,J.m.ja);if(O(b.D,J.m.Hc)===!1&&rM(b)===a)c=!0;else{var g;g=[nM[0],Ls(d.domain,d.path),a].join(".");c=Gs(e,g,f)!==1}return c};
var sM=function(a){if(a){var b;a:{var c=(Gb(a,"s")&&a.indexOf(".")===-1?"GS2":"GS1")+".1."+a;try{b=Pt(c,2);break a}catch(d){}b=void 0}return b}},uM=function(a,b){var c;a:{var d=tM,e=Ot[2];if(e){var f,g=Js(b.domain),h=Ks(b.path),m=Object.keys(e.Hh),n=St.get(2),p;if(f=(p=ys(a,g,h,m,n))==null?void 0:p.Ro){var q=Pt(f,2,d);c=q?Ut(q):void 0;break a}}c=void 0}if(c){var r=Tt(a,2,tM);if(r&&r.length>1){hM(28);var t;if(r&&r.length!==0){for(var u,v=-Infinity,w=l(r),y=w.next();!y.done;y=w.next()){var z=y.value;
if(z.t!==void 0){var C=Number(z.t);!isNaN(C)&&C>v&&(v=C,u=z)}}t=u}else t=void 0;var D=t;D&&D.t!==c.t&&(hM(32),c=D)}return Rt(c,2)}},tM=function(a){a&&(a==="GS1"?hM(33):a==="GS2"&&hM(34))},vM=function(a){var b=sM(a);if(b){var c=Number(b.o),d=Number(b.t),e=Number(b.j||0);c||hM(29);d||hM(30);isNaN(e)&&hM(31);if(c&&d&&!isNaN(e)){var f=b.h,g=f&&f!=="0"?String(f):void 0,h=b.d?String(b.d):void 0,m={};return m.s=String(b.s),m.o=c,m.g=!!Number(b.g),m.t=d,m.d=h,m.j=e,m.l=b.l==="1",m.h=g,m}}};

var xM=function(a,b,c){if(!b)return a;if(!a)return b;var d=vM(a);if(!d)return b;var e,f=vb((e=O(c.D,J.m.qf))!=null?e:30),g=R(c,Q.A.hb);if(!(Math.floor(g/1E3)>d.t+f*60))return a;var h=vM(b);if(!h)return a;h.o=d.o+1;var m;return(m=wM(h))!=null?m:b},zM=function(a,b){var c=R(b,Q.A.xa),d=yM(b,c),e=sM(a);if(!e)return!1;var f=Wr(c||{},void 0,void 0,St.get(2));Gs(d,void 0,f);return Vt(d,e,2,c)!==1},AM=function(a){var b=R(a,Q.A.xa);return uM(yM(a,b),b)},BM=function(a){var b=R(a,Q.A.hb),c={};c.s=Zv(a,J.m.Wb);
c.o=Zv(a,J.m.Ug);var d;d=Zv(a,J.m.Tg);var e=(c.g=d,c.t=Math.floor(b/1E3),c.d=R(a,Q.A.Jf),c.j=R(a,Q.A.Kf)||0,c.l=!!R(a,J.m.bi),c.h=Zv(a,J.m.Jg),c);return wM(e)},wM=function(a){if(a.s&&a.o){var b={},c=(b.s=a.s,b.o=String(a.o),b.g=vb(a.g)?"1":"0",b.t=String(a.t),b.j=String(a.j),b.l=a.l?"1":"0",b.h=a.h||"0",b.d=a.d,b);return Rt(c,2)}},yM=function(a,b){return b.prefix+"_ga_"+a.target.ids[Qp[6]]};
var CM=function(a){var b=O(a.D,J.m.Ta),c=a.D.H[J.m.Ta];if(c===b)return c;var d=md(b,null);c&&c[J.m.la]&&(d[J.m.la]=(d[J.m.la]||[]).concat(c[J.m.la]));return d},DM=function(a,b){var c=gt(!0);return c._up!=="1"?{}:{clientId:c[a],xb:c[b]}},EM=function(a,b,c){var d=gt(!0),e=d[b];e&&(oM(a,e,2),pM(e,a));var f=d[c];f&&zM(f,a);return{clientId:e,xb:f}},FM=function(){var a=Zk(x.location,"host"),b=Zk(cl(A.referrer),"host");return a&&b?a===b||a.indexOf("."+b)>=0||b.indexOf("."+a)>=0?!0:!1:!1},GM=function(a){if(!O(a.D,
J.m.Hb))return{};var b=R(a,Q.A.xa),c=b.prefix+"_ga",d=yM(a,b);ot(function(){var e;if(P("analytics_storage"))e={};else{var f={_up:"1"},g;g=Zv(a,J.m.Rb);e=(f[c]=g,f[d]=BM(a),f)}return e},1);return!P("analytics_storage")&&FM()?DM(c,d):{}},IM=function(a){var b=CM(a)||{},c=R(a,Q.A.xa),d=c.prefix+"_ga",e=yM(a,c),f={};qt(b[J.m.he],!!b[J.m.la])&&(f=EM(a,d,e),f.clientId&&f.xb&&(HM=!0));b[J.m.la]&&nt(function(){var g={},h=rM(a);h&&(g[d]=h);var m=AM(a);m&&(g[e]=m);var n=us("FPLC",void 0,void 0,J.m.ja);n.length&&
(g._fplc=n[0]);return g},b[J.m.la],b[J.m.Mc],!!b[J.m.uc]);return f},HM=!1;var JM=function(a){if(!R(a,Q.A.zd)&&kl(a.D)){var b=CM(a)||{},c=(qt(b[J.m.he],!!b[J.m.la])?gt(!0)._fplc:void 0)||(us("FPLC",void 0,void 0,J.m.ja).length>0?void 0:"0");U(a,"_fplc",c)}};function KM(a){(ty(a)||wk())&&U(a,J.m.Sk,to()||so());!ty(a)&&wk()&&U(a,J.m.il,"::")}function LM(a){if(wk()&&!ty(a)&&(wo()||U(a,J.m.Gk,!0),E(78))){mw(a);nw(a,Lp.Cf.Xm,Qo(O(a.D,J.m.eb)));var b=Lp.Cf.Ym;var c=O(a.D,J.m.Hc);nw(a,b,c===!0?1:c===!1?0:void 0);nw(a,Lp.Cf.Wm,Qo(O(a.D,J.m.Ab)));nw(a,Lp.Cf.Um,Ls(Po(O(a.D,J.m.pb)),Po(O(a.D,J.m.Sb))))}};var NM=function(a,b){Ep("grl",function(){return MM()})(b)||(L(35),a.isAborted=!0)},MM=function(){var a=Ab(),b=a+864E5,c=20,d=5E3;return function(e){var f=Ab();f>=b&&(b=f+864E5,d=5E3);c=Math.min(c+(f-a)/1E3*5,20);a=f;var g=!1;d<1||c<1||(g=!0,d--,c--);e&&(e.Xo=d,e.Ko=c);return g}};
var OM=function(a){var b=Zv(a,J.m.Ya);return Xk(cl(b),"host",!0)},PM=function(a){if(O(a.D,J.m.lf)!==void 0)a.copyToHitData(J.m.lf);else{var b=O(a.D,J.m.hi),c,d;a:{if(HM){var e=CM(a)||{};if(e&&e[J.m.la])for(var f=OM(a),g=e[J.m.la],h=0;h<g.length;h++)if(g[h]instanceof RegExp){if(g[h].test(f)){d=!0;break a}}else if(f.indexOf(g[h])>=0){d=!0;break a}}d=!1}if(!(c=d)){var m;if(m=b)a:{for(var n=b.include_conditions||[],p=OM(a),q=0;q<n.length;q++)if(n[q].test(p)){m=!0;break a}m=!1}c=m}c&&(U(a,J.m.lf,"1"),
hM(4))}};
var QM=function(a,b){Gr()&&(a.gcs=Hr(),R(b,Q.A.Ff)&&(a.gcu="1"));a.gcd=Lr(b.D);a.npa=R(b,Q.A.Ih)?"0":"1";Qr()&&(a._ng="1")},RM=function(a){if(R(a,Q.A.zd))return{url:ll("https://www.merchant-center-analytics.goog",void 0,"")+"/mc/collect",endpoint:20};var b=hl(kl(a.D),"/g/collect");if(b)return{url:b,endpoint:16};var c=uy(a),d=O(a.D,J.m.Pb),e=c&&!uo()&&d!==!1&&sK()&&P(J.m.U)&&P(J.m.ja)?17:16;return{url:Xz(e),endpoint:e}},SM={};SM[J.m.Rb]="cid";SM[J.m.Rh]="gcut";SM[J.m.dd]="are";SM[J.m.Gg]="pscdl";SM[J.m.di]=
"_fid";SM[J.m.Ck]="_geo";SM[J.m.Ub]="gdid";SM[J.m.ee]="_ng";SM[J.m.Jc]="frm";SM[J.m.lf]="ir";SM[J.m.Gk]="fp";SM[J.m.Bb]="ul";SM[J.m.Rg]="ni";SM[J.m.Rn]="pae";SM[J.m.Sg]="_rdi";SM[J.m.Nc]="sr";SM[J.m.Vn]="tid";SM[J.m.mi]="tt";SM[J.m.wc]="ec_mode";SM[J.m.nl]="gtm_up";SM[J.m.tf]="uaa";SM[J.m.uf]="uab";SM[J.m.vf]="uafvl";SM[J.m.wf]="uamb";SM[J.m.xf]="uam";SM[J.m.yf]="uap";SM[J.m.zf]=
"uapv";SM[J.m.Af]="uaw";SM[J.m.Sk]="ur";SM[J.m.il]="_uip";SM[J.m.Qn]="_prs";SM[J.m.ld]="lps";SM[J.m.Xd]="gclgs";SM[J.m.Zd]="gclst";SM[J.m.Yd]="gcllp";var TM={};TM[J.m.Se]="cc";TM[J.m.Te]="ci";TM[J.m.Ue]="cm";TM[J.m.Ve]="cn";TM[J.m.Xe]="cs";TM[J.m.Ye]="ck";TM[J.m.Sa]="cu";TM[J.m.kf]=
"_tu";TM[J.m.Ca]="dl";TM[J.m.Ya]="dr";TM[J.m.Gb]="dt";TM[J.m.Tg]="seg";TM[J.m.Wb]="sid";TM[J.m.Ug]="sct";TM[J.m.Ma]="uid";E(145)&&(TM[J.m.pf]="dp");var UM={};UM[J.m.Ig]="_et";UM[J.m.Tb]="edid";E(94)&&(UM._eu="_eu");var VM={};VM[J.m.Se]="cc";VM[J.m.Te]="ci";VM[J.m.Ue]="cm";VM[J.m.Ve]="cn";VM[J.m.Xe]="cs";VM[J.m.Ye]="ck";var WM={},XM=(WM[J.m.fb]=1,WM),YM=function(a,
b,c){function d(N,W){if(W!==void 0&&!Ao.hasOwnProperty(N)){W===null&&(W="");var ia;var ka=W;N!==J.m.Jg?ia=!1:R(a,Q.A.ke)||ty(a)?(e.ecid=ka,ia=!0):ia=void 0;if(!ia&&N!==J.m.bi){var Y=W;W===!0&&(Y="1");W===!1&&(Y="0");Y=String(Y);var X;if(SM[N])X=SM[N],e[X]=Y;else if(TM[N])X=TM[N],g[X]=Y;else if(UM[N])X=UM[N],f[X]=Y;else if(N.charAt(0)==="_")e[N]=Y;else{var ja;VM[N]?ja=!0:N!==J.m.We?ja=!1:(typeof W!=="object"&&C(N,W),ja=!0);ja||C(N,W)}}}}var e={},f={},g={};e.v="2";e.tid=a.target.destinationId;e.gtm=
Ur({Pa:R(a,Q.A.ib)});e._p=E(159)?pk:lM();if(c&&(c.Za||c.fj)&&(E(125)||(e.em=c.Lb),c.Jb)){var h=c.Jb.ze;h&&!E(8)&&(h=h.replace(/./g,"*"));h&&(e.eme=h)}R(a,Q.A.Rd)&&(e._gaz=1);QM(e,a);Or()&&(e.dma_cps=Mr());e.dma=Nr();jr(rr())&&(e.tcfd=Pr());Yz()&&(e.tag_exp=Yz());Zz()&&(e.ptag_exp=Zz());var m=Zv(a,J.m.Ub);m&&(e.gdid=m);f.en=String(a.eventName);if(R(a,Q.A.Hf)){var n=R(a,Q.A.kl);f._fv=n?2:1}R(a,Q.A.eh)&&(f._nsi=1);if(R(a,Q.A.oe)){var p=R(a,Q.A.ol);f._ss=p?2:1}R(a,Q.A.Gf)&&(f._c=1);R(a,Q.A.yd)&&(f._ee=
1);if(R(a,Q.A.jl)){var q=Zv(a,J.m.sa)||O(a.D,J.m.sa);if(Array.isArray(q))for(var r=0;r<q.length&&r<200;r++)f["pr"+(r+1)]=vg(q[r])}var t=Zv(a,J.m.Tb);t&&(f.edid=t);var u=Zv(a,J.m.sc);if(u&&typeof u==="object")for(var v=l(Object.keys(u)),w=v.next();!w.done;w=v.next()){var y=w.value,z=u[y];z!==void 0&&(z===null&&(z=""),f["gap."+y]=String(z))}for(var C=function(N,W){if(typeof W!=="object"||!XM[N]){var ia="ep."+N,ka="epn."+N;N=nb(W)?ka:ia;var Y=nb(W)?ia:ka;f.hasOwnProperty(Y)&&delete f[Y];f[N]=String(W)}},
D=l(Object.keys(a.C)),G=D.next();!G.done;G=D.next()){var I=G.value;d(I,Zv(a,I))}(function(N){ty(a)&&typeof N==="object"&&tb(N||{},function(W,ia){typeof ia!=="object"&&(e["sst."+W]=String(ia))})})(Zv(a,J.m.Ki));$z(e,Zv(a,J.m.wd));var M=Zv(a,J.m.Xb)||{};O(a.D,J.m.Pb,void 0,4)===!1&&(e.ngs="1");tb(M,function(N,W){W!==void 0&&((W===null&&(W=""),N!==J.m.Ma||g.uid)?b[N]!==W&&(f[(nb(W)?"upn.":"up.")+String(N)]=String(W),b[N]=W):g.uid=String(W))});if(wk()&&!wo()){var T=R(a,Q.A.Jf);T?e._gsid=T:e.njid="1"}var da=
RM(a);Ig.call(this,{ra:e,Pd:g,Zi:f},da.url,da.endpoint,ty(a),void 0,a.target.destinationId,a.D.eventId,a.D.priorityId)};va(YM,Ig);
var ZM=function(a,b){return a.replace(/\$\{([^\}]+)\}/g,function(c,d){return b[d]||c})},$M=function(a){var b={},c="",d=a.pathname.indexOf("/g/collect");d>=0&&(c=a.pathname.substring(0,d));b.transport_url=a.protocol+"//"+a.hostname+c;if(E(186)){var e;try{e=encodeURIComponent(c||"/")}catch(f){e=encodeURIComponent("/")}b.encoded_path=e}return b},aN=function(a,b,c,d,e){var f=0,g=new x.XMLHttpRequest;g.withCredentials=!0;g.onprogress=function(h){if(g.status===200){var m=g.responseText.substring(f);f=h.loaded;
SA(c,m)}};g.onerror=function(){e==null||e()};g.onload=function(){g.status<=399||e==null||e()};g.open(b?"POST":"GET",a);(d==null?0:d.attributionReporting)&&g.setAttributionReporting&&g.setAttributionReporting(d.attributionReporting);g.send(b)},cN=function(a,b,c){var d;return d=VA(UA(new TA(function(e,f){var g=ZM(e,b);c&&(g=g.replace("_is_sw=0",c));var h={};f.attribution_reporting&&(h.attributionsrc="");tm(a,g,void 0,XA(d,f),h)}),function(e,f){var g=ZM(e,b),h=f.dedupe_key;h&&ym(a,g,h)}),function(e,
f){var g=ZM(e,b);c&&(g=g.replace("_is_sw=0",c));var h={};f.attribution_reporting&&(h.attributionReporting={eventSourceEligible:!1,triggerEligible:!0});f.process_response?bN(a,g,void 0,d,h,XA(d,f)):um(a,g,void 0,h,void 0,XA(d,f))})},dN=function(a,b,c,d,e){nm(a,2,b);var f=cN(a,d,e);bN(a,b,c,f)},bN=function(a,b,c,d,e,f){Wc()?RA(a,b,c,d,e,void 0,f):aN(b,c,d,(e==null?0:e.attributionReporting)?{attributionReporting:e.attributionReporting}:{},f)},eN=function(a,b,c){var d=cl(b),e=$M(d),f=ZA(d);!E(132)||zc("; wv")||
zc("FBAN")||zc("FBAV")||Bc()?dN(a,f,c,e):Uy(f,c,e,function(g){dN(a,f,c,e,g)})};var fN={AW:Cn.Z.Om,G:Cn.Z.Zn,DC:Cn.Z.Xn};function gN(a){var b=lj(a);return""+ls(b.map(function(c){return c.value}).join("!"))}function hN(a){var b=Op(a);return b&&fN[b.prefix]}function iN(a,b){var c=a[b];c&&(c.clearTimerId&&x.clearTimeout(c.clearTimerId),c.clearTimerId=x.setTimeout(function(){delete a[b]},36E5))};
var jN=function(a,b,c,d){var e=a+"?"+b;d?sm(c,e,d):rm(c,e)},lN=function(a,b,c,d,e){var f=b,g=Zc();g!==void 0&&(f+="&tfd="+Math.round(g));b=f;var h=a+"?"+b;kN&&(d=!Gb(h,Wz())&&!Gb(h,Vz()));if(d&&!dM)eN(e,h,c);else{var m=b;Wc()?um(e,a+"?"+m,c,{Eh:!0})||jN(a,m,e,c):jN(a,m,e,c)}},mN=function(a,b){function c(y){q.push(y+"="+encodeURIComponent(""+a.ra[y]))}var d=b.yq,e=b.Bq,f=b.Aq,g=b.zq,h=b.wp,m=b.Qp,n=b.Pp,p=b.mp;if(d||e||f||g){var q=[];a.ra._ng&&c("_ng");c("tid");c("cid");c("gtm");q.push("aip=1");a.Pd.uid&&
!n&&q.push("uid="+encodeURIComponent(""+a.Pd.uid));c("dma");a.ra.dma_cps!=null&&c("dma_cps");a.ra.gcs!=null&&c("gcs");c("gcd");a.ra.npa!=null&&c("npa");a.ra.frm!=null&&c("frm");d&&(Yz()&&q.push("tag_exp="+Yz()),Zz()&&q.push("ptag_exp="+Zz()),jN("https://stats.g.doubleclick.net/g/collect","v=2&"+q.join("&"),{destinationId:a.destinationId||"",endpoint:19,eventId:a.eventId,priorityId:a.priorityId}),gp({targetId:String(a.ra.tid),request:{url:"https://stats.g.doubleclick.net/g/collect?v=2&"+q.join("&"),
parameterEncoding:2,endpoint:19},Oa:b.Oa}));if(e&&(Yz()&&q.push("tag_exp="+Yz()),Zz()&&q.push("ptag_exp="+Zz()),q.push("z="+qb()),!m)){var r=h&&Gb(h,"google.")&&h!=="google.com"?"https://www.%/ads/ga-audiences?v=1&t=sr&slf_rd=1&_r=4&".replace("%",h):void 0;if(r){var t=r+q.join("&");tm({destinationId:a.destinationId||"",endpoint:47,eventId:a.eventId,priorityId:a.priorityId},t);gp({targetId:String(a.ra.tid),request:{url:t,parameterEncoding:2,endpoint:47},Oa:b.Oa})}}if(f){var u="https://{ga4CollectionSubdomain.}analytics.google.com/g/s/collect".replace("{ga4CollectionSubdomain.}",
p?p+".":"");q=[];c("_gsid");c("gtm");a.ra._geo&&c("_geo");jN(u,q.join("&"),{destinationId:a.destinationId||"",endpoint:18,eventId:a.eventId,priorityId:a.priorityId});gp({targetId:String(a.ra.tid),request:{url:u+"?"+q.join("&"),parameterEncoding:2,endpoint:18},Oa:b.Oa})}if(g)if(q=[],q.push("v=2"),c("_gsid"),c("gtm"),a.ra._geo&&c("_geo"),E(224)){var v="https://{ga4CollectionSubdomain.}google-analytics.com/g/s/collect".replace("{ga4CollectionSubdomain.}",(p||"www")+".");jN(v,q.join("&"),{destinationId:a.destinationId||
"",endpoint:62,eventId:a.eventId,priorityId:a.priorityId});gp({targetId:String(a.ra.tid),request:{url:v+"?"+q.join("&"),parameterEncoding:2,endpoint:62},Oa:b.Oa})}else{var w="https://{ga4CollectionSubdomain.}google-analytics.com/g/collect".replace("{ga4CollectionSubdomain.}",(p||"www")+".");q.push("t=g");jN(w,q.join("&"),{destinationId:a.destinationId||"",endpoint:16,eventId:a.eventId,priorityId:a.priorityId});gp({targetId:String(a.ra.tid),request:{url:w+"?"+q.join("&"),parameterEncoding:2,endpoint:16},
Oa:b.Oa})}}},kN=!1;var nN=function(){this.N=1;this.P={};this.H=-1;this.C=new Bg};k=nN.prototype;k.Nb=function(a,b){var c=this,d=new YM(a,this.P,b),e={eventId:a.D.eventId,priorityId:a.D.priorityId},f=eM(a),g,h;f&&this.C.R(d)||this.flush();var m=f&&this.C.add(d);if(m){if(this.H<0){var n=x,p=n.setTimeout,q;ty(a)?oN?(oN=!1,q=pN):q=qN:q=5E3;this.H=p.call(n,function(){c.flush()},
q)}}else{var r=Eg(d,this.N++),t=r.params,u=r.body;g=t;h=u;lN(d.baseUrl,t,u,d.N,{destinationId:a.target.destinationId,endpoint:d.endpoint,eventId:d.eventId,priorityId:d.priorityId});var v=R(a,Q.A.ug),w=R(a,Q.A.Rd),y=R(a,Q.A.wg),z=R(a,Q.A.vg),C=O(a.D,J.m.ob)!==!1,D=Fr(a.D),G={yq:v,Bq:w,Aq:y,zq:z,wp:yo(),Er:C,Dr:D,Qp:uo(),Pp:R(a,Q.A.ke),Oa:e,D:a.D,mp:wo()};mN(d,G)}GA(a.D.eventId);hp(function(){if(m){var I=Eg(d),M=I.body;g=I.params;h=M}return{targetId:a.target.destinationId,request:{url:d.baseUrl+"?"+
g,parameterEncoding:2,postBody:h,endpoint:d.endpoint},Oa:e,isBatched:!1}})};k.add=function(a){if(E(100)){var b=R(a,Q.A.Ph);if(b){U(a,J.m.wc,R(a,Q.A.Nl));U(a,J.m.Rg,"1");this.Nb(a,b);return}}var c=sy(a);if(E(100)&&c){var d;var e=a.target.destinationId,f;var g=c,h=hN(e);if(h){var m=gN(g);f=(Gn(h)||{})[m]}else f=void 0;var n=f;d=n?n.sentTo[e]:void 0;if(d&&d+6E4>Ab())c=void 0,U(a,J.m.wc);else{var p=c,q=a.target.destinationId,r=hN(q);if(r){var t=gN(p),u=Gn(r)||{},v=u[t];if(v)v.timestamp=Ab(),v.sentTo=
v.sentTo||{},v.sentTo[q]=Ab(),v.pending=!0;else{var w={};u[t]={pending:!0,timestamp:Ab(),sentTo:(w[q]=Ab(),w)}}iN(u,t);Fn(r,u)}}}!c||dM||E(125)&&!E(93)?this.Nb(a):this.Cq(a)};k.flush=function(){if(this.C.events.length){var a=Gg(this.C,this.N++);lN(this.C.baseUrl,a.params,a.body,this.C.H,{destinationId:this.C.destinationId||"",endpoint:this.C.endpoint,eventId:this.C.fa,priorityId:this.C.ma});this.C=new Bg;this.H>=0&&(x.clearTimeout(this.H),this.H=-1)}};k.Xl=function(a,b){var c=Zv(a,J.m.wc);U(a,J.m.wc);
b.then(function(d){var e={},f=(e[Q.A.Ph]=d,e[Q.A.Nl]=c,e),g=Xw(a.target.destinationId,J.m.Wd,a.D.C);$w(g,a.D.eventId,{eventMetadata:f})})};k.Cq=function(a){var b=this,c=sy(a);if(Kj(c)){var d=zj(c,E(93));d?E(100)?(this.Xl(a,d),this.Nb(a)):d.then(function(g){b.Nb(a,g)},function(){b.Nb(a)}):this.Nb(a)}else{var e=Jj(c);if(E(93)){var f=uj(e);f?E(100)?(this.Xl(a,f),this.Nb(a)):f.then(function(g){b.Nb(a,g)},function(){b.Nb(a,e)}):this.Nb(a,e)}else this.Nb(a,e)}};var pN=wg('',
500),qN=wg('',5E3),oN=!0;
var rN=function(a,b,c){c===void 0&&(c={});if(b==null)return c;if(typeof b==="object")for(var d=l(Object.keys(b)),e=d.next();!e.done;e=d.next()){var f=e.value;rN(a+"."+f,b[f],c)}else c[a]=b;return c},sN=function(a){for(var b={},c=l(a),d=c.next();!d.done;d=c.next()){var e=d.value;b[e]=!!P(e)}return b},uN=function(a,b){var c=tN.filter(function(e){return!P(e)});if(c.length){var d=sN(c);up(c,function(){for(var e=sN(c),f=[],g=l(c),h=g.next();!h.done;h=g.next()){var m=h.value;!d[m]&&e[m]&&f.push(m);e[m]&&
(d[m]=!0)}if(f.length){S(b,Q.A.Ff,!0);var n=f.map(function(p){return Ko[p]}).join(".");n&&qy(b,"gcut",n);a(b)}})}},vN=function(a){ty(a)&&qy(a,"navt",$c())},wN=function(a){ty(a)&&qy(a,"lpc",au())},xN=function(a){if(E(152)&&ty(a)){var b=O(a.D,J.m.Vb),c;b===!0&&(c="1");b===!1&&(c="0");c&&qy(a,"rdp",c)}},yN=function(a){E(147)&&ty(a)&&O(a.D,J.m.Re,!0)===!1&&U(a,J.m.Re,0)},zN=function(a,b){if(ty(b)){var c=R(b,Q.A.Gf);(b.eventName==="page_view"||c)&&uN(a,b)}},AN=function(a){if(ty(a)&&a.eventName===J.m.Wd&&
R(a,Q.A.Ff)){var b=Zv(a,J.m.Rh);b&&(qy(a,"gcut",b),qy(a,"syn",1))}},BN=function(a){ty(a)&&S(a,Q.A.Ba,!1)},CN=function(a){ty(a)&&(R(a,Q.A.Ba)&&qy(a,"sp",1),R(a,Q.A.fo)&&qy(a,"syn",1),R(a,Q.A.Le)&&(qy(a,"em_event",1),qy(a,"sp",1)))},DN=function(a){if(ty(a)){var b=pk;b&&qy(a,"tft",Number(b))}},EN=function(a){function b(e){var f=rN(J.m.fb,e);tb(f,function(g,h){U(a,g,h)})}if(ty(a)){var c=sw(a,"ccd_add_1p_data",!1)?1:0;qy(a,"ude",c);var d=O(a.D,J.m.fb);d!==void 0?(b(d),U(a,J.m.wc,"c")):b(R(a,Q.A.jb));S(a,
Q.A.jb)}},FN=function(a){if(ty(a)){var b=Wv();b&&qy(a,"us_privacy",b);var c=yr();c&&qy(a,"gdpr",c);var d=xr();d&&qy(a,"gdpr_consent",d);var e=Jv.gppString;e&&qy(a,"gpp",e);var f=Jv.C;f&&qy(a,"gpp_sid",f)}},GN=function(a){ty(a)&&nn()&&O(a.D,J.m.ya)&&qy(a,"adr",1)},HN=function(a){if(ty(a)){var b=E(90)?wo():"";b&&qy(a,"gcsub",b)}},IN=function(a){if(ty(a)){O(a.D,J.m.Pb,void 0,4)===!1&&qy(a,"ngs",1);uo()&&qy(a,"ga_rd",1);sK()||qy(a,"ngst",1);var b=yo();b&&qy(a,"etld",b)}},JN=function(a){},KN=function(a){ty(a)&&nn()&&qy(a,"rnd",wv())},tN=[J.m.U,J.m.V];
var LN=function(a,b){var c;a:{var d=BM(a);if(d){if(zM(d,a)){c=d;break a}L(25);a.isAborted=!0}c=void 0}var e=c;return{clientId:qM(a,b),xb:e}},MN=function(a,b,c,d,e){var f=Po(O(a.D,J.m.Rb));if(O(a.D,J.m.Lc)&&O(a.D,J.m.Kc))f?oM(a,f,1):(L(127),a.isAborted=!0);else{var g=f?1:8;S(a,Q.A.eh,!1);f||(f=rM(a),g=3);f||(f=b,g=5);if(!f){var h=P(J.m.ja),m=kM();f=!m.from_cookie||h?m.vid:void 0;g=6}f?f=""+f:(f=Ns(),g=7,S(a,Q.A.Hf,!0),S(a,Q.A.eh,!0));oM(a,f,g)}var n=R(a,Q.A.hb),p=Math.floor(n/1E3),q=void 0;R(a,Q.A.eh)||
(q=AM(a)||c);var r=vb(O(a.D,J.m.qf,30));r=Math.min(475,r);r=Math.max(5,r);var t=vb(O(a.D,J.m.ji,1E4)),u=vM(q);S(a,Q.A.Hf,!1);S(a,Q.A.oe,!1);S(a,Q.A.Kf,0);u&&u.j&&S(a,Q.A.Kf,Math.max(0,u.j-Math.max(0,p-u.t)));var v=!1;if(!u){S(a,Q.A.Hf,!0);v=!0;var w={};u=(w.s=String(p),w.o=1,w.g=!1,w.t=p,w.l=!1,w.h=void 0,w)}p>u.t+r*60&&(v=!0,u.s=String(p),u.o++,u.g=!1,u.h=void 0);if(v)S(a,Q.A.oe,!0),d.Ep(a);else if(d.up()>t||a.eventName===J.m.Yc)u.g=!0;R(a,Q.A.ke)?O(a.D,J.m.Ma)?u.l=!0:(u.l&&!E(9)&&(u.h=void 0),u.l=
!1):u.l=!1;var y=u.h;if(R(a,Q.A.ke)||ty(a)){var z=O(a.D,J.m.Jg),C=z?1:8;z||(z=y,C=4);z||(z=Ms(),C=7);var D=z.toString(),G=C,I=R(a,Q.A.Zj);if(I===void 0||G<=I)U(a,J.m.Jg,D),S(a,Q.A.Zj,G)}e?(a.copyToHitData(J.m.Wb,u.s),a.copyToHitData(J.m.Ug,u.o),a.copyToHitData(J.m.Tg,u.g?1:0)):(U(a,J.m.Wb,u.s),U(a,J.m.Ug,u.o),U(a,J.m.Tg,u.g?1:0));S(a,J.m.bi,u.l?1:0);wk()&&S(a,Q.A.Jf,u.d||Pb())};var ON=function(a){for(var b={},c=String(NN.cookie).split(";"),d=0;d<c.length;d++){var e=c[d].split("="),f=e[0].replace(/^\s*|\s*$/g,"");if(f&&a(f)){var g=e.slice(1).join("=").replace(/^\s*|\s*$/g,"");g&&(g=decodeURIComponent(g));var h=void 0,m=void 0;((h=b)[m=f]||(h[m]=[])).push(g)}}return b},PN=function(){return ON(function(a){return a==="AMP_TOKEN"}).AMP_TOKEN||[]};var QN=window,NN=document,RN=function(a){var b=QN._gaUserPrefs;if(b&&b.ioo&&b.ioo()||NN.documentElement.hasAttribute("data-google-analytics-opt-out")||a&&QN["ga-disable-"+a]===!0)return!0;try{var c=QN.external;if(c&&c._gaUserPrefs&&c._gaUserPrefs=="oo")return!0}catch(f){}for(var d=PN(),e=0;e<d.length;e++)if(d[e]=="$OPT_OUT")return!0;return NN.getElementById("__gaOptOutExtension")?!0:!1};
var TN=function(a){return!a||SN.test(a)||Co.hasOwnProperty(a)},UN=function(a){var b=J.m.Nc,c;c||(c=function(){});Zv(a,b)!==void 0&&U(a,b,c(Zv(a,b)))},VN=function(a){var b=a.indexOf("?"),c=b===-1?a:a.substring(0,b),d=Wk(c);d&&(c=d);return b===-1?c:""+c+a.substring(b)},WN=function(a){O(a.D,J.m.Hb)&&(P(J.m.ja)||O(a.D,J.m.Rb)||U(a,J.m.nl,!0));var b;var c;c=c===void 0?3:c;var d=x.location.href;if(d){var e=cl(d).search.replace("?",""),f=Uk(e,"_gl",!1,!0)||"";b=f?ht(f,c)!==void 0:!1}else b=!1;b&&ty(a)&&
qy(a,"glv",1);if(a.eventName!==J.m.qa)return{};O(a.D,J.m.Hb)&&$u(["aw","dc"]);bv(["aw","dc"]);var g=IM(a),h=GM(a);return Object.keys(g).length?g:h},XN=function(a){var b=Kb(a.D.getMergedValues(J.m.oa,1,No(Oq.C[J.m.oa])),".");b&&U(a,J.m.Ub,b);var c=Kb(a.D.getMergedValues(J.m.oa,2),".");c&&U(a,J.m.Tb,c)},YN={jp:""},ZN={},$N=(ZN[J.m.Se]=1,ZN[J.m.Te]=1,ZN[J.m.Ue]=1,ZN[J.m.Ve]=1,ZN[J.m.Xe]=1,ZN[J.m.Ye]=1,ZN),SN=/^(_|ga_|google_|gtag\.|firebase_).*$/,aO=[rw,
ow,aw,tw,XN,Rw],bO=function(a){this.N=a;this.C=this.xb=this.clientId=void 0;this.Da=this.R=!1;this.sb=0;this.P=!1;this.Va=!0;this.fa={jj:!1};this.ma=new nN;this.H=new gM};k=bO.prototype;k.kq=function(a,b,c){var d=this,e=Op(this.N);if(e)if(c.eventMetadata[Q.A.yd]&&a.charAt(0)==="_")c.onFailure();else{a!==J.m.qa&&a!==J.m.Fb&&TN(a)&&L(58);cO(c.C);var f=new WH(e,a,c);S(f,Q.A.hb,b);var g=[J.m.ja],h=ty(f);S(f,Q.A.fh,h);if(sw(f,J.m.fe,O(f.D,J.m.fe))||h)g.push(J.m.U),g.push(J.m.V);dz(function(){wp(function(){d.lq(f)},
g)});E(88)&&a===J.m.qa&&sw(f,"ga4_ads_linked",!1)&&zn(Bn(an.X.Fa),function(){d.iq(a,c,f)})}else c.onFailure()};k.iq=function(a,b,c){function d(){for(var h=l(aO),m=h.next();!m.done;m=h.next()){var n=m.value;n(f);if(f.isAborted)break}R(f,Q.A.Ba)||f.isAborted||fA(f)}var e=Op(this.N),f=new WH(e,a,b);S(f,Q.A.ia,K.J.Ha);S(f,Q.A.Ba,!0);S(f,Q.A.fh,R(c,Q.A.fh));var g=[J.m.U,J.m.V];wp(function(){d();P(g)||vp(function(h){var m,n;m=h.consentEventId;n=h.consentPriorityId;S(f,Q.A.da,!0);S(f,Q.A.Ie,m);S(f,Q.A.Je,
n);d()},g)},g)};k.lq=function(a){var b=this;try{rw(a);if(a.isAborted){jM();return}E(165)||(this.C=a);dO(a);eO(a);fO(a);gO(a);E(138)&&(a.isAborted=!0);iw(a);var c={};NM(a,c);if(a.isAborted){a.D.onFailure();jM();return}E(165)&&(this.C=a);var d=c.Ko;c.Xo===0&&hM(25);d===0&&hM(26);tw(a);S(a,Q.A.Qf,an.X.Fc);hO(a);iO(a);this.jo(a);this.H.Kq(a);jO(a);kO(a);lO(a);mO(a);this.zm(WN(a));var e=a.eventName===J.m.qa;e&&(this.P=!0);nO(a);e&&!a.isAborted&&this.sb++>0&&hM(17);oO(a);pO(a);MN(a,this.clientId,this.xb,
this.H,!this.Da);qO(a);rO(a);sO(a);tO(a,this.fa);this.Va=uO(a,this.Va);vO(a);wO(a);xO(a);yO(a);zO(a);JM(a);PM(a);KN(a);JN(a);IN(a);HN(a);GN(a);FN(a);DN(a);CN(a);AN(a);yN(a);xN(a);wN(a);vN(a);KM(a);LM(a);AO(a);BO(a);CO(a);kw(a);jw(a);qw(a);DO(a);EO(a);Rw(a);FO(a);EN(a);BN(a);GO(a);!this.P&&R(a,Q.A.Le)&&hM(18);iM(a);if(R(a,Q.A.Ba)||a.isAborted){a.D.onFailure();jM();return}this.zm(LN(a,this.clientId));this.Da=!0;this.Hq(a);HO(a);zN(function(f){b.Ol(f)},a);this.H.Cj();IO(a);pw(a);if(a.isAborted){a.D.onFailure();
jM();return}this.Ol(a);a.D.onSuccess()}catch(f){a.D.onFailure()}jM()};k.Ol=function(a){this.ma.add(a)};k.zm=function(a){var b=a.clientId,c=a.xb;b&&c&&(this.clientId=b,this.xb=c)};k.flush=function(){this.ma.flush()};k.Hq=function(a){var b=this;if(!this.R){var c=P(J.m.V),d=P(J.m.ja),e=[J.m.V,J.m.ja];E(213)&&e.push(J.m.U);up(e,function(){var f=P(J.m.V),g=P(J.m.ja),h=!1,m={},n={};if(d!==g&&b.C&&b.xb&&b.clientId){var p=b.clientId,q;var r=vM(b.xb);q=r?r.h:void 0;if(g){var t=rM(b.C);if(t){b.clientId=t;var u=
AM(b.C);u&&(b.xb=xM(u,b.xb,b.C))}else pM(b.clientId,b.C),mM(b.clientId,!0);zM(b.xb,b.C);h=!0;m[J.m.fi]=p;E(69)&&q&&(m[J.m.Ln]=q)}else b.xb=void 0,b.clientId=void 0,x.gaGlobal={}}f&&!c&&(h=!0,n[Q.A.Ff]=!0,m[J.m.Rh]=Ko[J.m.V]);if(h){var v=Xw(b.N,J.m.Wd,m);$w(v,a.D.eventId,{eventMetadata:n})}d=g;c=f;b.fa.jj=!0});this.R=!0}};k.jo=function(a){a.eventName!==J.m.Fb&&this.H.io(a)};var fO=function(a){var b=A.location.protocol;b!=="http:"&&b!=="https:"&&(L(29),a.isAborted=!0)},gO=function(a){uc&&uc.loadPurpose===
"preview"&&(L(30),a.isAborted=!0)},hO=function(a){var b={prefix:String(O(a.D,J.m.eb,"")),path:String(O(a.D,J.m.Sb,"/")),flags:String(O(a.D,J.m.Ab,"")),domain:String(O(a.D,J.m.pb,"auto")),Cc:Number(O(a.D,J.m.qb,63072E3))};S(a,Q.A.xa,b)},jO=function(a){R(a,Q.A.zd)?S(a,Q.A.ke,!1):sw(a,"ccd_add_ec_stitching",!1)&&S(a,Q.A.ke,!0)},kO=function(a){if(sw(a,"ccd_add_1p_data",!1)){var b=a.D.H[J.m.Vg];if(Pk(b)){var c=O(a.D,J.m.fb);if(c===null)S(a,Q.A.ve,null);else if(b.enable_code&&ld(c)&&S(a,Q.A.ve,c),ld(b.selectors)&&
!R(a,Q.A.nh)){var d={};S(a,Q.A.nh,Nk(b.selectors,d));E(60)&&a.mergeHitDataForKey(J.m.sc,{ec_data_layer:Jk(d)})}}}},lO=function(a){if(E(91)&&!E(88)&&sw(a,"ga4_ads_linked",!1)&&a.eventName===J.m.qa){var b=O(a.D,J.m.Ra)!==!1;if(b){var c=Xv(a);c.Cc&&(c.Cc=Math.min(c.Cc,7776E3));Yv({xe:b,De:No(O(a.D,J.m.Ta)),He:!!O(a.D,J.m.Hb),Rc:c})}}},mO=function(a){var b=Fr(a.D);O(a.D,J.m.Vb)===!0&&(b=!1);S(a,Q.A.Ih,b)},AO=function(a){if(!$y(x))L(87);else if(ez!==void 0){L(85);var b=Yy(x);b?O(a.D,J.m.Sg)&&!ty(a)||cz(b,
a):L(86)}},nO=function(a){a.eventName===J.m.qa&&(O(a.D,J.m.rb,!0)?(a.D.C[J.m.oa]&&(a.D.N[J.m.oa]=a.D.C[J.m.oa],a.D.C[J.m.oa]=void 0,U(a,J.m.oa)),a.eventName=J.m.Yc):a.isAborted=!0)},iO=function(a){function b(c,d){Ao[c]||d===void 0||U(a,c,d)}tb(a.D.N,b);tb(a.D.C,b)},qO=function(a){var b=fq(a.D),c=function(d,e){$N[d]&&U(a,d,e)};ld(b[J.m.We])?tb(b[J.m.We],function(d,e){c((J.m.We+"_"+d).toLowerCase(),e)}):tb(b,c)},oO=XN,HO=function(a){if(E(132)&&ty(a)&&!(zc("; wv")||zc("FBAN")||zc("FBAV")||Bc())&&P(J.m.ja)){S(a,
Q.A.pl,!0);ty(a)&&qy(a,"sw_exp",1);a:{if(!E(132)||!ty(a))break a;var b=hl(kl(a.D),"/_/service_worker");Ry(b);}}},DO=function(a){if(a.eventName===J.m.Fb){var b=O(a.D,J.m.rc),c=O(a.D,J.m.Ic),d;d=Zv(a,b);c(d||O(a.D,b));a.isAborted=!0}},rO=function(a){if(!O(a.D,J.m.Kc)||!O(a.D,J.m.Lc)){var b=a.copyToHitData,c=J.m.Ca,d="",e=A.location;if(e){var f=e.pathname||"";f.charAt(0)!=="/"&&(f="/"+f);var g=e.search||
"";if(g&&g[0]==="?")for(var h=g.substring(1).split("&"),m=0;m<h.length;++m){var n=h[m].split("=");n&&n.length===2&&n[0]==="wbraid"&&(g=g.replace(/([?&])wbraid=[^&]+/,"$1wbraid="+Mb(n[1])))}d=e.protocol+"//"+e.hostname+f+g}b.call(a,c,d,VN);var p=a.copyToHitData,q=J.m.Ya,r;a:{var t=us("_opt_expid",void 0,void 0,J.m.ja)[0];if(t){var u=Wk(t);if(u){var v=u.split("$");if(v.length===3){r=v[2];break a}}}var w=Dp.ga4_referrer_override;if(w!==void 0)r=w;else{var y=Ck("gtm.gtagReferrer."+a.target.destinationId),
z=A.referrer;r=y?""+y:z}}p.call(a,q,r||void 0,VN);a.copyToHitData(J.m.Gb,A.title);a.copyToHitData(J.m.Bb,(uc.language||"").toLowerCase());var C=hx();a.copyToHitData(J.m.Nc,C.width+"x"+C.height);E(145)&&a.copyToHitData(J.m.pf,void 0,VN);E(87)&&zv()&&a.copyToHitData(J.m.ld,"1")}},tO=function(a,b){E(213)&&b.jj&&(S(a,Q.A.da,!0),b.jj=!1,wk()&&S(a,Q.A.Jf,Pb()))},uO=function(a,b){var c=R(a,Q.A.Kf);c=c||0;var d=P(J.m.U),e=!b&&d,f;f=E(213)?!!R(a,Q.A.da):e||!!R(a,Q.A.Ff)||!!Zv(a,J.m.fi);var g=c===0||f;S(a,
Q.A.Fi,g);g&&S(a,Q.A.Kf,60);return d},vO=function(a){S(a,Q.A.ug,!1);S(a,Q.A.Rd,!1);if(!ty(a)&&!R(a,Q.A.zd)&&O(a.D,J.m.Pb)!==!1&&sK()&&P([J.m.U,J.m.ja])){var b=uy(a);(R(a,Q.A.oe)||O(a.D,J.m.fi))&&S(a,Q.A.ug,!!b);b&&R(a,Q.A.Fi)&&R(a,Q.A.ml)&&S(a,Q.A.Rd,!0)}},wO=function(a){S(a,Q.A.vg,!1);S(a,Q.A.wg,!1);if(!wo()&&wk()&&!ty(a)&&!R(a,Q.A.zd)&&R(a,Q.A.Fi)){var b=R(a,Q.A.Rd);R(a,Q.A.Jf)&&(b?S(a,Q.A.wg,!0):S(a,Q.A.vg,!0))}},zO=function(a){a.copyToHitData(J.m.mi);for(var b=O(a.D,J.m.gi)||[],c=0;c<b.length;c++){var d=
b[c];if(d.rule_result){a.copyToHitData(J.m.mi,d.traffic_type);hM(3);break}}},IO=function(a){a.copyToHitData(J.m.Ck);O(a.D,J.m.Sg)&&(U(a,J.m.Sg,!0),ty(a)||UN(a))},EO=function(a){a.copyToHitData(J.m.Ma);a.copyToHitData(J.m.Xb)},sO=function(a){sw(a,"google_ng")&&!uo()?a.copyToHitData(J.m.ee,1):lw(a)},GO=function(a){var b=O(a.D,J.m.Lc);b&&hM(12);R(a,Q.A.Le)&&hM(14);var c=Pm(Em());(b||Ym(c)||c&&c.parent&&c.context&&c.context.source===5)&&hM(19)},dO=function(a){if(RN(a.target.destinationId))L(28),a.isAborted=
!0;else if(E(144)){var b=Om();if(b&&Array.isArray(b.destinations))for(var c=0;c<b.destinations.length;c++)if(RN(b.destinations[c])){L(125);a.isAborted=!0;break}}},BO=function(a){Vl("attribution-reporting")&&U(a,J.m.dd,"1")},eO=function(a){if(YN.jp.replace(/\s+/g,"").split(",").indexOf(a.eventName)>=0)a.isAborted=!0;else{var b=ry(a);b&&b.blacklisted&&(a.isAborted=!0)}},xO=function(a){var b=function(c){return!!c&&c.conversion};S(a,Q.A.Gf,b(ry(a)));R(a,Q.A.Hf)&&S(a,Q.A.kl,b(ry(a,"first_visit")));R(a,
Q.A.oe)&&S(a,Q.A.ol,b(ry(a,"session_start")))},yO=function(a){Eo.hasOwnProperty(a.eventName)&&(S(a,Q.A.jl,!0),a.copyToHitData(J.m.sa),a.copyToHitData(J.m.Sa))},FO=function(a){if(!ty(a)&&R(a,Q.A.Gf)&&P(J.m.U)&&sw(a,"ga4_ads_linked",!1)){var b=Xv(a),c=qu(b.prefix),d=Sv(c);U(a,J.m.Xd,d.th);U(a,J.m.Zd,d.wh);U(a,J.m.Yd,d.uh)}},CO=function(a){if(E(122)){var b=wo();b&&S(a,Q.A.Yn,b)}},pO=function(a){S(a,Q.A.ml,uy(a)&&O(a.D,J.m.Pb)!==!1&&sK()&&!uo())};
function cO(a){tb(a,function(c){c.charAt(0)==="_"&&delete a[c]});var b=a[J.m.Xb]||{};tb(b,function(c){c.charAt(0)==="_"&&delete b[c]})};var KO=function(a){if(!JO(a)){var b=!1,c=function(){!b&&JO(a)&&(b=!0,Mc(A,"visibilitychange",c),E(5)&&Mc(A,"prerenderingchange",c),L(55))};Lc(A,"visibilitychange",c);E(5)&&Lc(A,"prerenderingchange",c);L(54)}},JO=function(a){if(E(5)&&"prerendering"in A?A.prerendering:A.visibilityState==="prerender")return!1;a();return!0};function LO(a,b){KO(function(){var c=Op(a);if(c){var d=MO(c,b);Nq(a,d,an.X.Fc)}});}function MO(a,b){var c=function(){};var d=new bO(a.id),e=a.prefix==="MC";c=function(f,g,h,m){e&&(m.eventMetadata[Q.A.zd]=!0);d.kq(g,h,m)};NO(a,d,b);return c}
function NO(a,b,c){var d=b.H,e={},f={eventId:c,eventMetadata:(e[Q.A.Lj]=!0,e),deferrable:!0};d.qq(function(){dM=!0;Oq.flush();d.xh()>=1E3&&uc.sendBeacon!==void 0&&Pq(J.m.Wd,{},a.id,f);b.flush();d.Am(function(){dM=!1;d.Am()})});};var OO=MO;function QO(a,b,c){var d=this;}QO.M="internal.gtagConfig";
function SO(a,b){}
SO.publicName="gtagSet";function TO(){var a={};return a};function UO(a){}UO.M="internal.initializeServiceWorker";function VO(a,b){}VO.publicName="injectHiddenIframe";var WO=function(){var a=0;return function(b){switch(b){case 1:a|=1;break;case 2:a|=2;break;case 3:a|=4}return a}}();
function XO(a,b,c,d,e){}XO.M="internal.injectHtml";var aP={};
function cP(a,b,c,d){}var dP={dl:1,id:1},eP={};
function fP(a,b,c,d){}E(160)?fP.publicName="injectScript":cP.publicName="injectScript";fP.M="internal.injectScript";function gP(){return xo()}gP.M="internal.isAutoPiiEligible";function hP(a){var b=!0;return b}hP.publicName="isConsentGranted";function iP(a){var b=!1;return b}iP.M="internal.isDebugMode";function jP(){return vo()}jP.M="internal.isDmaRegion";function kP(a){var b=!1;return b}kP.M="internal.isEntityInfrastructure";function lP(a){var b=!1;if(!sh(a))throw F(this.getName(),["number"],[a]);b=E(a);return b}lP.M="internal.isFeatureEnabled";function mP(){var a=!1;return a}mP.M="internal.isFpfe";function nP(){var a=!1;return a}nP.M="internal.isGcpConversion";function oP(){var a=!1;return a}oP.M="internal.isLandingPage";function pP(){var a=!1;return a}pP.M="internal.isOgt";function qP(){var a;return a}qP.M="internal.isSafariPcmEligibleBrowser";function rP(){var a=Ph(function(b){EF(this).log("error",b)});a.publicName="JSON";return a};function sP(a){var b=void 0;return Cd(b)}sP.M="internal.legacyParseUrl";function tP(){return!1}
var uP={getItem:function(a){var b=null;return b},setItem:function(a,b){return!1},removeItem:function(a){}};function vP(){}vP.publicName="logToConsole";function wP(a,b){}wP.M="internal.mergeRemoteConfig";function xP(a,b,c){c=c===void 0?!0:c;var d=[];return Cd(d)}xP.M="internal.parseCookieValuesFromString";function yP(a){var b=void 0;if(typeof a!=="string")return;a&&Gb(a,"//")&&(a=A.location.protocol+a);if(typeof URL==="function"){var c;a:{var d;try{d=new URL(a)}catch(w){c=void 0;break a}for(var e={},f=Array.from(d.searchParams),g=0;g<f.length;g++){var h=f[g][0],m=f[g][1];e.hasOwnProperty(h)?typeof e[h]==="string"?e[h]=[e[h],m]:e[h].push(m):e[h]=m}c=Cd({href:d.href,origin:d.origin,protocol:d.protocol,username:d.username,password:d.password,host:d.host,
hostname:d.hostname,port:d.port,pathname:d.pathname,search:d.search,searchParams:e,hash:d.hash})}return c}var n;try{n=cl(a)}catch(w){return}if(!n.protocol||!n.host)return;var p={};if(n.search)for(var q=n.search.replace("?","").split("&"),r=0;r<q.length;r++){var t=q[r].split("="),u=t[0],v=Wk(t.splice(1).join("="))||"";v=v.replace(/\+/g," ");p.hasOwnProperty(u)?typeof p[u]==="string"?p[u]=[p[u],v]:p[u].push(v):p[u]=v}n.searchParams=p;n.origin=n.protocol+"//"+n.host;n.username="";n.password="";b=Cd(n);
return b}yP.publicName="parseUrl";function zP(a){}zP.M="internal.processAsNewEvent";function AP(a,b,c){var d;return d}AP.M="internal.pushToDataLayer";function BP(a){var b=Ca.apply(1,arguments),c=!1;if(!nh(a))throw F(this.getName(),["string"],arguments);for(var d=[this,a],e=l(b),f=e.next();!f.done;f=e.next())d.push(Bd(f.value,this.K,1));try{H.apply(null,d),c=!0}catch(g){return!1}return c}BP.publicName="queryPermission";function CP(a){var b=this;}CP.M="internal.queueAdsTransmission";function DP(a,b){var c=void 0;return c}DP.publicName="readAnalyticsStorage";function EP(){var a="";return a}EP.publicName="readCharacterSet";function FP(){return ek}FP.M="internal.readDataLayerName";function GP(){var a="";return a}GP.publicName="readTitle";function HP(a,b){var c=this;if(!nh(a)||!jh(b))throw F(this.getName(),["string","function"],arguments);Sw(a,function(d){b.invoke(c.K,Cd(d,c.K,1))});}HP.M="internal.registerCcdCallback";function IP(a,b){return!0}IP.M="internal.registerDestination";var JP=["config","event","get","set"];function KP(a,b,c){}KP.M="internal.registerGtagCommandListener";function LP(a,b){var c=!1;return c}LP.M="internal.removeDataLayerEventListener";function MP(a,b){}
MP.M="internal.removeFormData";function NP(){}NP.publicName="resetDataLayer";function OP(a,b,c){var d=void 0;return d}OP.M="internal.scrubUrlParams";function PP(a){}PP.M="internal.sendAdsHit";function QP(a,b,c,d){if(arguments.length<2||!hh(d)||!hh(c))throw F(this.getName(),["any","any","Object|undefined","Object|undefined"],arguments);var e=c?Bd(c):{},f=Bd(a),g=Array.isArray(f)?f:[f];b=String(b);var h=d?Bd(d):{},m=EF(this);h.originatingEntity=tG(m);for(var n=0;n<g.length;n++){var p=g[n];if(typeof p==="string"){var q=
{};md(e,q);var r={};md(h,r);var t=Xw(p,b,q);$w(t,h.eventId||m.eventId,r)}}}QP.M="internal.sendGtagEvent";function RP(a,b,c){}RP.publicName="sendPixel";function SP(a,b){}SP.M="internal.setAnchorHref";function TP(a){}TP.M="internal.setContainerConsentDefaults";function UP(a,b,c,d){var e=this;d=d===void 0?!0:d;var f=!1;
return f}UP.publicName="setCookie";function VP(a){}VP.M="internal.setCorePlatformServices";function WP(a,b){}WP.M="internal.setDataLayerValue";function XP(a){}XP.publicName="setDefaultConsentState";function YP(a,b){}YP.M="internal.setDelegatedConsentType";function ZP(a,b){}ZP.M="internal.setFormAction";function $P(a,b,c){c=c===void 0?!1:c;}$P.M="internal.setInCrossContainerData";function aQ(a,b,c){return!1}aQ.publicName="setInWindow";function bQ(a,b,c){}bQ.M="internal.setProductSettingsParameter";function cQ(a,b,c){if(!nh(a)||!nh(b)||arguments.length!==3)throw F(this.getName(),["string","string","any"],arguments);for(var d=b.split("."),e=Rq(a),f=0;f<d.length-1;f++){if(e[d[f]]===void 0)e[d[f]]={};else if(!ld(e[d[f]]))throw Error("setRemoteConfigParameter failed, path contains a non-object type: "+d[f]);e=e[d[f]]}e[d[f]]=Bd(c,this.K,1);}cQ.M="internal.setRemoteConfigParameter";function dQ(a,b){}dQ.M="internal.setTransmissionMode";function eQ(a,b,c,d){var e=this;}eQ.publicName="sha256";function fQ(a,b,c){}
fQ.M="internal.sortRemoteConfigParameters";function gQ(a){}gQ.M="internal.storeAdsBraidLabels";function hQ(a,b){var c=void 0;return c}hQ.M="internal.subscribeToCrossContainerData";var iQ={},jQ={};iQ.getItem=function(a){var b=null;H(this,"access_template_storage");var c=EF(this).Kb();jQ[c]&&(b=jQ[c].hasOwnProperty("gtm."+a)?jQ[c]["gtm."+a]:null);return b};iQ.setItem=function(a,b){H(this,"access_template_storage");var c=EF(this).Kb();jQ[c]=jQ[c]||{};jQ[c]["gtm."+a]=b;};
iQ.removeItem=function(a){H(this,"access_template_storage");var b=EF(this).Kb();if(!jQ[b]||!jQ[b].hasOwnProperty("gtm."+a))return;delete jQ[b]["gtm."+a];};iQ.clear=function(){H(this,"access_template_storage"),delete jQ[EF(this).Kb()];};iQ.publicName="templateStorage";function kQ(a,b){var c=!1;return c}kQ.M="internal.testRegex";function lQ(a){var b;return b};function mQ(a,b){var c;return c}mQ.M="internal.unsubscribeFromCrossContainerData";function nQ(a){}nQ.publicName="updateConsentState";function oQ(a){var b=!1;return b}oQ.M="internal.userDataNeedsEncryption";var pQ;function qQ(a,b,c){pQ=pQ||new $h;pQ.add(a,b,c)}function rQ(a,b){var c=pQ=pQ||new $h;if(c.C.hasOwnProperty(a))throw Error("Attempting to add a private function which already exists: "+a+".");if(c.contains(a))throw Error("Attempting to add a private function with an existing API name: "+a+".");c.C[a]=lb(b)?vh(a,b):wh(a,b)}
function sQ(){return function(a){var b;var c=pQ;if(c.contains(a))b=c.get(a,this);else{var d;if(d=c.C.hasOwnProperty(a)){var e=this.K.wb();if(e){var f=!1,g=e.Kb();if(g){Ch(g)||(f=!0);}d=f}else d=!0}if(d){var h=c.C.hasOwnProperty(a)?c.C[a]:void 0;
b=h}else throw Error(a+" is not a valid API name.");}return b}};function tQ(){var a=function(c){return void rQ(c.M,c)},b=function(c){return void qQ(c.publicName,c)};b(yF);b(FF);b(TG);b(VG);b(WG);b(cH);b(eH);b($H);b(rP());b(bI);b(wL);b(xL);b(TL);b(UL);b(VL);b(aM);b(SO);b(VO);b(hP);b(vP);b(yP);b(BP);b(EP);b(GP);b(RP);b(UP);b(XP);b(aQ);b(eQ);b(iQ);b(nQ);qQ("Math",Ah());qQ("Object",Yh);qQ("TestHelper",bi());qQ("assertApi",xh);qQ("assertThat",yh);qQ("decodeUri",Dh);qQ("decodeUriComponent",Eh);qQ("encodeUri",Fh);qQ("encodeUriComponent",Gh);qQ("fail",Lh);qQ("generateRandom",
Mh);qQ("getTimestamp",Nh);qQ("getTimestampMillis",Nh);qQ("getType",Oh);qQ("makeInteger",Qh);qQ("makeNumber",Rh);qQ("makeString",Sh);qQ("makeTableMap",Th);qQ("mock",Wh);qQ("mockObject",Xh);qQ("fromBase64",pL,!("atob"in x));qQ("localStorage",uP,!tP());qQ("toBase64",lQ,!("btoa"in x));a(xF);a(BF);a(VF);a(gG);a(nG);a(sG);a(IG);a(RG);a(UG);a(XG);a(YG);a(ZG);a($G);a(aH);a(bH);a(dH);a(fH);a(ZH);a(aI);a(cI);a(dI);a(eI);a(fI);a(gI);a(hI);a(mI);a(uI);a(vI);a(GI);a(LI);a(QI);a(ZI);a(dJ);a(qJ);a(sJ);a(GJ);a(HJ);
a(JJ);a(nL);a(oL);a(qL);a(rL);a(sL);a(tL);a(uL);a(zL);a(AL);a(BL);a(CL);a(DL);a(EL);a(FL);a(GL);a(HL);a(IL);a(JL);a(KL);a(ML);a(NL);a(OL);a(PL);a(QL);a(RL);a(SL);a(WL);a(XL);a(YL);a(ZL);a($L);a(cM);a(QO);a(UO);a(XO);a(fP);a(gP);a(iP);a(jP);a(kP);a(lP);a(mP);a(nP);a(oP);a(pP);a(qP);a(sP);a(GG);a(wP);a(xP);a(zP);a(AP);a(CP);a(FP);a(HP);a(IP);a(KP);a(LP);a(MP);a(OP);a(PP);a(QP);a(SP);a(TP);a(VP);a(WP);a(YP);a(ZP);a($P);a(bQ);a(cQ);a(dQ);a(fQ);a(gQ);a(hQ);a(kQ);a(mQ);a(oQ);rQ("internal.IframingStateSchema",
TO());
E(104)&&a(yL);E(160)?b(fP):b(cP);E(177)&&b(DP);return sQ()};var vF;
function uQ(){var a=data.sandboxed_scripts,b=data.security_groups;a:{var c=data.runtime||[],d=data.runtime_lines;vF=new Xe;vQ();Ff=uF();var e=vF,f=tQ(),g=new ud("require",f);g.Wa();e.C.C.set("require",g);Sa.set("require",g);for(var h=[],m=0;m<c.length;m++){var n=c[m];if(!Array.isArray(n)||n.length<3){if(n.length===0)continue;break a}d&&d[m]&&d[m].length&&$f(n,d[m]);try{vF.execute(n),E(120)&&rl&&n[0]===50&&h.push(n[1])}catch(r){}}E(120)&&(Sf=h)}if(a&&a.length)for(var p=0;p<a.length;p++){var q=a[p].replace(/^_*/,
"");sk[q]=["sandboxedScripts"]}wQ(b)}function vQ(){vF.Vc(function(a,b,c){Dp.SANDBOXED_JS_SEMAPHORE=Dp.SANDBOXED_JS_SEMAPHORE||0;Dp.SANDBOXED_JS_SEMAPHORE++;try{return a.apply(b,c)}finally{Dp.SANDBOXED_JS_SEMAPHORE--}})}function wQ(a){a&&tb(a,function(b,c){for(var d=0;d<c.length;d++){var e=c[d].replace(/^_*/,"");sk[e]=sk[e]||[];sk[e].push(b)}})};function xQ(a){$w(Uw("developer_id."+a,!0),0,{})};var yQ=Array.isArray;function zQ(a,b){return md(a,b||null)}function V(a){return window.encodeURIComponent(a)}function AQ(a,b,c){Kc(a,b,c)}
function BQ(a){var b=["veinteractive.com","ve-interactive.cn"];if(!a)return!1;var c=Xk(cl(a),"host");if(!c)return!1;for(var d=0;b&&d<b.length;d++){var e=b[d]&&b[d].toLowerCase();if(e){var f=c.length-e.length;f>0&&e.charAt(0)!=="."&&(f--,e="."+e);if(f>=0&&c.indexOf(e,f)===f)return!0}}return!1}function CQ(a,b,c){for(var d={},e=!1,f=0;a&&f<a.length;f++)a[f]&&a[f].hasOwnProperty(b)&&a[f].hasOwnProperty(c)&&(d[a[f][b]]=a[f][c],e=!0);return e?d:null}
function DQ(a,b){var c={};if(a)for(var d in a)a.hasOwnProperty(d)&&(c[d]=a[d]);if(b){var e=CQ(b,"parameter","parameterValue");e&&(c=zQ(e,c))}return c}function EQ(a,b,c){return a===void 0||a===c?b:a}function FQ(a,b,c){return Gc(a,b,c,void 0)}function GQ(){return x.location.href}function HQ(a,b){return Ck(a,b||2)}function IQ(a,b){x[a]=b}function JQ(a,b,c){var d=x;b&&(d[a]===void 0||c&&!d[a])&&(d[a]=b);return d[a]}

var KQ={};var Z={securityGroups:{}};
Z.securityGroups.access_template_storage=["google"],Z.__access_template_storage=function(){return{assert:function(){},T:function(){return{}}}},Z.__access_template_storage.F="access_template_storage",Z.__access_template_storage.isVendorTemplate=!0,Z.__access_template_storage.priorityOverride=0,Z.__access_template_storage.isInfrastructure=!1,Z.__access_template_storage["5"]=!1;

Z.securityGroups.read_event_data=["google"],function(){function a(b,c){return{key:c}}(function(b){Z.__read_event_data=b;Z.__read_event_data.F="read_event_data";Z.__read_event_data.isVendorTemplate=!0;Z.__read_event_data.priorityOverride=0;Z.__read_event_data.isInfrastructure=!1;Z.__read_event_data["5"]=!1})(function(b){var c=b.vtp_eventDataAccess,d=b.vtp_keyPatterns||[],e=b.vtp_createPermissionError;return{assert:function(f,g){if(g!=null&&!mb(g))throw e(f,{key:g},"Key must be a string.");if(c!=="any"){try{if(c===
"specific"&&g!=null&&Lg(g,d))return}catch(h){throw e(f,{key:g},"Invalid key filter.");}throw e(f,{key:g},"Prohibited read from event data.");}},T:a}})}();

Z.securityGroups.detect_youtube_activity_events=["google"],function(){function a(b,c){return{options:{fixMissingApi:!!c.fixMissingApi}}}(function(b){Z.__detect_youtube_activity_events=b;Z.__detect_youtube_activity_events.F="detect_youtube_activity_events";Z.__detect_youtube_activity_events.isVendorTemplate=!0;Z.__detect_youtube_activity_events.priorityOverride=0;Z.__detect_youtube_activity_events.isInfrastructure=!1;Z.__detect_youtube_activity_events["5"]=!1})(function(b){var c=!!b.vtp_allowFixMissingJavaScriptApi,
d=b.vtp_createPermissionError;return{assert:function(e,f){if(!c&&f&&f.fixMissingApi)throw d(e,{},"Prohibited option: fixMissingApi.");},T:a}})}();



Z.securityGroups.detect_history_change_events=["google"],function(){function a(){return{}}(function(b){Z.__detect_history_change_events=b;Z.__detect_history_change_events.F="detect_history_change_events";Z.__detect_history_change_events.isVendorTemplate=!0;Z.__detect_history_change_events.priorityOverride=0;Z.__detect_history_change_events.isInfrastructure=!1;Z.__detect_history_change_events["5"]=!1})(function(){return{assert:function(){},T:a}})}();


Z.securityGroups.detect_link_click_events=["google"],function(){function a(b,c){return{options:c}}(function(b){Z.__detect_link_click_events=b;Z.__detect_link_click_events.F="detect_link_click_events";Z.__detect_link_click_events.isVendorTemplate=!0;Z.__detect_link_click_events.priorityOverride=0;Z.__detect_link_click_events.isInfrastructure=!1;Z.__detect_link_click_events["5"]=!1})(function(b){var c=b.vtp_allowWaitForTags,d=b.vtp_createPermissionError;return{assert:function(e,f){if(!c&&f&&f.waitForTags)throw d(e,
{},"Prohibited option waitForTags.");},T:a}})}();
Z.securityGroups.detect_form_submit_events=["google"],function(){function a(b,c){return{options:c}}(function(b){Z.__detect_form_submit_events=b;Z.__detect_form_submit_events.F="detect_form_submit_events";Z.__detect_form_submit_events.isVendorTemplate=!0;Z.__detect_form_submit_events.priorityOverride=0;Z.__detect_form_submit_events.isInfrastructure=!1;Z.__detect_form_submit_events["5"]=!1})(function(b){var c=b.vtp_allowWaitForTags,d=b.vtp_createPermissionError;return{assert:function(e,f){if(!c&&f&&
f.waitForTags)throw d(e,{},"Prohibited option waitForTags.");},T:a}})}();
Z.securityGroups.read_container_data=["google"],Z.__read_container_data=function(){return{assert:function(){},T:function(){return{}}}},Z.__read_container_data.F="read_container_data",Z.__read_container_data.isVendorTemplate=!0,Z.__read_container_data.priorityOverride=0,Z.__read_container_data.isInfrastructure=!1,Z.__read_container_data["5"]=!1;
Z.securityGroups.listen_data_layer=["google"],function(){function a(b,c){return{eventName:c}}(function(b){Z.__listen_data_layer=b;Z.__listen_data_layer.F="listen_data_layer";Z.__listen_data_layer.isVendorTemplate=!0;Z.__listen_data_layer.priorityOverride=0;Z.__listen_data_layer.isInfrastructure=!1;Z.__listen_data_layer["5"]=!1})(function(b){var c=b.vtp_accessType,d=b.vtp_allowedEvents||[],e=b.vtp_createPermissionError;return{assert:function(f,g){if(!mb(g))throw e(f,{eventName:g},"Event name must be a string.");
if(!(c==="any"||c==="specific"&&d.indexOf(g)>=0))throw e(f,{eventName:g},"Prohibited listen on data layer event.");},T:a}})}();
Z.securityGroups.detect_user_provided_data=["google"],function(){function a(b,c){return{dataSource:c}}(function(b){Z.__detect_user_provided_data=b;Z.__detect_user_provided_data.F="detect_user_provided_data";Z.__detect_user_provided_data.isVendorTemplate=!0;Z.__detect_user_provided_data.priorityOverride=0;Z.__detect_user_provided_data.isInfrastructure=!1;Z.__detect_user_provided_data["5"]=!1})(function(b){var c=b.vtp_createPermissionError;return{assert:function(d,e){if(e!=="auto"&&e!=="manual"&&e!==
"code")throw c(d,{},"Unknown user provided data source.");if(b.vtp_limitDataSources)if(e!=="auto"||b.vtp_allowAutoDataSources){if(e==="manual"&&!b.vtp_allowManualDataSources)throw c(d,{},"Detection of user provided data via manually specified CSS selectors is not allowed.");if(e==="code"&&!b.vtp_allowCodeDataSources)throw c(d,{},"Detection of user provided data from an in-page variable is not allowed.");}else throw c(d,{},"Automatic detection of user provided data is not allowed.");},T:a}})}();



Z.securityGroups.get_url=["google"],function(){function a(b,c,d){return{component:c,queryKey:d}}(function(b){Z.__get_url=b;Z.__get_url.F="get_url";Z.__get_url.isVendorTemplate=!0;Z.__get_url.priorityOverride=0;Z.__get_url.isInfrastructure=!1;Z.__get_url["5"]=!1})(function(b){var c=b.vtp_urlParts==="any"?null:[];c&&(b.vtp_protocol&&c.push("protocol"),b.vtp_host&&c.push("host"),b.vtp_port&&c.push("port"),b.vtp_path&&c.push("path"),b.vtp_extension&&c.push("extension"),b.vtp_query&&c.push("query"),b.vtp_fragment&&
c.push("fragment"));var d=c&&b.vtp_queriesAllowed!=="any"?b.vtp_queryKeys||[]:null,e=b.vtp_createPermissionError;return{assert:function(f,g,h){if(g){if(!mb(g))throw e(f,{},"URL component must be a string.");if(c&&c.indexOf(g)<0)throw e(f,{},"Prohibited URL component: "+g);if(g==="query"&&d){if(!h)throw e(f,{},"Prohibited from getting entire URL query when query keys are specified.");if(!mb(h))throw e(f,{},"Query key must be a string.");if(d.indexOf(h)<0)throw e(f,{},"Prohibited query key: "+h);}}else if(c)throw e(f,
{},"Prohibited from getting entire URL when components are specified.");},T:a}})}();




Z.securityGroups.gct=["google"],function(){function a(b){for(var c=[],d=0;d<b.length;d++)try{c.push(new RegExp(b[d]))}catch(e){}return c}(function(b){Z.__gct=b;Z.__gct.F="gct";Z.__gct.isVendorTemplate=!0;Z.__gct.priorityOverride=0;Z.__gct.isInfrastructure=!1;Z.__gct["5"]=!0})(function(b){var c={},d=b.vtp_sessionDuration;d>0&&(c[J.m.qf]=d);c[J.m.Kg]=b.vtp_eventSettings;c[J.m.lk]=b.vtp_dynamicEventSettings;c[J.m.fe]=b.vtp_googleSignals===1;c[J.m.Dk]=b.vtp_foreignTld;c[J.m.Bk]=b.vtp_restrictDomain===
1;c[J.m.gi]=b.vtp_internalTrafficResults;var e=J.m.Ta,f=b.vtp_linker;f&&f[J.m.la]&&(f[J.m.la]=a(f[J.m.la]));c[e]=f;var g=J.m.hi,h=b.vtp_referralExclusionDefinition;h&&h.include_conditions&&(h.include_conditions=a(h.include_conditions));c[g]=h;Uq(b.vtp_trackingId,c);LO(b.vtp_trackingId,b.vtp_gtmEventId);Nc(b.vtp_gtmOnSuccess)})}();



Z.securityGroups.get=["google"],Z.__get=function(a){var b=a.vtp_settings,c=b.eventParameters||{},d=String(a.vtp_eventName),e={};e.eventId=a.vtp_gtmEventId;e.priorityId=a.vtp_gtmPriorityId;a.vtp_deferrable&&(e.deferrable=!0);var f=Xw(String(b.streamId),d,c);$w(f,e.eventId,e);a.vtp_gtmOnSuccess()},Z.__get.F="get",Z.__get.isVendorTemplate=!0,Z.__get.priorityOverride=0,Z.__get.isInfrastructure=!1,Z.__get["5"]=!1;
Z.securityGroups.detect_scroll_events=["google"],function(){function a(){return{}}(function(b){Z.__detect_scroll_events=b;Z.__detect_scroll_events.F="detect_scroll_events";Z.__detect_scroll_events.isVendorTemplate=!0;Z.__detect_scroll_events.priorityOverride=0;Z.__detect_scroll_events.isInfrastructure=!1;Z.__detect_scroll_events["5"]=!1})(function(){return{assert:function(){},T:a}})}();



Z.securityGroups.detect_form_interaction_events=["google"],function(){function a(){return{}}(function(b){Z.__detect_form_interaction_events=b;Z.__detect_form_interaction_events.F="detect_form_interaction_events";Z.__detect_form_interaction_events.isVendorTemplate=!0;Z.__detect_form_interaction_events.priorityOverride=0;Z.__detect_form_interaction_events.isInfrastructure=!1;Z.__detect_form_interaction_events["5"]=!1})(function(){return{assert:function(){},T:a}})}();
var Gp={dataLayer:Dk,callback:function(a){rk.hasOwnProperty(a)&&lb(rk[a])&&rk[a]();delete rk[a]},bootstrap:0};
function LQ(){Fp();Sm();WB();Eb(sk,Z.securityGroups);var a=Pm(Em()),b,c=a==null?void 0:(b=a.context)==null?void 0:b.source;ep(c,a==null?void 0:a.parent);c!==2&&c!==4&&c!==3||L(142);Rf={Qo:fg}}var MQ=!1;E(218)&&(MQ=Yi(47,MQ));
function po(){try{if(MQ||!Zm()){ak();Xj.P=Zi(18,"");
Xj.sb="ad_storage|analytics_storage|ad_user_data|ad_personalization";Xj.Va="ad_storage|analytics_storage|ad_user_data";Xj.Da="57f0";Xj.Da="57f0";if(E(109)){}Pa[7]=!0;var a=Ep("debugGroupId",function(){return String(Math.floor(Number.MAX_SAFE_INTEGER*Math.random()))});lp(a);Cp();iF();sr();Ip();if(Tm()){DG();MC().removeExternalRestrictions(Mm());}else{fz();Pf();Lf=Z;Mf=TE;hg=new og;uQ();LQ();RE();no||(mo=ro());
zp();$D();nD();HD=!1;A.readyState==="complete"?JD():Lc(x,"load",JD);hD();rl&&(vq(Jq),x.setInterval(Iq,864E5),vq(jF),vq(zC),vq(mA),vq(Mq),vq(rF),vq(KC),E(120)&&(vq(EC),vq(FC),vq(GC)),kF={},lF={},vq(nF),vq(oF),bj());sl&&($n(),bq(),bE(),iE(),gE(),Sn("bt",String(Xj.C?2:Xj.N?1:0)),Sn("ct",String(Xj.C?0:Xj.N?1:3)),eE());IE();ko(1);EG();nE();qk=Ab();Gp.bootstrap=qk;Xj.ma&&ZD();E(109)&&IA();E(134)&&(typeof x.name==="string"&&Gb(x.name,"web-pixel-sandbox-CUSTOM")&&bd()?xQ("dMDg0Yz"):x.Shopify&&(xQ("dN2ZkMj"),bd()&&xQ("dNTU0Yz")))}}}catch(b){ko(4),Fq()}}
(function(a){function b(){n=A.documentElement.getAttribute("data-tag-assistant-present");So(n)&&(m=h.Yk)}function c(){m&&xc?g(m):a()}if(!x[Zi(37,"__TAGGY_INSTALLED")]){var d=!1;if(A.referrer){var e=cl(A.referrer);d=Zk(e,"host")===Zi(38,"cct.google")}if(!d){var f=us(Zi(39,"googTaggyReferrer"));d=!(!f.length||!f[0].length)}d&&(x[Zi(37,"__TAGGY_INSTALLED")]=!0,Gc(Zi(40,"https://cct.google/taggy/agent.js")))}var g=function(u){var v="GTM",w="GTM";lk&&(v="OGT",w="GTAG");
var y=Zi(23,"google.tagmanager.debugui2.queue"),z=x[y];z||(z=[],x[y]=z,Gc("https://"+bk.xg+"/debug/bootstrap?id="+lg.ctid+"&src="+w+"&cond="+String(u)+"&gtm="+Ur()));var C={messageType:"CONTAINER_STARTING",data:{scriptSource:xc,containerProduct:v,debug:!1,id:lg.ctid,targetRef:{ctid:lg.ctid,isDestination:Km()},aliases:Nm(),destinations:Lm()}};C.data.resume=function(){a()};bk.Tm&&(C.data.initialPublish=!0);z.push(C)},h={co:1,bl:2,xl:3,Xj:4,Yk:5};h[h.co]="GTM_DEBUG_LEGACY_PARAM";h[h.bl]="GTM_DEBUG_PARAM";h[h.xl]="REFERRER";
h[h.Xj]="COOKIE";h[h.Yk]="EXTENSION_PARAM";var m=void 0,n=void 0,p=Xk(x.location,"query",!1,void 0,"gtm_debug");So(p)&&(m=h.bl);if(!m&&A.referrer){var q=cl(A.referrer);Zk(q,"host")===Zi(24,"tagassistant.google.com")&&(m=h.xl)}if(!m){var r=us("__TAG_ASSISTANT");r.length&&r[0].length&&(m=h.Xj)}m||b();if(!m&&Ro(n)){var t=!1;Lc(A,"TADebugSignal",function(){t||(t=!0,b(),c())},!1);x.setTimeout(function(){t||(t=!0,b(),c())},200)}else c()})(function(){E(83)&&MQ&&!ro()["0"]?oo():po()});

})()

