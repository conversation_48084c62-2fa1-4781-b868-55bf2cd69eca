import{r as i,cb as g,cS as m,j as u,aV as x,c5 as D,c6 as d,T}from"./index-CP4gzJXp.js";import{g as f}from"./dialogTitleClasses-XEjvqc9B.js";const y=o=>{const{classes:s}=o;return d({root:["root"]},f,s)},C=x(T,{name:"MuiDialogTitle",slot:"Root"})({padding:"16px 24px",flex:"0 0 auto"}),j=i.forwardRef(function(s,t){const a=g({props:s,name:"MuiDialogTitle"}),{className:r,id:e,...n}=a,l=a,c=y(l),{titleId:p=e}=i.useContext(m);return u.jsx(C,{component:"h2",className:D(c.root,r),ownerState:l,ref:t,variant:"h6",id:e??p,...n})});export{j as D};
