import{b6 as n,ba as p,aG as m,j as u,aB as g,b9 as c}from"./index-CP4gzJXp.js";import{S as h}from"./SignupForm-VPvpIcuk.js";import"./index.esm-Bw9oClnr.js";import"./yup-Bdh5_3QG.js";import"./index.esm-CVsSWzb0.js";import"./PasswordTextField-Bu7nMFM0.js";import"./ViewOnlyAlert-CkXljFy_.js";import"./Alert-BWvPB4gW.js";import"./SocialAuth-DUKTxlfk.js";const v=()=>{const{setSession:s}=n(),{trigger:o}=p(),a=m(),r=async i=>{const t=await o(i).catch(e=>{throw new Error(e.data.message)});t&&(s(t.user,t.authToken),a(c.root))};return u.jsx(h,{handleSignup:r,loginLink:g.defaultJwtLogin})};export{v as default};
