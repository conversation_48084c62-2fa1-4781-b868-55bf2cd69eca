gapi.loaded_0(function(_){var window=this;
_._F_toggles_initialize=function(a){(typeof globalThis!=="undefined"?globalThis:typeof self!=="undefined"?self:this)._F_toggles=a||[]};(0,_._F_toggles_initialize)([]);
var aa,ca,fa,ma,na,qa,Ba,Ca;aa=function(a){var b=0;return function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}}};ca=typeof Object.defineProperties=="function"?Object.defineProperty:function(a,b,c){if(a==Array.prototype||a==Object.prototype)return a;a[b]=c.value;return a};
fa=function(a){a=["object"==typeof globalThis&&globalThis,a,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global];for(var b=0;b<a.length;++b){var c=a[b];if(c&&c.Math==Math)return c}throw Error("a");};_.ha=fa(this);ma=function(a,b){if(b)a:{var c=_.ha;a=a.split(".");for(var d=0;d<a.length-1;d++){var e=a[d];if(!(e in c))break a;c=c[e]}a=a[a.length-1];d=c[a];b=b(d);b!=d&&b!=null&&ca(c,a,{configurable:!0,writable:!0,value:b})}};
ma("Symbol",function(a){if(a)return a;var b=function(f,h){this.a3=f;ca(this,"description",{configurable:!0,writable:!0,value:h})};b.prototype.toString=function(){return this.a3};var c="jscomp_symbol_"+(Math.random()*1E9>>>0)+"_",d=0,e=function(f){if(this instanceof e)throw new TypeError("Symbol is not a constructor");return new b(c+(f||"")+"_"+d++,f)};return e});
ma("Symbol.iterator",function(a){if(a)return a;a=Symbol("Symbol.iterator");for(var b="Array Int8Array Uint8Array Uint8ClampedArray Int16Array Uint16Array Int32Array Uint32Array Float32Array Float64Array".split(" "),c=0;c<b.length;c++){var d=_.ha[b[c]];typeof d==="function"&&typeof d.prototype[a]!="function"&&ca(d.prototype,a,{configurable:!0,writable:!0,value:function(){return na(aa(this))}})}return a});na=function(a){a={next:a};a[Symbol.iterator]=function(){return this};return a};
_.pa=typeof Object.create=="function"?Object.create:function(a){var b=function(){};b.prototype=a;return new b};if(typeof Object.setPrototypeOf=="function")qa=Object.setPrototypeOf;else{var sa;a:{var ua={a:!0},xa={};try{xa.__proto__=ua;sa=xa.a;break a}catch(a){}sa=!1}qa=sa?function(a,b){a.__proto__=b;if(a.__proto__!==b)throw new TypeError(a+" is not extensible");return a}:null}_.ya=qa;
_.za=function(a){var b=typeof Symbol!="undefined"&&Symbol.iterator&&a[Symbol.iterator];if(b)return b.call(a);if(typeof a.length=="number")return{next:aa(a)};throw Error("b`"+String(a));};Ba=function(a,b){return Object.prototype.hasOwnProperty.call(a,b)};Ca=typeof Object.assign=="function"?Object.assign:function(a,b){for(var c=1;c<arguments.length;c++){var d=arguments[c];if(d)for(var e in d)Ba(d,e)&&(a[e]=d[e])}return a};ma("Object.assign",function(a){return a||Ca});
ma("globalThis",function(a){return a||_.ha});ma("Reflect.setPrototypeOf",function(a){return a?a:_.ya?function(b,c){try{return(0,_.ya)(b,c),!0}catch(d){return!1}}:null});
ma("Promise",function(a){function b(){this.xf=null}function c(h){return h instanceof e?h:new e(function(k){k(h)})}if(a)return a;b.prototype.YP=function(h){if(this.xf==null){this.xf=[];var k=this;this.ZP(function(){k.E9()})}this.xf.push(h)};var d=_.ha.setTimeout;b.prototype.ZP=function(h){d(h,0)};b.prototype.E9=function(){for(;this.xf&&this.xf.length;){var h=this.xf;this.xf=[];for(var k=0;k<h.length;++k){var l=h[k];h[k]=null;try{l()}catch(m){this.Yp(m)}}}this.xf=null};b.prototype.Yp=function(h){this.ZP(function(){throw h;
})};var e=function(h){this.Ca=0;this.nf=void 0;this.Kr=[];this.DW=!1;var k=this.OF();try{h(k.resolve,k.reject)}catch(l){k.reject(l)}};e.prototype.OF=function(){function h(m){return function(n){l||(l=!0,m.call(k,n))}}var k=this,l=!1;return{resolve:h(this.Hfa),reject:h(this.BK)}};e.prototype.Hfa=function(h){if(h===this)this.BK(new TypeError("A Promise cannot resolve to itself"));else if(h instanceof e)this.mha(h);else{a:switch(typeof h){case "object":var k=h!=null;break a;case "function":k=!0;break a;
default:k=!1}k?this.Gfa(h):this.BT(h)}};e.prototype.Gfa=function(h){var k=void 0;try{k=h.then}catch(l){this.BK(l);return}typeof k=="function"?this.nha(k,h):this.BT(h)};e.prototype.BK=function(h){this.D0(2,h)};e.prototype.BT=function(h){this.D0(1,h)};e.prototype.D0=function(h,k){if(this.Ca!=0)throw Error("c`"+h+"`"+k+"`"+this.Ca);this.Ca=h;this.nf=k;this.Ca===2&&this.Wfa();this.G9()};e.prototype.Wfa=function(){var h=this;d(function(){if(h.Uda()){var k=_.ha.console;typeof k!=="undefined"&&k.error(h.nf)}},
1)};e.prototype.Uda=function(){if(this.DW)return!1;var h=_.ha.CustomEvent,k=_.ha.Event,l=_.ha.dispatchEvent;if(typeof l==="undefined")return!0;typeof h==="function"?h=new h("unhandledrejection",{cancelable:!0}):typeof k==="function"?h=new k("unhandledrejection",{cancelable:!0}):(h=_.ha.document.createEvent("CustomEvent"),h.initCustomEvent("unhandledrejection",!1,!0,h));h.promise=this;h.reason=this.nf;return l(h)};e.prototype.G9=function(){if(this.Kr!=null){for(var h=0;h<this.Kr.length;++h)f.YP(this.Kr[h]);
this.Kr=null}};var f=new b;e.prototype.mha=function(h){var k=this.OF();h.Iy(k.resolve,k.reject)};e.prototype.nha=function(h,k){var l=this.OF();try{h.call(k,l.resolve,l.reject)}catch(m){l.reject(m)}};e.prototype.then=function(h,k){function l(q,t){return typeof q=="function"?function(v){try{m(q(v))}catch(u){n(u)}}:t}var m,n,p=new e(function(q,t){m=q;n=t});this.Iy(l(h,m),l(k,n));return p};e.prototype.catch=function(h){return this.then(void 0,h)};e.prototype.Iy=function(h,k){function l(){switch(m.Ca){case 1:h(m.nf);
break;case 2:k(m.nf);break;default:throw Error("d`"+m.Ca);}}var m=this;this.Kr==null?f.YP(l):this.Kr.push(l);this.DW=!0};e.resolve=c;e.reject=function(h){return new e(function(k,l){l(h)})};e.race=function(h){return new e(function(k,l){for(var m=_.za(h),n=m.next();!n.done;n=m.next())c(n.value).Iy(k,l)})};e.all=function(h){var k=_.za(h),l=k.next();return l.done?c([]):new e(function(m,n){function p(v){return function(u){q[v]=u;t--;t==0&&m(q)}}var q=[],t=0;do q.push(void 0),t++,c(l.value).Iy(p(q.length-
1),n),l=k.next();while(!l.done)})};return e});var Da=function(a,b,c){if(a==null)throw new TypeError("The 'this' value for String.prototype."+c+" must not be null or undefined");if(b instanceof RegExp)throw new TypeError("First argument to String.prototype."+c+" must not be a regular expression");return a+""};
ma("String.prototype.startsWith",function(a){return a?a:function(b,c){var d=Da(this,b,"startsWith"),e=d.length,f=b.length;c=Math.max(0,Math.min(c|0,d.length));for(var h=0;h<f&&c<e;)if(d[c++]!=b[h++])return!1;return h>=f}});ma("Object.setPrototypeOf",function(a){return a||_.ya});ma("Symbol.dispose",function(a){return a?a:Symbol("Symbol.dispose")});
ma("WeakMap",function(a){function b(){}function c(l){var m=typeof l;return m==="object"&&l!==null||m==="function"}function d(l){if(!Ba(l,f)){var m=new b;ca(l,f,{value:m})}}function e(l){var m=Object[l];m&&(Object[l]=function(n){if(n instanceof b)return n;Object.isExtensible(n)&&d(n);return m(n)})}if(function(){if(!a||!Object.seal)return!1;try{var l=Object.seal({}),m=Object.seal({}),n=new a([[l,2],[m,3]]);if(n.get(l)!=2||n.get(m)!=3)return!1;n.delete(l);n.set(m,4);return!n.has(l)&&n.get(m)==4}catch(p){return!1}}())return a;
var f="$jscomp_hidden_"+Math.random();e("freeze");e("preventExtensions");e("seal");var h=0,k=function(l){this.Da=(h+=Math.random()+1).toString();if(l){l=_.za(l);for(var m;!(m=l.next()).done;)m=m.value,this.set(m[0],m[1])}};k.prototype.set=function(l,m){if(!c(l))throw Error("e");d(l);if(!Ba(l,f))throw Error("f`"+l);l[f][this.Da]=m;return this};k.prototype.get=function(l){return c(l)&&Ba(l,f)?l[f][this.Da]:void 0};k.prototype.has=function(l){return c(l)&&Ba(l,f)&&Ba(l[f],this.Da)};k.prototype.delete=
function(l){return c(l)&&Ba(l,f)&&Ba(l[f],this.Da)?delete l[f][this.Da]:!1};return k});
ma("Map",function(a){if(function(){if(!a||typeof a!="function"||!a.prototype.entries||typeof Object.seal!="function")return!1;try{var k=Object.seal({x:4}),l=new a(_.za([[k,"s"]]));if(l.get(k)!="s"||l.size!=1||l.get({x:4})||l.set({x:4},"t")!=l||l.size!=2)return!1;var m=l.entries(),n=m.next();if(n.done||n.value[0]!=k||n.value[1]!="s")return!1;n=m.next();return n.done||n.value[0].x!=4||n.value[1]!="t"||!m.next().done?!1:!0}catch(p){return!1}}())return a;var b=new WeakMap,c=function(k){this[0]={};this[1]=
f();this.size=0;if(k){k=_.za(k);for(var l;!(l=k.next()).done;)l=l.value,this.set(l[0],l[1])}};c.prototype.set=function(k,l){k=k===0?0:k;var m=d(this,k);m.list||(m.list=this[0][m.id]=[]);m.entry?m.entry.value=l:(m.entry={next:this[1],Jk:this[1].Jk,head:this[1],key:k,value:l},m.list.push(m.entry),this[1].Jk.next=m.entry,this[1].Jk=m.entry,this.size++);return this};c.prototype.delete=function(k){k=d(this,k);return k.entry&&k.list?(k.list.splice(k.index,1),k.list.length||delete this[0][k.id],k.entry.Jk.next=
k.entry.next,k.entry.next.Jk=k.entry.Jk,k.entry.head=null,this.size--,!0):!1};c.prototype.clear=function(){this[0]={};this[1]=this[1].Jk=f();this.size=0};c.prototype.has=function(k){return!!d(this,k).entry};c.prototype.get=function(k){return(k=d(this,k).entry)&&k.value};c.prototype.entries=function(){return e(this,function(k){return[k.key,k.value]})};c.prototype.keys=function(){return e(this,function(k){return k.key})};c.prototype.values=function(){return e(this,function(k){return k.value})};c.prototype.forEach=
function(k,l){for(var m=this.entries(),n;!(n=m.next()).done;)n=n.value,k.call(l,n[1],n[0],this)};c.prototype[Symbol.iterator]=c.prototype.entries;var d=function(k,l){var m=l&&typeof l;m=="object"||m=="function"?b.has(l)?m=b.get(l):(m=""+ ++h,b.set(l,m)):m="p_"+l;var n=k[0][m];if(n&&Ba(k[0],m))for(k=0;k<n.length;k++){var p=n[k];if(l!==l&&p.key!==p.key||l===p.key)return{id:m,list:n,index:k,entry:p}}return{id:m,list:n,index:-1,entry:void 0}},e=function(k,l){var m=k[1];return na(function(){if(m){for(;m.head!=
k[1];)m=m.Jk;for(;m.next!=m.head;)return m=m.next,{done:!1,value:l(m)};m=null}return{done:!0,value:void 0}})},f=function(){var k={};return k.Jk=k.next=k.head=k},h=0;return c});
ma("Set",function(a){if(function(){if(!a||typeof a!="function"||!a.prototype.entries||typeof Object.seal!="function")return!1;try{var c=Object.seal({x:4}),d=new a(_.za([c]));if(!d.has(c)||d.size!=1||d.add(c)!=d||d.size!=1||d.add({x:4})!=d||d.size!=2)return!1;var e=d.entries(),f=e.next();if(f.done||f.value[0]!=c||f.value[1]!=c)return!1;f=e.next();return f.done||f.value[0]==c||f.value[0].x!=4||f.value[1]!=f.value[0]?!1:e.next().done}catch(h){return!1}}())return a;var b=function(c){this.Sa=new Map;if(c){c=
_.za(c);for(var d;!(d=c.next()).done;)this.add(d.value)}this.size=this.Sa.size};b.prototype.add=function(c){c=c===0?0:c;this.Sa.set(c,c);this.size=this.Sa.size;return this};b.prototype.delete=function(c){c=this.Sa.delete(c);this.size=this.Sa.size;return c};b.prototype.clear=function(){this.Sa.clear();this.size=0};b.prototype.has=function(c){return this.Sa.has(c)};b.prototype.entries=function(){return this.Sa.entries()};b.prototype.values=function(){return this.Sa.values()};b.prototype.keys=b.prototype.values;
b.prototype[Symbol.iterator]=b.prototype.values;b.prototype.forEach=function(c,d){var e=this;this.Sa.forEach(function(f){return c.call(d,f,f,e)})};return b});var Ea=function(a,b){a instanceof String&&(a+="");var c=0,d=!1,e={next:function(){if(!d&&c<a.length){var f=c++;return{value:b(f,a[f]),done:!1}}d=!0;return{done:!0,value:void 0}}};e[Symbol.iterator]=function(){return e};return e};ma("Array.prototype.entries",function(a){return a?a:function(){return Ea(this,function(b,c){return[b,c]})}});
ma("Array.prototype.keys",function(a){return a?a:function(){return Ea(this,function(b){return b})}});ma("String.prototype.codePointAt",function(a){return a?a:function(b){var c=Da(this,null,"codePointAt"),d=c.length;b=Number(b)||0;if(b>=0&&b<d){b|=0;var e=c.charCodeAt(b);if(e<55296||e>56319||b+1===d)return e;b=c.charCodeAt(b+1);return b<56320||b>57343?e:(e-55296)*1024+b+9216}}});
ma("String.fromCodePoint",function(a){return a?a:function(b){for(var c="",d=0;d<arguments.length;d++){var e=Number(arguments[d]);if(e<0||e>1114111||e!==Math.floor(e))throw new RangeError("invalid_code_point "+e);e<=65535?c+=String.fromCharCode(e):(e-=65536,c+=String.fromCharCode(e>>>10&1023|55296),c+=String.fromCharCode(e&1023|56320))}return c}});ma("Object.entries",function(a){return a?a:function(b){var c=[],d;for(d in b)Ba(b,d)&&c.push([d,b[d]]);return c}});
ma("String.prototype.endsWith",function(a){return a?a:function(b,c){var d=Da(this,b,"endsWith");c===void 0&&(c=d.length);c=Math.max(0,Math.min(c|0,d.length));for(var e=b.length;e>0&&c>0;)if(d[--c]!=b[--e])return!1;return e<=0}});ma("Number.isFinite",function(a){return a?a:function(b){return typeof b!=="number"?!1:!isNaN(b)&&b!==Infinity&&b!==-Infinity}});
var Ka=function(a,b,c){a instanceof String&&(a=String(a));for(var d=a.length,e=0;e<d;e++){var f=a[e];if(b.call(c,f,e,a))return{i:e,dE:f}}return{i:-1,dE:void 0}};ma("Array.prototype.find",function(a){return a?a:function(b,c){return Ka(this,b,c).dE}});ma("Array.prototype.values",function(a){return a?a:function(){return Ea(this,function(b,c){return c})}});
ma("Promise.prototype.finally",function(a){return a?a:function(b){return this.then(function(c){return Promise.resolve(b()).then(function(){return c})},function(c){return Promise.resolve(b()).then(function(){throw c;})})}});ma("Object.is",function(a){return a?a:function(b,c){return b===c?b!==0||1/b===1/c:b!==b&&c!==c}});
ma("Array.prototype.includes",function(a){return a?a:function(b,c){var d=this;d instanceof String&&(d=String(d));var e=d.length;c=c||0;for(c<0&&(c=Math.max(c+e,0));c<e;c++){var f=d[c];if(f===b||Object.is(f,b))return!0}return!1}});ma("String.prototype.includes",function(a){return a?a:function(b,c){return Da(this,b,"includes").indexOf(b,c||0)!==-1}});
ma("Array.from",function(a){return a?a:function(b,c,d){c=c!=null?c:function(k){return k};var e=[],f=typeof Symbol!="undefined"&&Symbol.iterator&&b[Symbol.iterator];if(typeof f=="function"){b=f.call(b);for(var h=0;!(f=b.next()).done;)e.push(c.call(d,f.value,h++))}else for(f=b.length,h=0;h<f;h++)e.push(c.call(d,b[h],h));return e}});ma("Object.values",function(a){return a?a:function(b){var c=[],d;for(d in b)Ba(b,d)&&c.push(b[d]);return c}});
ma("Set.prototype.intersection",function(a){return a?a:function(b){if(!(this instanceof Set))throw new TypeError("Method must be called on an instance of Set.");if(typeof b!=="object"||b===null||typeof b.size!=="number"||b.size<0||typeof b.keys!=="function"||typeof b.has!=="function")throw new TypeError("Argument must be set-like");var c=new Set;if(this.size<=b.size)b={a1:this.keys(),TW:b};else{b=b.keys();if(typeof b!=="object"||b===null||typeof b.next!=="function")throw new TypeError("Invalid iterator.");
b={a1:b,TW:this}}var d=b;b=d.a1;d=d.TW;for(var e=b.next();!e.done;)d.has(e.value)&&c.add(e.value),e=b.next();return c}});ma("Array.prototype.flat",function(a){return a?a:function(b){b=b===void 0?1:b;var c=[];Array.prototype.forEach.call(this,function(d){Array.isArray(d)&&b>0?(d=Array.prototype.flat.call(d,b-1),c.push.apply(c,d)):c.push(d)});return c}});ma("Number.MAX_SAFE_INTEGER",function(){return 9007199254740991});ma("Number.MIN_SAFE_INTEGER",function(){return-9007199254740991});
ma("Number.isInteger",function(a){return a?a:function(b){return Number.isFinite(b)?b===Math.floor(b):!1}});ma("Number.isSafeInteger",function(a){return a?a:function(b){return Number.isInteger(b)&&Math.abs(b)<=Number.MAX_SAFE_INTEGER}});ma("Array.prototype.flatMap",function(a){return a?a:function(b,c){var d=[];Array.prototype.forEach.call(this,function(e,f){e=b.call(c,e,f,this);Array.isArray(e)?d.push.apply(d,e):d.push(e)});return d}});
ma("Math.imul",function(a){return a?a:function(b,c){b=Number(b);c=Number(c);var d=b&65535,e=c&65535;return d*e+((b>>>16&65535)*e+d*(c>>>16&65535)<<16>>>0)|0}});ma("Math.trunc",function(a){return a?a:function(b){b=Number(b);if(isNaN(b)||b===Infinity||b===-Infinity||b===0)return b;var c=Math.floor(Math.abs(b));return b<0?-c:c}});ma("Number.isNaN",function(a){return a?a:function(b){return typeof b==="number"&&isNaN(b)}});
ma("String.prototype.replaceAll",function(a){return a?a:function(b,c){if(b instanceof RegExp&&!b.global)throw new TypeError("String.prototype.replaceAll called with a non-global RegExp argument.");return b instanceof RegExp?this.replace(b,c):this.replace(new RegExp(String(b).replace(/([-()\[\]{}+?*.$\^|,:#<!\\])/g,"\\$1").replace(/\x08/g,"\\x08"),"g"),c)}});var La=function(a){a=Math.trunc(a)||0;a<0&&(a+=this.length);if(!(a<0||a>=this.length))return this[a]};
ma("Array.prototype.at",function(a){return a?a:La});var Ra=function(a){return a?a:La};ma("Int8Array.prototype.at",Ra);ma("Uint8Array.prototype.at",Ra);ma("Uint8ClampedArray.prototype.at",Ra);ma("Int16Array.prototype.at",Ra);ma("Uint16Array.prototype.at",Ra);ma("Int32Array.prototype.at",Ra);ma("Uint32Array.prototype.at",Ra);ma("Float32Array.prototype.at",Ra);ma("Float64Array.prototype.at",Ra);ma("String.prototype.at",function(a){return a?a:La});
ma("Array.prototype.findIndex",function(a){return a?a:function(b,c){return Ka(this,b,c).i}});_.Sa={};/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
_.Ua=_.Ua||{};_.Xa=this||self;_.Ya=_.Xa._F_toggles||[];_.$a="closure_uid_"+(Math.random()*1E9>>>0);_.bb=function(a,b){var c=Array.prototype.slice.call(arguments,1);return function(){var d=c.slice();d.push.apply(d,arguments);return a.apply(this,d)}};_.r=function(a,b){a=a.split(".");for(var c=_.Xa,d;a.length&&(d=a.shift());)a.length||b===void 0?c=c[d]&&c[d]!==Object.prototype[d]?c[d]:c[d]={}:c[d]=b};
_.cb=function(a,b){function c(){}c.prototype=b.prototype;a.N=b.prototype;a.prototype=new c;a.prototype.constructor=a;a.base=function(d,e,f){for(var h=Array(arguments.length-2),k=2;k<arguments.length;k++)h[k-2]=arguments[k];return b.prototype[e].apply(d,h)}};_.eb=window.osapi=window.osapi||{};
window.___jsl=window.___jsl||{};
(window.___jsl.cd=window.___jsl.cd||[]).push({gwidget:{parsetags:"explicit"},appsapi:{plus_one_service:"/plus/v1"},csi:{rate:.01},poshare:{hangoutContactPickerServer:"https://plus.google.com"},gappsutil:{required_scopes:["https://www.googleapis.com/auth/plus.me","https://www.googleapis.com/auth/plus.people.recommended"],display_on_page_ready:!1},appsutil:{required_scopes:["https://www.googleapis.com/auth/plus.me","https://www.googleapis.com/auth/plus.people.recommended"],display_on_page_ready:!1},
"oauth-flow":{authUrl:"https://accounts.google.com/o/oauth2/auth",proxyUrl:"https://accounts.google.com/o/oauth2/postmessageRelay",redirectUri:"postmessage"},iframes:{sharebox:{params:{json:"&"},url:":socialhost:/:session_prefix:_/sharebox/dialog"},plus:{url:":socialhost:/:session_prefix:_/widget/render/badge?usegapi=1"},":socialhost:":"https://apis.google.com",":im_socialhost:":"https://plus.googleapis.com",domains_suggest:{url:"https://domains.google.com/suggest/flow"},card:{params:{s:"#",userid:"&"},
url:":socialhost:/:session_prefix:_/hovercard/internalcard"},":signuphost:":"https://plus.google.com",":gplus_url:":"https://plus.google.com",plusone:{url:":socialhost:/:session_prefix:_/+1/fastbutton?usegapi=1"},plus_share:{url:":socialhost:/:session_prefix:_/+1/sharebutton?plusShare=true&usegapi=1"},plus_circle:{url:":socialhost:/:session_prefix:_/widget/plus/circle?usegapi=1"},plus_followers:{url:":socialhost:/_/im/_/widget/render/plus/followers?usegapi=1"},configurator:{url:":socialhost:/:session_prefix:_/plusbuttonconfigurator?usegapi=1"},
appcirclepicker:{url:":socialhost:/:session_prefix:_/widget/render/appcirclepicker"},page:{url:":socialhost:/:session_prefix:_/widget/render/page?usegapi=1"},person:{url:":socialhost:/:session_prefix:_/widget/render/person?usegapi=1"},community:{url:":ctx_socialhost:/:session_prefix::im_prefix:_/widget/render/community?usegapi=1"},follow:{url:":socialhost:/:session_prefix:_/widget/render/follow?usegapi=1"},commentcount:{url:":socialhost:/:session_prefix:_/widget/render/commentcount?usegapi=1"},comments:{url:":socialhost:/:session_prefix:_/widget/render/comments?usegapi=1"},
blogger:{url:":socialhost:/:session_prefix:_/widget/render/blogger?usegapi=1"},youtube:{url:":socialhost:/:session_prefix:_/widget/render/youtube?usegapi=1"},reportabuse:{url:":socialhost:/:session_prefix:_/widget/render/reportabuse?usegapi=1"},additnow:{url:":socialhost:/additnow/additnow.html"},appfinder:{url:"https://workspace.google.com/:session_prefix:marketplace/appfinder?usegapi=1"},":source:":"1p"},poclient:{update_session:"google.updateSessionCallback"},"googleapis.config":{rpc:"/rpc",root:"https://content.googleapis.com",
"root-1p":"https://clients6.google.com",useGapiForXd3:!0,xd3:"/static/proxy.html",auth:{useInterimAuth:!1}},report:{apis:["iframes\\..*","gadgets\\..*","gapi\\.appcirclepicker\\..*","gapi\\.client\\..*"],rate:1E-4},client:{perApiBatch:!0},gen204logger:{interval:3E4,rate:.001,batch:!1}});
var kb;_.ib=function(a){return function(){return _.gb[a].apply(this,arguments)}};_.jb=function(a,b){if(Error.captureStackTrace)Error.captureStackTrace(this,_.jb);else{var c=Error().stack;c&&(this.stack=c)}a&&(this.message=String(a));b!==void 0&&(this.cause=b);this.VZ=!0};kb=function(a,b){a=a.split("%s");for(var c="",d=a.length-1,e=0;e<d;e++)c+=a[e]+(e<b.length?b[e]:"%s");_.jb.call(this,c+a[d])};_.gb=[];_.cb(_.jb,Error);_.jb.prototype.name="CustomError";_.cb(kb,_.jb);kb.prototype.name="AssertionError";
var wb,yb,zb;_.lb=function(a,b){return _.gb[a]=b};_.nb=function(a,b){throw Error(b===void 0?"unexpected value "+a+"!":b);};_.qb=function(a,b){return(0,_.ob)(a,b)>=0};_.sb=function(a){var b=[],c=0,d;for(d in a)b[c++]=a[d];return b};_.ub=function(a){var b=typeof a;return b=="object"&&a!=null||b=="function"};
_.y=function(a,b){a.prototype=(0,_.pa)(b.prototype);a.prototype.constructor=a;if(_.ya)(0,_.ya)(a,b);else for(var c in b)if(c!="prototype")if(Object.defineProperties){var d=Object.getOwnPropertyDescriptor(b,c);d&&Object.defineProperty(a,c,d)}else a[c]=b[c];a.N=b.prototype};_.vb=function(a,b){a=a.split(".");b=b||_.Xa;for(var c=0;c<a.length;c++)if(b=b[a[c]],b==null)return null;return b};wb=function(a){var b=_.vb("WIZ_global_data.oxN3nb");a=b&&b[a];return a!=null?a:!1};
yb=function(a,b,c){return a.call.apply(a.bind,arguments)};zb=function(a,b,c){if(!a)throw Error();if(arguments.length>2){var d=Array.prototype.slice.call(arguments,2);return function(){var e=Array.prototype.slice.call(arguments);Array.prototype.unshift.apply(e,d);return a.apply(b,e)}}return function(){return a.apply(b,arguments)}};_.Ab=function(a,b,c){_.Ab=Function.prototype.bind&&Function.prototype.bind.toString().indexOf("native code")!=-1?yb:zb;return _.Ab.apply(null,arguments)};_.ob=Array.prototype.indexOf?function(a,b){return Array.prototype.indexOf.call(a,b,void 0)}:function(a,b){if(typeof a==="string")return typeof b!=="string"||b.length!=1?-1:a.indexOf(b,0);for(var c=0;c<a.length;c++)if(c in a&&a[c]===b)return c;return-1};
_.Bb=Array.prototype.lastIndexOf?function(a,b){return Array.prototype.lastIndexOf.call(a,b,a.length-1)}:function(a,b){var c=a.length-1;c<0&&(c=Math.max(0,a.length+c));if(typeof a==="string")return typeof b!=="string"||b.length!=1?-1:a.lastIndexOf(b,c);for(;c>=0;c--)if(c in a&&a[c]===b)return c;return-1};_.Cb=Array.prototype.forEach?function(a,b,c){Array.prototype.forEach.call(a,b,c)}:function(a,b,c){for(var d=a.length,e=typeof a==="string"?a.split(""):a,f=0;f<d;f++)f in e&&b.call(c,e[f],f,a)};
_.Db=Array.prototype.filter?function(a,b){return Array.prototype.filter.call(a,b,void 0)}:function(a,b){for(var c=a.length,d=[],e=0,f=typeof a==="string"?a.split(""):a,h=0;h<c;h++)if(h in f){var k=f[h];b.call(void 0,k,h,a)&&(d[e++]=k)}return d};_.Fb=Array.prototype.map?function(a,b,c){return Array.prototype.map.call(a,b,c)}:function(a,b,c){for(var d=a.length,e=Array(d),f=typeof a==="string"?a.split(""):a,h=0;h<d;h++)h in f&&(e[h]=b.call(c,f[h],h,a));return e};
_.Hb=Array.prototype.some?function(a,b,c){return Array.prototype.some.call(a,b,c)}:function(a,b,c){for(var d=a.length,e=typeof a==="string"?a.split(""):a,f=0;f<d;f++)if(f in e&&b.call(c,e[f],f,a))return!0;return!1};_.Jb=Array.prototype.every?function(a,b,c){return Array.prototype.every.call(a,b,c)}:function(a,b,c){for(var d=a.length,e=typeof a==="string"?a.split(""):a,f=0;f<d;f++)if(f in e&&!b.call(c,e[f],f,a))return!1;return!0};var Nb=!!(_.Ya[0]>>15&1),Ob=!!(_.Ya[0]>>16&1),Pb=!!(_.Ya[0]&32),Qb=!!(_.Ya[0]>>17&1),Rb=!!(_.Ya[0]&16);_.Sb=Nb?Ob:wb(610401301);_.Tb=Nb?Pb:wb(**********);_.Vb=Nb?Qb:wb(651175828);_.Wb=Nb?Rb:wb(555019702);_.Xb=function(a){_.Xb[" "](a);return a};_.Xb[" "]=function(){};
/*

 Copyright Google LLC
 SPDX-License-Identifier: Apache-2.0
*/
var ac,cc,sc,Fc,Nc,cd,md;_.Yb=function(a){var b=a.length;if(b>0){for(var c=Array(b),d=0;d<b;d++)c[d]=a[d];return c}return[]};_.Zb=function(a,b,c){for(var d in a)b.call(c,a[d],d,a)};ac=function(){var a=null;if(!$b)return a;try{var b=function(c){return c};a=$b.createPolicy("goog#html",{createHTML:b,createScript:b,createScriptURL:b})}catch(c){}return a};cc=function(){bc===void 0&&(bc=ac());return bc};_.ec=function(a){var b=cc();a=b?b.createHTML(a):a;return new _.dc(a)};
_.fc=function(a){if(a instanceof _.dc)return a.rZ;throw Error("j");};_.ic=function(a){return new _.hc(a)};_.kc=function(a){var b=cc();a=b?b.createScriptURL(a):a;return new _.jc(a)};_.lc=function(a){if(a instanceof _.jc)return a.sZ;throw Error("j");};_.oc=function(a){return a instanceof _.mc};_.qc=function(a){if(_.oc(a))return a.uZ;throw Error("j");};sc=function(a){return new _.rc(function(b){return b.substr(0,a.length+1).toLowerCase()===a+":"})};_.uc=function(a){if(tc.test(a))return a};
_.wc=function(a){return a instanceof _.mc?_.qc(a):_.uc(a)};_.xc=function(a,b){if(a instanceof _.dc)return a;a=String(a).replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;");if(b==null?0:b.Fsa)a=a.replace(/(^|[\r\n\t ]) /g,"$1&#160;");if(b==null?0:b.Vea)a=a.replace(/(\r\n|\n|\r)/g,"<br>");if(b==null?0:b.Gsa)a=a.replace(/(\t+)/g,'<span style="white-space:pre">$1</span>');return _.ec(a)};
_.zc=function(a){var b=_.yc.apply(1,arguments);if(b.length===0)return _.kc(a[0]);for(var c=a[0],d=0;d<b.length;d++)c+=encodeURIComponent(b[d])+a[d+1];return _.kc(c)};_.Bc=function(a,b){return a.lastIndexOf(b,0)==0};_.Cc=function(a){return/^[\s\xa0]*$/.test(a)};_.Dc=function(a,b){return a.indexOf(b)!=-1};
_.Gc=function(a,b){var c=0;a=(0,_.Ec)(String(a)).split(".");b=(0,_.Ec)(String(b)).split(".");for(var d=Math.max(a.length,b.length),e=0;c==0&&e<d;e++){var f=a[e]||"",h=b[e]||"";do{f=/(\d*)(\D*)(.*)/.exec(f)||["","","",""];h=/(\d*)(\D*)(.*)/.exec(h)||["","","",""];if(f[0].length==0&&h[0].length==0)break;c=Fc(f[1].length==0?0:parseInt(f[1],10),h[1].length==0?0:parseInt(h[1],10))||Fc(f[2].length==0,h[2].length==0)||Fc(f[2],h[2]);f=f[3];h=h[3]}while(c==0)}return c};
Fc=function(a,b){return a<b?-1:a>b?1:0};_.Hc=function(a,b){b=_.wc(b);b!==void 0&&(a.href=b)};_.Ic=function(a,b,c,d){b=_.wc(b);return b!==void 0?a.open(b,c,d):null};_.Jc=function(a,b){b=b===void 0?document:b;var c,d;b=(d=(c=b).querySelector)==null?void 0:d.call(c,a+"[nonce]");return b==null?"":b.nonce||b.getAttribute("nonce")||""};_.Kc=function(a,b){if(a.nodeType===1&&/^(script|style)$/i.test(a.tagName))throw Error("j");a.innerHTML=_.fc(b)};
_.Lc=function(){var a=_.Xa.navigator;return a&&(a=a.userAgent)?a:""};Nc=function(a){if(!_.Sb||!_.Mc)return!1;for(var b=0;b<_.Mc.brands.length;b++){var c=_.Mc.brands[b].brand;if(c&&_.Dc(c,a))return!0}return!1};_.Oc=function(a){return _.Dc(_.Lc(),a)};_.Pc=function(a){for(var b=RegExp("([A-Z][\\w ]+)/([^\\s]+)\\s*(?:\\((.*?)\\))?","g"),c=[],d;d=b.exec(a);)c.push([d[1],d[2],d[3]||void 0]);return c};_.Qc=function(){return _.Sb?!!_.Mc&&_.Mc.brands.length>0:!1};_.Rc=function(){return _.Qc()?!1:_.Oc("Opera")};
_.Tc=function(){return _.Qc()?!1:_.Oc("Trident")||_.Oc("MSIE")};_.Uc=function(){return _.Qc()?!1:_.Oc("Edge")};_.Vc=function(){return _.Qc()?Nc("Microsoft Edge"):_.Oc("Edg/")};_.Wc=function(){return _.Qc()?Nc("Opera"):_.Oc("OPR")};_.Xc=function(){return _.Oc("Firefox")||_.Oc("FxiOS")};_.Yc=function(){return _.Qc()?Nc("Chromium"):(_.Oc("Chrome")||_.Oc("CriOS"))&&!_.Uc()||_.Oc("Silk")};
_.Zc=function(a){var b={};a.forEach(function(c){b[c[0]]=c[1]});return function(c){return b[c.find(function(d){return d in b})]||""}};_.$c=function(a){var b=/rv: *([\d\.]*)/.exec(a);if(b&&b[1])return b[1];b="";var c=/MSIE +([\d\.]+)/.exec(a);if(c&&c[1])if(a=/Trident\/(\d.\d)/.exec(a),c[1]=="7.0")if(a&&a[1])switch(a[1]){case "4.0":b="8.0";break;case "5.0":b="9.0";break;case "6.0":b="10.0";break;case "7.0":b="11.0"}else b="7.0";else b=c[1];return b};
cd=function(){return _.Sb?!!_.Mc&&!!_.Mc.platform:!1};_.dd=function(){return cd()?_.Mc.platform==="Android":_.Oc("Android")};_.ed=function(){return _.Oc("iPhone")&&!_.Oc("iPod")&&!_.Oc("iPad")};_.fd=function(){return _.ed()||_.Oc("iPad")||_.Oc("iPod")};_.gd=function(){return cd()?_.Mc.platform==="macOS":_.Oc("Macintosh")};_.hd=function(){return cd()?_.Mc.platform==="Windows":_.Oc("Windows")};_.id=function(){return cd()?_.Mc.platform==="Chrome OS":_.Oc("CrOS")};
_.kd=function(a,b){a.raw=b;Object.freeze&&(Object.freeze(a),Object.freeze(b));return a};_.ld=function(a){return _.kd(a,a)};_.yc=function(){for(var a=Number(this),b=[],c=a;c<arguments.length;c++)b[c-a]=arguments[c];return b};_.nd=function(a){var b=typeof a;return b!="object"?b:a?Array.isArray(a)?"array":b:"null"};_.od=function(a){var b=_.nd(a);return b=="array"||b=="object"&&typeof a.length=="number"};_.pd=function(){return Date.now()};var qd=globalThis.trustedTypes,$b=qd,bc;_.dc=function(a){this.rZ=a};_.dc.prototype.toString=function(){return this.rZ+""};_.rd=function(){return new _.dc(qd?qd.emptyHTML:"")}();_.hc=function(a){this.tZ=a};_.hc.prototype.toString=function(){return this.tZ};_.jc=function(a){this.sZ=a};_.jc.prototype.toString=function(){return this.sZ+""};_.mc=function(a){this.uZ=a};_.mc.prototype.toString=function(){return this.uZ};_.sd=new _.mc("about:invalid#zClosurez");var tc;_.rc=function(a){this.yj=a};_.td=[sc("data"),sc("http"),sc("https"),sc("mailto"),sc("ftp"),new _.rc(function(a){return/^[^:]*([/?#]|$)/.test(a)})];_.ud=function(){return typeof URL==="function"}();tc=/^\s*(?!javascript:)(?:[\w+.-]+:|[^:/?#]*(?:[/?#]|$))/i;_.vd=function(a,b){this.width=a;this.height=b};_.wd=function(a,b){return a==b?!0:a&&b?a.width==b.width&&a.height==b.height:!1};_.g=_.vd.prototype;_.g.clone=function(){return new _.vd(this.width,this.height)};_.g.area=function(){return this.width*this.height};_.g.aspectRatio=function(){return this.width/this.height};_.g.isEmpty=function(){return!this.area()};_.g.ceil=function(){this.width=Math.ceil(this.width);this.height=Math.ceil(this.height);return this};
_.g.floor=function(){this.width=Math.floor(this.width);this.height=Math.floor(this.height);return this};_.g.round=function(){this.width=Math.round(this.width);this.height=Math.round(this.height);return this};_.g.scale=function(a,b){this.width*=a;this.height*=typeof b==="number"?b:a;return this};_.Ec=String.prototype.trim?function(a){return a.trim()}:function(a){return/^[\s\xa0]*([\s\S]*?)[\s\xa0]*$/.exec(a)[1]};_.xd=String.prototype.repeat?function(a,b){return a.repeat(b)}:function(a,b){return Array(b+1).join(a)};_.yd=Math.random()*2147483648|0;var zd;zd=_.Xa.navigator;_.Mc=zd?zd.userAgentData||null:null;var Pd,Qd,Vd;_.Ad=_.Rc();_.Bd=_.Tc();_.Cd=_.Oc("Edge");_.Dd=_.Cd||_.Bd;_.Ed=_.Oc("Gecko")&&!(_.Dc(_.Lc().toLowerCase(),"webkit")&&!_.Oc("Edge"))&&!(_.Oc("Trident")||_.Oc("MSIE"))&&!_.Oc("Edge");_.Fd=_.Dc(_.Lc().toLowerCase(),"webkit")&&!_.Oc("Edge");_.Gd=_.Fd&&_.Oc("Mobile");_.Hd=_.gd();_.Id=_.hd();_.Jd=(cd()?_.Mc.platform==="Linux":_.Oc("Linux"))||_.id();_.Kd=_.dd();_.Ld=_.ed();_.Md=_.Oc("iPad");_.Nd=_.Oc("iPod");_.Od=_.fd();Pd=function(){var a=_.Xa.document;return a?a.documentMode:void 0};
a:{var Rd="",Sd=function(){var a=_.Lc();if(_.Ed)return/rv:([^\);]+)(\)|;)/.exec(a);if(_.Cd)return/Edge\/([\d\.]+)/.exec(a);if(_.Bd)return/\b(?:MSIE|rv)[: ]([^\);]+)(\)|;)/.exec(a);if(_.Fd)return/WebKit\/(\S+)/.exec(a);if(_.Ad)return/(?:Version)[ \/]?(\S+)/.exec(a)}();Sd&&(Rd=Sd?Sd[1]:"");if(_.Bd){var Td=Pd();if(Td!=null&&Td>parseFloat(Rd)){Qd=String(Td);break a}}Qd=Rd}_.Ud=Qd;if(_.Xa.document&&_.Bd){var Wd=Pd();Vd=Wd?Wd:parseInt(_.Ud,10)||void 0}else Vd=void 0;_.Xd=Vd;var ce,je,ie;_.$d=function(a){return a?new _.Yd(_.Zd(a)):md||(md=new _.Yd)};_.ae=function(a,b){return typeof b==="string"?a.getElementById(b):b};_.be=function(a,b,c,d){a=d||a;return(b=b&&b!="*"?String(b).toUpperCase():"")||c?a.querySelectorAll(b+(c?"."+c:"")):a.getElementsByTagName("*")};
_.de=function(a,b){_.Zb(b,function(c,d){d=="style"?a.style.cssText=c:d=="class"?a.className=c:d=="for"?a.htmlFor=c:ce.hasOwnProperty(d)?a.setAttribute(ce[d],c):_.Bc(d,"aria-")||_.Bc(d,"data-")?a.setAttribute(d,c):a[d]=c})};ce={cellpadding:"cellPadding",cellspacing:"cellSpacing",colspan:"colSpan",frameborder:"frameBorder",height:"height",maxlength:"maxLength",nonce:"nonce",role:"role",rowspan:"rowSpan",type:"type",usemap:"useMap",valign:"vAlign",width:"width"};_.fe=function(a){return _.ee(a||window)};
_.ee=function(a){a=a.document;a=_.ge(a)?a.documentElement:a.body;return new _.vd(a.clientWidth,a.clientHeight)};_.he=function(a){return a?a.defaultView:window};_.ke=function(a,b){var c=b[1],d=ie(a,String(b[0]));c&&(typeof c==="string"?d.className=c:Array.isArray(c)?d.className=c.join(" "):_.de(d,c));b.length>2&&je(a,d,b,2);return d};
je=function(a,b,c,d){function e(k){k&&b.appendChild(typeof k==="string"?a.createTextNode(k):k)}for(;d<c.length;d++){var f=c[d];if(!_.od(f)||_.ub(f)&&f.nodeType>0)e(f);else{a:{if(f&&typeof f.length=="number"){if(_.ub(f)){var h=typeof f.item=="function"||typeof f.item=="string";break a}if(typeof f==="function"){h=typeof f.item=="function";break a}}h=!1}_.Cb(h?_.Yb(f):f,e)}}};_.ne=function(a){return ie(document,a)};
ie=function(a,b){b=String(b);a.contentType==="application/xhtml+xml"&&(b=b.toLowerCase());return a.createElement(b)};_.ge=function(a){return a.compatMode=="CSS1Compat"};_.oe=function(a){if(a.nodeType!=1)return!1;switch(a.tagName){case "APPLET":case "AREA":case "BASE":case "BR":case "COL":case "COMMAND":case "EMBED":case "FRAME":case "HR":case "IMG":case "INPUT":case "IFRAME":case "ISINDEX":case "KEYGEN":case "LINK":case "NOFRAMES":case "NOSCRIPT":case "META":case "OBJECT":case "PARAM":case "SCRIPT":case "SOURCE":case "STYLE":case "TRACK":case "WBR":return!1}return!0};
_.pe=function(a,b){je(_.Zd(a),a,arguments,1)};_.qe=function(a){for(var b;b=a.firstChild;)a.removeChild(b)};_.re=function(a,b){b.parentNode&&b.parentNode.insertBefore(a,b)};_.se=function(a){return a&&a.parentNode?a.parentNode.removeChild(a):null};_.te=function(a){return a.children!=void 0?a.children:Array.prototype.filter.call(a.childNodes,function(b){return b.nodeType==1})};_.ue=function(a){return _.ub(a)&&a.nodeType==1};
_.ve=function(a,b){if(!a||!b)return!1;if(a.contains&&b.nodeType==1)return a==b||a.contains(b);if(typeof a.compareDocumentPosition!="undefined")return a==b||!!(a.compareDocumentPosition(b)&16);for(;b&&a!=b;)b=b.parentNode;return b==a};_.Zd=function(a){return a.nodeType==9?a:a.ownerDocument||a.document};
_.we=function(a,b){if("textContent"in a)a.textContent=b;else if(a.nodeType==3)a.data=String(b);else if(a.firstChild&&a.firstChild.nodeType==3){for(;a.lastChild!=a.firstChild;)a.removeChild(a.lastChild);a.firstChild.data=String(b)}else _.qe(a),a.appendChild(_.Zd(a).createTextNode(String(b)))};_.Yd=function(a){this.zc=a||_.Xa.document||document};_.g=_.Yd.prototype;_.g.Ha=_.$d;_.g.FL=_.ib(0);_.g.ub=function(){return this.zc};_.g.O=_.ib(1);_.g.getElementsByTagName=function(a,b){return(b||this.zc).getElementsByTagName(String(a))};
_.g.EH=_.ib(2);_.g.wa=function(a,b,c){return _.ke(this.zc,arguments)};_.g.createElement=function(a){return ie(this.zc,a)};_.g.createTextNode=function(a){return this.zc.createTextNode(String(a))};_.g.getWindow=function(){return this.zc.defaultView};_.g.appendChild=function(a,b){a.appendChild(b)};_.g.append=_.pe;_.g.canHaveChildren=_.oe;_.g.ne=_.qe;_.g.ZV=_.re;_.g.removeNode=_.se;_.g.OG=_.te;_.g.isElement=_.ue;_.g.contains=_.ve;_.g.hH=_.Zd;_.g.wj=_.ib(3);
/*
 gapi.loader.OBJECT_CREATE_TEST_OVERRIDE &&*/
_.xe=function(a){return a===null?"null":a===void 0?"undefined":a};_.ye=window;_.Be=document;_.Ce=_.ye.location;_.De=/\[native code\]/;_.Ee=function(a,b,c){return a[b]=a[b]||c};_.Fe=function(){var a;if((a=Object.create)&&_.De.test(a))a=a(null);else{a={};for(var b in a)a[b]=void 0}return a};_.Ge=function(a,b){return Object.prototype.hasOwnProperty.call(a,b)};_.He=function(a,b){a=a||{};for(var c in a)_.Ge(a,c)&&(b[c]=a[c])};_.Ie=_.Ee(_.ye,"gapi",{});_.Je=function(a,b,c){var d=new RegExp("([#].*&|[#])"+b+"=([^&#]*)","g");b=new RegExp("([?#].*&|[?#])"+b+"=([^&#]*)","g");if(a=a&&(d.exec(a)||b.exec(a)))try{c=decodeURIComponent(a[2])}catch(e){}return c};_.Ke=new RegExp(/^/.source+/([a-zA-Z][-+.a-zA-Z0-9]*:)?/.source+/(\/\/[^\/?#]*)?/.source+/([^?#]*)?/.source+/(\?([^#]*))?/.source+/(#((#|[^#])*))?/.source+/$/.source);_.Le=new RegExp(/(%([^0-9a-fA-F%]|[0-9a-fA-F]([^0-9a-fA-F%])?)?)*/.source+/%($|[^0-9a-fA-F]|[0-9a-fA-F]($|[^0-9a-fA-F]))/.source,"g");
_.Me=new RegExp(/\/?\??#?/.source+"("+/[\/?#]/i.source+"|"+/[\uD800-\uDBFF]/i.source+"|"+/%[c-f][0-9a-f](%[89ab][0-9a-f]){0,2}(%[89ab]?)?/i.source+"|"+/%[0-9a-f]?/i.source+")$","i");_.Oe=function(a,b,c){_.Ne(a,b,c,"add","at")};_.Ne=function(a,b,c,d,e){if(a[d+"EventListener"])a[d+"EventListener"](b,c,!1);else if(a[e+"tachEvent"])a[e+"tachEvent"]("on"+b,c)};_.Pe={};_.Pe=_.Ee(_.ye,"___jsl",_.Fe());_.Ee(_.Pe,"I",0);_.Ee(_.Pe,"hel",10);var Qe,Re,Se,Te,We,Ue,Ve,Xe,Ye;Qe=function(a){var b=window.___jsl=window.___jsl||{};b[a]=b[a]||[];return b[a]};Re=function(a){var b=window.___jsl=window.___jsl||{};b.cfg=!a&&b.cfg||{};return b.cfg};Se=function(a){return typeof a==="object"&&/\[native code\]/.test(a.push)};
Te=function(a,b,c){if(b&&typeof b==="object")for(var d in b)!Object.prototype.hasOwnProperty.call(b,d)||c&&d==="___goc"&&typeof b[d]==="undefined"||(a[d]&&b[d]&&typeof a[d]==="object"&&typeof b[d]==="object"&&!Se(a[d])&&!Se(b[d])?Te(a[d],b[d]):b[d]&&typeof b[d]==="object"?(a[d]=Se(b[d])?[]:{},Te(a[d],b[d])):a[d]=b[d])};
We=function(a,b){if(a&&!/^\s+$/.test(a)){for(;a.charCodeAt(a.length-1)==0;)a=a.substring(0,a.length-1);var c=a,d=Qe("dm");d.push(20);try{var e=window.JSON.parse(a)}catch(h){}if(typeof e==="object")return d.push(21),e;try{e=window.JSON.parse("{"+a+"}")}catch(h){}if(typeof e==="object")return d.push(22),e;a=a.replace(RegExp("([^\"',{}\\s]+?)\\s*:\\s*(.*?)[,}\\s]","g"),function(h,k,l){l=l.startsWith('"')?"%DOUBLE_QUOTE%"+l.substring(1):l;l=l.endsWith('"')?l.slice(0,-1)+"%DOUBLE_QUOTE%":l;return"%DOUBLE_QUOTE%"+
k+"%DOUBLE_QUOTE%:"+l});a=a.replace(/\\'/g,"%SINGLE_QUOTE%");a=a.replace(/"/g,'\\"');a=a.replace(/'/g,'"');a=a.replace(/%SINGLE_QUOTE%/g,"'");a=a.replace(/%DOUBLE_QUOTE%/g,'"');try{e=window.JSON.parse(a)}catch(h){}if(typeof e==="object")return d.push(23),e;try{e=window.JSON.parse("{"+a+"}")}catch(h){}if(typeof e==="object")return d.push(24),e;a=document.getElementsByTagName("script")||[];var f;a.length>0&&(f=a[0].nonce||a[0].getAttribute("nonce"));if(f&&f===b||!f&&Ue())if(e=Ve(c),d.push(25),typeof e===
"object")return e;return{}}};Ue=function(){var a=window.location.hostname;return a?/(^|\.)(2mdn|ampproject|android|appspot|blogger|blogspot|chrome|chromium|doubleclick|gcpnode|ggpht|gmail|google|google-analytics|googleadservices|googleapis|googleapis-cn|googleoptimize|googlers|googlesource|googlesyndication|googletagmanager|googletagservices|googleusercontent|googlevideo|gstatic|tiltbrush|waze|withgoogle|youtube|ytimg)(\.com?|\.net|\.org)?(\.[a-z][a-z]|\.cat)?$/.test(a):!1};
Ve=function(a){try{var b=(new Function("return ("+a+"\n)"))()}catch(c){}if(typeof b==="object")return b;try{b=(new Function("return ({"+a+"\n})"))()}catch(c){}return b};Xe=function(a,b){var c={___goc:void 0};a.length&&a[a.length-1]&&Object.hasOwnProperty.call(a[a.length-1],"___goc")&&typeof a[a.length-1].___goc==="undefined"&&(c=a.pop());Te(c,b);a.push(c)};
Ye=function(a){Re(!0);var b=window.___gcfg,c=Qe("cu"),d=window.___gu;b&&b!==d&&(Xe(c,b),window.___gu=b);b=Qe("cu");var e=document.getElementsByTagName("script")||[];d=[];var f=[];f.push.apply(f,Qe("us"));for(var h=0;h<e.length;++h)for(var k=e[h],l=0;l<f.length;++l)k.src&&k.src.indexOf(f[l])==0&&d.push(k);d.length==0&&e.length>0&&e[e.length-1].src&&d.push(e[e.length-1]);for(e=0;e<d.length;++e)d[e].getAttribute("gapi_processed")||(d[e].setAttribute("gapi_processed",!0),(f=d[e])?(h=f.nodeType,f=h==3||
h==4?f.nodeValue:f.textContent||""):f=void 0,h=d[e].nonce||d[e].getAttribute("nonce"),(f=We(f,h))&&b.push(f));a&&Xe(c,a);d=Qe("cd");a=0;for(b=d.length;a<b;++a)Te(Re(),d[a],!0);d=Qe("ci");a=0;for(b=d.length;a<b;++a)Te(Re(),d[a],!0);a=0;for(b=c.length;a<b;++a)Te(Re(),c[a],!0)};_.Ze=function(a,b){var c=Re();if(!a)return c;a=a.split("/");for(var d=0,e=a.length;c&&typeof c==="object"&&d<e;++d)c=c[a[d]];return d===a.length&&c!==void 0?c:b};
_.$e=function(a,b){var c;if(typeof a==="string"){var d=c={};a=a.split("/");for(var e=0,f=a.length;e<f-1;++e){var h={};d=d[a[e]]=h}d[a[e]]=b}else c=a;Ye(c)};var af=function(){var a=window.__GOOGLEAPIS;a&&(a.googleapis&&!a["googleapis.config"]&&(a["googleapis.config"]=a.googleapis),_.Ee(_.Pe,"ci",[]).push(a),window.__GOOGLEAPIS=void 0)};af&&af();Ye();_.r("gapi.config.get",_.Ze);_.r("gapi.config.update",_.$e);
_.bf=function(a){a=_.xe(a);return _.ec(a)};
var hf,jf,kf,lf,mf,of,qf,rf,sf,tf,uf,vf,wf,xf,yf,zf,Af,Bf,Cf,Df,Ef,Ff,Gf,Hf,If,Jf,Lf,Mf,Nf,Of,Pf,Sf,Tf;kf=void 0;lf=function(a){try{return _.Xa.JSON.parse.call(_.Xa.JSON,a)}catch(b){return!1}};mf=function(a){return Object.prototype.toString.call(a)};of=mf(0);qf=mf(new Date(0));rf=mf(!0);sf=mf("");tf=mf({});uf=mf([]);
vf=function(a,b){if(b)for(var c=0,d=b.length;c<d;++c)if(a===b[c])throw new TypeError("Converting circular structure to JSON");d=typeof a;if(d!=="undefined"){c=Array.prototype.slice.call(b||[],0);c[c.length]=a;b=[];var e=mf(a);if(a!=null&&typeof a.toJSON==="function"&&(Object.prototype.hasOwnProperty.call(a,"toJSON")||(e!==uf||a.constructor!==Array&&a.constructor!==Object)&&(e!==tf||a.constructor!==Array&&a.constructor!==Object)&&e!==sf&&e!==of&&e!==rf&&e!==qf))return vf(a.toJSON.call(a),c);if(a==
null)b[b.length]="null";else if(e===of)a=Number(a),isNaN(a)||isNaN(a-a)?a="null":a===-0&&1/a<0&&(a="-0"),b[b.length]=String(a);else if(e===rf)b[b.length]=String(!!Number(a));else{if(e===qf)return vf(a.toISOString.call(a),c);if(e===uf&&mf(a.length)===of){b[b.length]="[";var f=0;for(d=Number(a.length)>>0;f<d;++f)f&&(b[b.length]=","),b[b.length]=vf(a[f],c)||"null";b[b.length]="]"}else if(e==sf&&mf(a.length)===of){b[b.length]='"';f=0;for(c=Number(a.length)>>0;f<c;++f)d=String.prototype.charAt.call(a,
f),e=String.prototype.charCodeAt.call(a,f),b[b.length]=d==="\b"?"\\b":d==="\f"?"\\f":d==="\n"?"\\n":d==="\r"?"\\r":d==="\t"?"\\t":d==="\\"||d==='"'?"\\"+d:e<=31?"\\u"+(e+65536).toString(16).substr(1):e>=32&&e<=65535?d:"\ufffd";b[b.length]='"'}else if(d==="object"){b[b.length]="{";d=0;for(f in a)Object.prototype.hasOwnProperty.call(a,f)&&(e=vf(a[f],c),e!==void 0&&(d++&&(b[b.length]=","),b[b.length]=vf(f),b[b.length]=":",b[b.length]=e));b[b.length]="}"}else return}return b.join("")}};wf=/[\0-\x07\x0b\x0e-\x1f]/;
xf=/^([^"]*"([^\\"]|\\.)*")*[^"]*"([^"\\]|\\.)*[\0-\x1f]/;yf=/^([^"]*"([^\\"]|\\.)*")*[^"]*"([^"\\]|\\.)*\\[^\\\/"bfnrtu]/;zf=/^([^"]*"([^\\"]|\\.)*")*[^"]*"([^"\\]|\\.)*\\u([0-9a-fA-F]{0,3}[^0-9a-fA-F])/;Af=/"([^\0-\x1f\\"]|\\[\\\/"bfnrt]|\\u[0-9a-fA-F]{4})*"/g;Bf=/-?(0|[1-9][0-9]*)(\.[0-9]+)?([eE][-+]?[0-9]+)?/g;Cf=/[ \t\n\r]+/g;Df=/[^"]:/;Ef=/""/g;Ff=/true|false|null/g;Gf=/00/;Hf=/[\{]([^0\}]|0[^:])/;If=/(^|\[)[,:]|[,:](\]|\}|[,:]|$)/;Jf=/[^\[,:][\[\{]/;Lf=/^(\{|\}|\[|\]|,|:|0)+/;Mf=/\u2028/g;
Nf=/\u2029/g;
Of=function(a){a=String(a);if(wf.test(a)||xf.test(a)||yf.test(a)||zf.test(a))return!1;var b=a.replace(Af,'""');b=b.replace(Bf,"0");b=b.replace(Cf,"");if(Df.test(b))return!1;b=b.replace(Ef,"0");b=b.replace(Ff,"0");if(Gf.test(b)||Hf.test(b)||If.test(b)||Jf.test(b)||!b||(b=b.replace(Lf,"")))return!1;a=a.replace(Mf,"\\u2028").replace(Nf,"\\u2029");b=void 0;try{b=kf?[lf(a)]:eval("(function (var_args) {\n  return Array.prototype.slice.call(arguments, 0);\n})(\n"+a+"\n)")}catch(c){return!1}return b&&b.length===
1?b[0]:!1};Pf=function(){var a=((_.Xa.document||{}).scripts||[]).length;if((hf===void 0||kf===void 0||jf!==a)&&jf!==-1){hf=kf=!1;jf=-1;try{try{kf=!!_.Xa.JSON&&_.Xa.JSON.stringify.call(_.Xa.JSON,{a:[3,!0,new Date(0)],c:function(){}})==='{"a":[3,true,"1970-01-01T00:00:00.000Z"]}'&&lf("true")===!0&&lf('[{"a":3}]')[0].a===3}catch(b){}hf=kf&&!lf("[00]")&&!lf('"\u0007"')&&!lf('"\\0"')&&!lf('"\\v"')}finally{jf=a}}};_.Qf=function(a){if(jf===-1)return!1;Pf();return(hf?lf:Of)(a)};
_.Rf=function(a){if(jf!==-1)return Pf(),kf?_.Xa.JSON.stringify.call(_.Xa.JSON,a):vf(a)};Sf=!Date.prototype.toISOString||typeof Date.prototype.toISOString!=="function"||(new Date(0)).toISOString()!=="1970-01-01T00:00:00.000Z";
Tf=function(){var a=Date.prototype.getUTCFullYear.call(this);return[a<0?"-"+String(1E6-a).substr(1):a<=9999?String(1E4+a).substr(1):"+"+String(1E6+a).substr(1),"-",String(101+Date.prototype.getUTCMonth.call(this)).substr(1),"-",String(100+Date.prototype.getUTCDate.call(this)).substr(1),"T",String(100+Date.prototype.getUTCHours.call(this)).substr(1),":",String(100+Date.prototype.getUTCMinutes.call(this)).substr(1),":",String(100+Date.prototype.getUTCSeconds.call(this)).substr(1),".",String(1E3+Date.prototype.getUTCMilliseconds.call(this)).substr(1),
"Z"].join("")};Date.prototype.toISOString=Sf?Tf:Date.prototype.toISOString;
var Jg=function(){this.blockSize=-1},Kg=function(){this.blockSize=-1;this.blockSize=64;this.Qc=[];this.mF=[];this.j7=[];this.ZB=[];this.ZB[0]=128;for(var a=1;a<this.blockSize;++a)this.ZB[a]=0;this.PD=this.jr=0;this.reset()};_.cb(Kg,Jg);Kg.prototype.reset=function(){this.Qc[0]=1732584193;this.Qc[1]=4023233417;this.Qc[2]=2562383102;this.Qc[3]=271733878;this.Qc[4]=3285377520;this.PD=this.jr=0};
var Lg=function(a,b,c){c||(c=0);var d=a.j7;if(typeof b==="string")for(var e=0;e<16;e++)d[e]=b.charCodeAt(c)<<24|b.charCodeAt(c+1)<<16|b.charCodeAt(c+2)<<8|b.charCodeAt(c+3),c+=4;else for(e=0;e<16;e++)d[e]=b[c]<<24|b[c+1]<<16|b[c+2]<<8|b[c+3],c+=4;for(b=16;b<80;b++)c=d[b-3]^d[b-8]^d[b-14]^d[b-16],d[b]=(c<<1|c>>>31)&4294967295;b=a.Qc[0];c=a.Qc[1];e=a.Qc[2];for(var f=a.Qc[3],h=a.Qc[4],k,l,m=0;m<80;m++)m<40?m<20?(k=f^c&(e^f),l=1518500249):(k=c^e^f,l=1859775393):m<60?(k=c&e|f&(c|e),l=2400959708):(k=c^
e^f,l=3395469782),k=(b<<5|b>>>27)+k+h+l+d[m]&4294967295,h=f,f=e,e=(c<<30|c>>>2)&4294967295,c=b,b=k;a.Qc[0]=a.Qc[0]+b&4294967295;a.Qc[1]=a.Qc[1]+c&4294967295;a.Qc[2]=a.Qc[2]+e&4294967295;a.Qc[3]=a.Qc[3]+f&4294967295;a.Qc[4]=a.Qc[4]+h&4294967295};
Kg.prototype.update=function(a,b){if(a!=null){b===void 0&&(b=a.length);for(var c=b-this.blockSize,d=0,e=this.mF,f=this.jr;d<b;){if(f==0)for(;d<=c;)Lg(this,a,d),d+=this.blockSize;if(typeof a==="string")for(;d<b;){if(e[f]=a.charCodeAt(d),++f,++d,f==this.blockSize){Lg(this,e);f=0;break}}else for(;d<b;)if(e[f]=a[d],++f,++d,f==this.blockSize){Lg(this,e);f=0;break}}this.jr=f;this.PD+=b}};
Kg.prototype.digest=function(){var a=[],b=this.PD*8;this.jr<56?this.update(this.ZB,56-this.jr):this.update(this.ZB,this.blockSize-(this.jr-56));for(var c=this.blockSize-1;c>=56;c--)this.mF[c]=b&255,b/=256;Lg(this,this.mF);for(c=b=0;c<5;c++)for(var d=24;d>=0;d-=8)a[b]=this.Qc[c]>>d&255,++b;return a};_.Mg=function(){this.mN=new Kg};_.g=_.Mg.prototype;_.g.reset=function(){this.mN.reset()};_.g.o2=function(a){this.mN.update(a)};_.g.yR=function(){return this.mN.digest()};_.g.Bx=function(a){a=unescape(encodeURIComponent(a));for(var b=[],c=a.length,d=0;d<c;++d)b.push(a.charCodeAt(d));this.o2(b)};_.g.Ti=function(){for(var a=this.yR(),b="",c=0;c<a.length;c++)b+="0123456789ABCDEF".charAt(Math.floor(a[c]/16))+"0123456789ABCDEF".charAt(a[c]%16);return b};
_.$h=function(a){var b=window.___jsl=window.___jsl||{};b.cfg=!a&&b.cfg||{};return b.cfg};_.ai=function(a){var b=_.$h();if(!a)return b;a=a.split("/");for(var c=0,d=a.length;b&&typeof b==="object"&&c<d;++c)b=b[a[c]];return c===a.length&&b!==void 0?b:void 0};
_.bi=function(a,b,c,d){for(var e=0,f=a.length,h;e<f;){var k=e+(f-e>>>1);var l=c?b.call(void 0,a[k],k,a):b(d,a[k]);l>0?e=k+1:(f=k,h=!l)}return h?e:-e-1};_.ci=function(a,b){var c={},d;for(d in a)b.call(void 0,a[d],d,a)&&(c[d]=a[d]);return c};var di;di=/^https?:\/\/(?:\w|[\-\.])+\.google\.(?:\w|[\-:\.])+(?:\/[^\?#]*)?\/u\/(\d)\//;
_.ei=function(a){var b=_.ai("googleapis.config/sessionIndex");"string"===typeof b&&b.length>254&&(b=null);b==null&&(b=window.__X_GOOG_AUTHUSER);"string"===typeof b&&b.length>254&&(b=null);if(b==null){var c=window.google;c&&(b=c.authuser)}"string"===typeof b&&b.length>254&&(b=null);b==null&&(a=a||window.location.href,b=_.Je(a,"authuser")||null,b==null&&(b=(b=a.match(di))?b[1]:null));if(b==null)return null;b=String(b);b.length>254&&(b=null);return b};
_.ri=function(){if(!_.Xa.addEventListener||!Object.defineProperty)return!1;var a=!1,b=Object.defineProperty({},"passive",{get:function(){a=!0}});try{var c=function(){};_.Xa.addEventListener("test",c,b);_.Xa.removeEventListener("test",c,b)}catch(d){}return a}();
var si=function(){var a=_.Pe.ms||_.Pe.u;if(a)return(new URL(a)).origin};var ti=function(a){this.mT=a;this.count=this.count=0};ti.prototype.rb=function(a,b){a?this.count+=a:this.count++;this.mT&&(b===void 0||b)&&this.mT()};ti.prototype.get=function(){return this.count};ti.prototype.reset=function(){this.count=0};var vi,yi;vi=function(){var a=!0,b=this;a=a===void 0?!0:a;this.Zy=new Map;this.fF=!1;var c=si();c&&(this.url=c+"/js/gen_204",c=_.ai("gen204logger")||{},this.pu=c.interval,this.nT=c.rate,this.fF=c.uqa,a&&this.url&&ui(this),document.addEventListener("visibilitychange",this.flush),this.flush(),document.addEventListener("visibilitychange",function(){document.visibilityState==="hidden"&&b.flush()}),document.addEventListener("pagehide",this.flush.bind(this)))};_.wi=function(){vi.qX||(vi.qX=new vi);return vi.qX};
yi=function(a){var b=_.Pe.dm||[];if(b&&b.length!==0){b=_.za(b);for(var c=b.next();!c.done;c=b.next())_.xi(a,c.value).rb(1,!1);delete _.Pe.dm;a.flush()}};_.xi=function(a,b){a.Zy.has(b)||a.Zy.set(b,new ti(a.fF?void 0:function(){a.flush()}));return a.Zy.get(b)};
vi.prototype.flush=function(){var a=this;if(this.url&&this.nT){yi(this);for(var b="",c=_.za(this.Zy),d=c.next();!d.done;d=c.next()){var e=_.za(d.value);d=e.next().value;e=e.next().value;var f=e.get();f>0&&(b+=b.length>0?"&":"",b+="c=",b+=encodeURIComponent(d+":"+f),e.reset());if(b.length>1E3)break}if(b!==""&&Math.random()<this.nT){try{var h=AbortSignal.timeout(3E4)}catch(k){h=void 0}fetch(this.url+"?"+b,{method:"GET",mode:"no-cors",signal:h}).catch(function(){}).finally(function(){ui(a)})}}};
vi.prototype.setInterval=function(a){this.pu=a};var ui=function(a){a.pu&&a.fF&&setTimeout(function(){a.flush()},a.pu)};var Ai,zi,Gi,Hi,Bi,Ei,Ci,Ii,Di;_.Fi=function(){_.xi(_.wi(),50).rb();if(zi){var a=new _.ye.Uint32Array(1);Ai.getRandomValues(a);a=Number("0."+a[0])}else a=Bi,a+=parseInt(Ci.substr(0,20),16),Ci=Di(Ci),a/=Ei+1.2089258196146292E24;return a};Ai=_.ye.crypto;zi=!1;Gi=0;Hi=0;Bi=1;Ei=0;Ci="";Ii=function(a){a=a||_.ye.event;var b=a.screenX+a.clientX<<16;b+=a.screenY+a.clientY;b*=(new Date).getTime()%1E6;Bi=Bi*b%Ei;Gi>0&&++Hi==Gi&&_.Ne(_.ye,"mousemove",Ii,"remove","de")};
Di=function(a){var b=new _.Mg;b.Bx(a);return b.Ti()};zi=!!Ai&&typeof Ai.getRandomValues=="function";zi||(Ei=(screen.width*screen.width+screen.height)*1E6,Ci=Di(_.Be.cookie+"|"+_.Be.location+"|"+(new Date).getTime()+"|"+Math.random()),Gi=_.ai("random/maxObserveMousemove")||0,Gi!=0&&_.Oe(_.ye,"mousemove",Ii));
var ul,vl,wl,xl,zl,Al,Bl,Cl,Dl,Il,Jl,Kl,Ol,Pl,Ql,Rl,Sl,Tl,Ul,Vl;_.tl=function(a,b){if(!a)throw Error(b||"");};ul=/&/g;vl=/</g;wl=/>/g;xl=/"/g;zl=/'/g;Al=function(a){return String(a).replace(ul,"&amp;").replace(vl,"&lt;").replace(wl,"&gt;").replace(xl,"&quot;").replace(zl,"&#39;")};Bl=/[\ud800-\udbff][\udc00-\udfff]|[^!-~]/g;Cl=/%([a-f]|[0-9a-fA-F][a-f])/g;Dl=/^(https?|ftp|file|chrome-extension):$/i;
Il=function(a){a=String(a);a=a.replace(Bl,function(e){try{return encodeURIComponent(e)}catch(f){return encodeURIComponent(e.replace(/^[^%]+$/g,"\ufffd"))}}).replace(_.Le,function(e){return e.replace(/%/g,"%25")}).replace(Cl,function(e){return e.toUpperCase()});a=a.match(_.Ke)||[];var b=_.Fe(),c=function(e){return e.replace(/\\/g,"%5C").replace(/\^/g,"%5E").replace(/`/g,"%60").replace(/\{/g,"%7B").replace(/\|/g,"%7C").replace(/\}/g,"%7D")},d=!!(a[1]||"").match(Dl);b.base=c((a[1]||"")+(a[2]||"")+(a[3]||
(a[2]&&d?"/":"")));d=function(e){return c(e.replace(/\?/g,"%3F").replace(/#/g,"%23"))};b.query=a[5]?[d(a[5])]:[];b.Yi=a[7]?[d(a[7])]:[];return b};Jl=function(a){return a.base+(a.query.length>0?"?"+a.query.join("&"):"")+(a.Yi.length>0?"#"+a.Yi.join("&"):"")};Kl=function(a,b){var c=[];if(a)for(var d in a)if(_.Ge(a,d)&&a[d]!=null){var e=b?b(a[d]):a[d];c.push(encodeURIComponent(d)+"="+encodeURIComponent(e))}return c};
_.Ll=function(a,b,c,d){a=Il(a);a.query.push.apply(a.query,Kl(b,d));a.Yi.push.apply(a.Yi,Kl(c,d));return Jl(a)};
_.Ml=function(a,b){var c=Il(b);b=c.base;c.query.length&&(b+="?"+c.query.join(""));c.Yi.length&&(b+="#"+c.Yi.join(""));var d="";b.length>2E3&&(c=b,b=b.substr(0,2E3),b=b.replace(_.Me,""),d=c.substr(b.length));var e=a.createElement("div");a=a.createElement("a");c=Il(b);b=c.base;c.query.length&&(b+="?"+c.query.join(""));c.Yi.length&&(b+="#"+c.Yi.join(""));_.Hc(a,new _.mc(_.xe(b)));e.appendChild(a);_.Kc(e,_.ec(e.innerHTML));b=String(e.firstChild.href);e.parentNode&&e.parentNode.removeChild(e);c=Il(b+d);
b=c.base;c.query.length&&(b+="?"+c.query.join(""));c.Yi.length&&(b+="#"+c.Yi.join(""));return b};_.Nl=/^https?:\/\/[^\/%\\?#\s]+\/[^\s]*$/i;Pl=function(a){for(;a.firstChild;)a.removeChild(a.firstChild)};Ql=/^https?:\/\/(?:\w|[\-\.])+\.google\.(?:\w|[\-:\.])+(?:\/[^\?#]*)?\/b\/(\d{10,21})\//;
Rl=function(){var a=_.ai("googleapis.config/sessionDelegate");"string"===typeof a&&a.length>21&&(a=null);a==null&&(a=(a=window.location.href.match(Ql))?a[1]:null);if(a==null)return null;a=String(a);a.length>21&&(a=null);return a};Sl=function(){var a=_.Pe.onl;if(!a){a=_.Fe();_.Pe.onl=a;var b=_.Fe();a.e=function(c){var d=b[c];d&&(delete b[c],d())};a.a=function(c,d){b[c]=d};a.r=function(c){delete b[c]}}return a};Tl=function(a,b){b=b.onload;return typeof b==="function"?(Sl().a(a,b),b):null};
Ul=function(a){_.tl(/^\w+$/.test(a),"Unsupported id - "+a);return'onload="window.___jsl.onl.e(&#34;'+a+'&#34;)"'};Vl=function(a){Sl().r(a)};var Xl,Yl,bm;_.Wl={allowtransparency:"true",frameborder:"0",hspace:"0",marginheight:"0",marginwidth:"0",scrolling:"no",style:"",tabindex:"0",vspace:"0",width:"100%"};Xl={allowtransparency:!0,onload:!0};Yl=0;_.Zl=function(a,b){var c=0;do var d=b.id||["I",Yl++,"_",(new Date).getTime()].join("");while(a.getElementById(d)&&++c<5);_.tl(c<5,"Error creating iframe id");return d};_.$l=function(a,b){return a?b+"/"+a:""};
_.am=function(a,b,c,d){var e={},f={};a.documentMode&&a.documentMode<9&&(e.hostiemode=a.documentMode);_.He(d.queryParams||{},e);_.He(d.fragmentParams||{},f);var h=d.pfname;var k=_.Fe();_.ai("iframes/dropLegacyIdParam")||(k.id=c);k._gfid=c;k.parent=a.location.protocol+"//"+a.location.host;c=_.Je(a.location.href,"parent");h=h||"";!h&&c&&(h=_.Je(a.location.href,"_gfid","")||_.Je(a.location.href,"id",""),h=_.$l(h,_.Je(a.location.href,"pfname","")));h||(c=_.Qf(_.Je(a.location.href,"jcp","")))&&typeof c==
"object"&&(h=_.$l(c.id,c.pfname));k.pfname=h;d.connectWithJsonParam&&(h={},h.jcp=_.Rf(k),k=h);h=_.Je(b,"rpctoken")||e.rpctoken||f.rpctoken;h||(h=d.rpctoken||String(Math.round(_.Fi()*1E8)),k.rpctoken=h);d.rpctoken=h;_.He(k,d.connectWithQueryParams?e:f);k=a.location.href;a=_.Fe();(h=_.Je(k,"_bsh",_.Pe.bsh))&&(a._bsh=h);(k=_.Pe.dpo?_.Pe.h:_.Je(k,"jsh",_.Pe.h))&&(a.jsh=k);d.hintInFragment?_.He(a,f):_.He(a,e);return _.Ll(b,e,f,d.paramsSerializer)};
bm=function(a){_.tl(!a||_.Nl.test(a),"Illegal url for new iframe - "+a)};
_.cm=function(a,b,c,d,e){bm(c.src);var f,h=Tl(d,c),k=h?Ul(d):"";try{document.all&&(f=a.createElement('<iframe frameborder="'+Al(String(c.frameborder))+'" scrolling="'+Al(String(c.scrolling))+'" '+k+' name="'+Al(String(c.name))+'"/>'))}catch(m){}finally{f||(f=_.$d(a).createElement("IFRAME"),h&&(f.onload=function(){f.onload=null;h.call(this)},Vl(d)))}f.setAttribute("ng-non-bindable","");for(var l in c)a=c[l],l==="style"&&typeof a==="object"?_.He(a,f.style):Xl[l]||f.setAttribute(l,String(a));(l=e&&e.beforeNode||
null)||e&&e.dontclear||Pl(b);b.insertBefore(f,l);f=l?l.previousSibling:b.lastChild;c.allowtransparency&&(f.allowTransparency=!0);return f};var dm,gm;dm=/^:[\w]+$/;_.em=/:([a-zA-Z_]+):/g;_.fm=function(){var a=_.ei()||"0",b=Rl();var c=_.ei()||a;var d=Rl(),e="";c&&(e+="u/"+encodeURIComponent(String(c))+"/");d&&(e+="b/"+encodeURIComponent(String(d))+"/");c=e||null;(e=(d=_.ai("isLoggedIn")===!1)?"_/im/":"")&&(c="");var f=_.ai("iframes/:socialhost:"),h=_.ai("iframes/:im_socialhost:");return Ol={socialhost:f,ctx_socialhost:d?h:f,session_index:a,session_delegate:b,session_prefix:c,im_prefix:e}};gm=function(a,b){return _.fm()[b]||""};
_.hm=function(a){return _.Ml(_.Be,a.replace(_.em,gm))};_.im=function(a){var b=a;dm.test(a)&&(b="iframes/"+b.substring(1)+"/url",b=_.ai(b),_.tl(!!b,"Unknown iframe url config for - "+a));return _.hm(b)};
_.jm=function(a,b,c){c=c||{};var d=c.attributes||{};_.tl(!(c.allowPost||c.forcePost)||!d.onload,"onload is not supported by post iframe (allowPost or forcePost)");a=_.im(a);d=b.ownerDocument||_.Be;var e=_.Zl(d,c);a=_.am(d,a,e,c);var f=c,h=_.Fe();_.He(_.Wl,h);_.He(f.attributes,h);h.name=h.id=e;h.src=a;c.eurl=a;c=(f=c)||{};var k=!!c.allowPost;if(c.forcePost||k&&a.length>2E3){c=Il(a);h.src="";f.dropDataPostorigin||(h["data-postorigin"]=a);a=_.cm(d,b,h,e);if(navigator.userAgent.indexOf("WebKit")!=-1){var l=
a.contentWindow.document;l.open();h=l.createElement("div");k={};var m=e+"_inner";k.name=m;k.src="";k.style="display:none";_.cm(d,h,k,m,f)}h=(f=c.query[0])?f.split("&"):[];f=[];for(k=0;k<h.length;k++)m=h[k].split("=",2),f.push([decodeURIComponent(m[0]),decodeURIComponent(m[1])]);c.query=[];h=Jl(c);_.tl(_.Nl.test(h),"Invalid URL: "+h);c=d.createElement("form");c.method="POST";c.target=e;c.style.display="none";e=_.wc(h);e!==void 0&&(c.action=e);for(e=0;e<f.length;e++)h=d.createElement("input"),h.type=
"hidden",h.name=f[e][0],h.value=f[e][1],c.appendChild(h);b.appendChild(c);c.submit();c.parentNode.removeChild(c);l&&l.close();b=a}else b=_.cm(d,b,h,e,f);return b};
var Uf=function(){this.Bg=window.console};Uf.prototype.log=function(a){this.Bg&&this.Bg.log&&this.Bg.log(a)};Uf.prototype.error=function(a){this.Bg&&(this.Bg.error?this.Bg.error(a):this.Bg.log&&this.Bg.log(a))};Uf.prototype.warn=function(a){this.Bg&&(this.Bg.warn?this.Bg.warn(a):this.Bg.log&&this.Bg.log(a))};Uf.prototype.debug=function(){};_.Vf=new Uf;
_.Hg=function(a){if(!a)return"";if(/^about:(?:blank|srcdoc)$/.test(a))return window.origin||"";a.indexOf("blob:")===0&&(a=a.substring(5));a=a.split("#")[0].split("?")[0];a=a.toLowerCase();a.indexOf("//")==0&&(a=window.location.protocol+a);/^[\w\-]*:\/\//.test(a)||(a=window.location.href);var b=a.substring(a.indexOf("://")+3),c=b.indexOf("/");c!=-1&&(b=b.substring(0,c));c=a.substring(0,a.indexOf("://"));if(!c)throw Error("s`"+a);if(c!=="http"&&c!=="https"&&c!=="chrome-extension"&&c!=="moz-extension"&&
c!=="file"&&c!=="android-app"&&c!=="chrome-search"&&c!=="chrome-untrusted"&&c!=="chrome"&&c!=="app"&&c!=="devtools")throw Error("t`"+c);a="";var d=b.indexOf(":");if(d!=-1){var e=b.substring(d+1);b=b.substring(0,d);if(c==="http"&&e!=="80"||c==="https"&&e!=="443")a=":"+e}return c+"://"+b+a};
var qh;_.ph=function(a){_.Xa.setTimeout(function(){throw a;},0)};qh=0;_.rh=function(a){return Object.prototype.hasOwnProperty.call(a,_.$a)&&a[_.$a]||(a[_.$a]=++qh)};
_.sh=function(){return _.Oc("Safari")&&!(_.Yc()||(_.Qc()?0:_.Oc("Coast"))||_.Rc()||_.Uc()||_.Vc()||_.Wc()||_.Xc()||_.Oc("Silk")||_.Oc("Android"))};_.th=function(){return _.Oc("Android")&&!(_.Yc()||_.Xc()||_.Rc()||_.Oc("Silk"))};_.uh=_.Xc();_.vh=_.ed()||_.Oc("iPod");_.wh=_.Oc("iPad");_.xh=_.th();_.yh=_.Yc();_.zh=_.sh()&&!_.fd();
var Yi;_.Xi=function(a,b){b=(0,_.ob)(a,b);var c;(c=b>=0)&&Array.prototype.splice.call(a,b,1);return c};_.Zi=function(a,b){for(var c,d,e=1;e<arguments.length;e++){d=arguments[e];for(c in d)a[c]=d[c];for(var f=0;f<Yi.length;f++)c=Yi[f],Object.prototype.hasOwnProperty.call(d,c)&&(a[c]=d[c])}};Yi="constructor hasOwnProperty isPrototypeOf propertyIsEnumerable toLocaleString toString valueOf".split(" ");_.$i=[];_.aj=[];_.bj=!1;
_.cj=function(a){_.$i[_.$i.length]=a;if(_.bj)for(var b=0;b<_.aj.length;b++)a((0,_.Ab)(_.aj[b].wrap,_.aj[b]))};
var Pj=function(a){this.T=a};_.g=Pj.prototype;_.g.value=function(){return this.T};_.g.Ne=function(a){this.T.width=a;return this};_.g.Rb=function(){return this.T.width};_.g.Td=function(a){this.T.height=a;return this};_.g.Mc=function(){return this.T.height};_.g.Fi=function(a){this.T.style=a;return this};_.g.getStyle=function(){return this.T.style};_.Qj=function(a){this.T=a||{}};_.g=_.Qj.prototype;_.g.value=function(){return this.T};_.g.setUrl=function(a){this.T.url=a;return this};_.g.getUrl=function(){return this.T.url};_.g.Fi=function(a){this.T.style=a;return this};_.g.getStyle=function(){return this.T.style};_.g.Me=function(a){this.T.id=a;return this};_.g.getId=function(){return this.T.id};_.g.fn=function(a){this.T.rpctoken=a;return this};_.Rj=function(a,b){a.T.messageHandlers=b;return a};_.Sj=function(a,b){a.T.messageHandlersFilter=b;return a};
_.g=_.Qj.prototype;_.g.cs=_.ib(4);_.g.getContext=function(){return this.T.context};_.g.kd=function(){return this.T.openerIframe};_.g.Wn=function(){this.T.attributes=this.T.attributes||{};return new Pj(this.T.attributes)};_.g.Sz=_.ib(5);
var Xj;_.Tj=function(a){var b={},c;for(c in a)b[c]=a[c];return b};Xj=function(){for(var a;a=Uj.remove();){try{a.Qh.call(a.scope)}catch(b){_.ph(b)}Vj.put(a)}Wj=!1};_.Yj=function(a){if(!(a instanceof Array)){a=_.za(a);for(var b,c=[];!(b=a.next()).done;)c.push(b.value);a=c}return a};_.Zj=function(){};_.ak=function(a){a.prototype.$goog_Thenable=!0};_.bk=function(a){if(!a)return!1;try{return!!a.$goog_Thenable}catch(b){return!1}};
_.ck=typeof AsyncContext!=="undefined"&&typeof AsyncContext.Snapshot==="function"?function(a){return a&&AsyncContext.Snapshot.wrap(a)}:function(a){return a};var dk=function(a,b){this.X8=a;this.Efa=b;this.LB=0;this.MA=null};dk.prototype.get=function(){if(this.LB>0){this.LB--;var a=this.MA;this.MA=a.next;a.next=null}else a=this.X8();return a};dk.prototype.put=function(a){this.Efa(a);this.LB<100&&(this.LB++,a.next=this.MA,this.MA=a)};_.ek=function(a){return a};_.cj(function(a){_.ek=a});var fk=function(){this.hE=this.Ss=null};fk.prototype.add=function(a,b){var c=Vj.get();c.set(a,b);this.hE?this.hE.next=c:this.Ss=c;this.hE=c};fk.prototype.remove=function(){var a=null;this.Ss&&(a=this.Ss,this.Ss=this.Ss.next,this.Ss||(this.hE=null),a.next=null);return a};var Vj=new dk(function(){return new gk},function(a){return a.reset()}),gk=function(){this.next=this.scope=this.Qh=null};gk.prototype.set=function(a,b){this.Qh=a;this.scope=b;this.next=null};
gk.prototype.reset=function(){this.next=this.scope=this.Qh=null};var hk,Wj,Uj,ik;Wj=!1;Uj=new fk;_.jk=function(a,b){hk||ik();Wj||(hk(),Wj=!0);Uj.add(a,b)};ik=function(){var a=Promise.resolve(void 0);hk=function(){a.then(Xj)}};var mk,nk,ok,Ck,Gk,Ek,Hk;_.lk=function(a,b){this.Ca=0;this.nf=void 0;this.cq=this.Dl=this.Fb=null;this.CA=this.qG=!1;if(a!=_.Zj)try{var c=this;a.call(b,function(d){kk(c,2,d)},function(d){kk(c,3,d)})}catch(d){kk(this,3,d)}};mk=function(){this.next=this.context=this.Jr=this.Rv=this.zn=null;this.ly=!1};mk.prototype.reset=function(){this.context=this.Jr=this.Rv=this.zn=null;this.ly=!1};nk=new dk(function(){return new mk},function(a){a.reset()});
ok=function(a,b,c){var d=nk.get();d.Rv=a;d.Jr=b;d.context=c;return d};_.pk=function(a){if(a instanceof _.lk)return a;var b=new _.lk(_.Zj);kk(b,2,a);return b};_.qk=function(a){return new _.lk(function(b,c){c(a)})};_.sk=function(a,b,c){rk(a,b,c,null)||_.jk(_.bb(b,a))};_.tk=function(a){return new _.lk(function(b,c){var d=a.length,e=[];if(d)for(var f=function(m,n){d--;e[m]=n;d==0&&b(e)},h=function(m){c(m)},k,l=0;l<a.length;l++)k=a[l],_.sk(k,_.bb(f,l),h);else b(e)})};
_.vk=function(){var a,b,c=new _.lk(function(d,e){a=d;b=e});return new uk(c,a,b)};_.lk.prototype.then=function(a,b,c){return wk(this,(0,_.ck)(typeof a==="function"?a:null),(0,_.ck)(typeof b==="function"?b:null),c)};_.ak(_.lk);var yk=function(a,b,c,d){xk(a,ok(b||_.Zj,c||null,d))};_.lk.prototype.finally=function(a){var b=this;a=(0,_.ck)(a);return new Promise(function(c,d){yk(b,function(e){a();c(e)},function(e){a();d(e)})})};_.lk.prototype.KD=function(a,b){return wk(this,null,(0,_.ck)(a),b)};
_.lk.prototype.catch=_.lk.prototype.KD;_.lk.prototype.cancel=function(a){if(this.Ca==0){var b=new _.zk(a);_.jk(function(){Ak(this,b)},this)}};
var Ak=function(a,b){if(a.Ca==0)if(a.Fb){var c=a.Fb;if(c.Dl){for(var d=0,e=null,f=null,h=c.Dl;h&&(h.ly||(d++,h.zn==a&&(e=h),!(e&&d>1)));h=h.next)e||(f=h);e&&(c.Ca==0&&d==1?Ak(c,b):(f?(d=f,d.next==c.cq&&(c.cq=d),d.next=d.next.next):Bk(c),Ck(c,e,3,b)))}a.Fb=null}else kk(a,3,b)},xk=function(a,b){a.Dl||a.Ca!=2&&a.Ca!=3||Dk(a);a.cq?a.cq.next=b:a.Dl=b;a.cq=b},wk=function(a,b,c,d){var e=ok(null,null,null);e.zn=new _.lk(function(f,h){e.Rv=b?function(k){try{var l=b.call(d,k);f(l)}catch(m){h(m)}}:f;e.Jr=c?
function(k){try{var l=c.call(d,k);l===void 0&&k instanceof _.zk?h(k):f(l)}catch(m){h(m)}}:h});e.zn.Fb=a;xk(a,e);return e.zn};_.lk.prototype.Xha=function(a){this.Ca=0;kk(this,2,a)};_.lk.prototype.Yha=function(a){this.Ca=0;kk(this,3,a)};
var kk=function(a,b,c){a.Ca==0&&(a===c&&(b=3,c=new TypeError("Promise cannot resolve to itself")),a.Ca=1,rk(c,a.Xha,a.Yha,a)||(a.nf=c,a.Ca=b,a.Fb=null,Dk(a),b!=3||c instanceof _.zk||Ek(a,c)))},rk=function(a,b,c,d){if(a instanceof _.lk)return yk(a,b,c,d),!0;if(_.bk(a))return a.then(b,c,d),!0;if(_.ub(a))try{var e=a.then;if(typeof e==="function")return Fk(a,e,b,c,d),!0}catch(f){return c.call(d,f),!0}return!1},Fk=function(a,b,c,d,e){var f=!1,h=function(l){f||(f=!0,c.call(e,l))},k=function(l){f||(f=!0,
d.call(e,l))};try{b.call(a,h,k)}catch(l){k(l)}},Dk=function(a){a.qG||(a.qG=!0,_.jk(a.Az,a))},Bk=function(a){var b=null;a.Dl&&(b=a.Dl,a.Dl=b.next,b.next=null);a.Dl||(a.cq=null);return b};_.lk.prototype.Az=function(){for(var a;a=Bk(this);)Ck(this,a,this.Ca,this.nf);this.qG=!1};Ck=function(a,b,c,d){if(c==3&&b.Jr&&!b.ly)for(;a&&a.CA;a=a.Fb)a.CA=!1;if(b.zn)b.zn.Fb=null,Gk(b,c,d);else try{b.ly?b.Rv.call(b.context):Gk(b,c,d)}catch(e){Hk.call(null,e)}nk.put(b)};
Gk=function(a,b,c){b==2?a.Rv.call(a.context,c):a.Jr&&a.Jr.call(a.context,c)};Ek=function(a,b){a.CA=!0;_.jk(function(){a.CA&&Hk.call(null,b)})};Hk=_.ph;_.zk=function(a){_.jb.call(this,a);this.VZ=!1};_.cb(_.zk,_.jb);_.zk.prototype.name="cancel";var uk=function(a,b,c){this.promise=a;this.resolve=b;this.reject=c};
_.Ik=function(a){return new _.lk(a)};
var Qk=function(){this.vx={PZ:Jk?"../"+Jk:null,lz:Kk,FU:Lk,rsa:Mk,io:Nk,ita:Ok};this.Sf=_.ye;this.gZ=this.e9;this.W9=/MSIE\s*[0-8](\D|$)/.test(window.navigator.userAgent);if(this.vx.PZ){this.Sf=this.vx.FU(this.Sf,this.vx.PZ);var a=this.Sf.document,b=a.createElement("script");b.setAttribute("type","text/javascript");b.text="window.doPostMsg=function(w,s,o) {window.setTimeout(function(){w.postMessage(s,o);},0);};";a.body.appendChild(b);this.gZ=this.Sf.doPostMsg}this.nN={};this.QN={};a=(0,_.Ab)(this.NH,
this);_.Oe(this.Sf,"message",a);_.Ee(_.Pe,"RPMQ",[]).push(a);this.Sf!=this.Sf.parent&&Pk(this,this.Sf.parent,this.fJ(this.Sf.name),"*")};Qk.prototype.fJ=function(a){return'{"h":"'+escape(a)+'"}'};var Rk=function(a){var b=null;a.indexOf('{"h":"')===0&&a.indexOf('"}')===a.length-2&&(b=unescape(a.substring(6,a.length-2)));return b},Sk=function(a){if(!/^\s*{/.test(a))return!1;a=_.Qf(a);return a!==null&&typeof a==="object"&&!!a.g};
Qk.prototype.NH=function(a){var b=String(a.data);_.Vf.debug("gapix.rpc.receive("+Mk+"): "+(!b||b.length<=512?b:b.substr(0,512)+"... ("+b.length+" bytes)"));var c=b.indexOf("!_")!==0;c||(b=b.substring(2));var d=Sk(b);if(!c&&!d){if(!d&&(c=Rk(b))){if(this.nN[c])this.nN[c]();else this.QN[c]=1;return}var e=a.origin,f=this.vx.lz;this.W9?_.ye.setTimeout(function(){f(b,e)},0):f(b,e)}};Qk.prototype.Hb=function(a,b){a===".."||this.QN[a]?(b(),delete this.QN[a]):this.nN[a]=b};
var Pk=function(a,b,c,d){var e=Sk(c)?"":"!_";_.Vf.debug("gapix.rpc.send("+Mk+"): "+(!c||c.length<=512?c:c.substr(0,512)+"... ("+c.length+" bytes)"));a.gZ(b,e+c,d)};Qk.prototype.e9=function(a,b,c){a.postMessage(b,c)};Qk.prototype.send=function(a,b,c){(a=this.vx.FU(this.Sf,a))&&!a.closed&&Pk(this,a,b,c)};var Tk,Uk,Vk,Wk,Xk,Yk,Zk,Jk,Mk,$k,al,bl,Lk,Nk,dl,el,jl,kl,ml,Ok,ol,nl,fl,gl,pl,Kk,ql,rl;Tk=0;Uk=[];Vk={};Wk={};Xk=_.ye.location.href;Yk=_.Je(Xk,"rpctoken");Zk=_.Je(Xk,"parent")||_.Be.referrer;Jk=_.Je(Xk,"rly");Mk=Jk||(_.ye!==_.ye.top||_.ye.opener)&&_.ye.name||"..";$k=null;al={};bl=function(){};_.cl={send:bl,Hb:bl,fJ:bl};
Lk=function(a,b){var c=a;b.charAt(0)=="/"&&(b=b.substring(1),c=_.ye.top);if(b.length===0)return c;for(b=b.split("/");b.length;){a=b.shift();a.charAt(0)=="{"&&a.charAt(a.length-1)=="}"&&(a=a.substring(1,a.length-1));var d=a;if(d==="..")c=c==c.parent?c.opener:c.parent;else if(d!==".."&&c.frames[d]){var e=c;a=d;c=c.frames[d];if(!("postMessage"in c))if(c instanceof HTMLIFrameElement&&"contentWindow"in c)c=c.contentWindow!=null&&"postMessage"in c.contentWindow?c.contentWindow:null;else{d=null;e=_.za(e.document.getElementsByTagName("iframe"));
for(var f=e.next();!f.done;f=e.next())if(f=f.value,f.getAttribute("id")==a||f.getAttribute("name")==a)d=f;if(d&&"contentWindow"in d)c=d.contentWindow!=null?d.contentWindow:null;else throw Error("F`"+c+"`"+a);}}else return null}return c};Nk=function(a){return(a=Vk[a])&&a.token};dl=function(a){if(a.f in{})return!1;var b=a.t,c=Vk[a.r];a=a.origin;return c&&(c.token===b||!c.token&&!b)&&(a===c.origin||c.origin==="*")};
el=function(a){var b=a.id.split("/"),c=b[b.length-1],d=a.origin;return function(e){var f=e.origin;return e.f==c&&(d==f||d=="*")}};_.hl=function(a,b,c){a=fl(a);Wk[a.name]={Qh:b,Fv:a.Fv,Qs:c||dl};gl()};_.il=function(a){a=fl(a);delete Wk[a.name]};jl={};kl=function(a,b){(a=jl["_"+a])&&a[1](this)&&a[0].call(this,b)};ml=function(a){var b=a.c;if(!b)return bl;var c=a.r,d=a.g?"legacy__":"";return function(){var e=[].slice.call(arguments,0);e.unshift(c,d+"__cb",null,b);_.ll.apply(null,e)}};
Ok=function(a){$k=a};ol=function(a){al[a]||(al[a]=_.ye.setTimeout(function(){al[a]=!1;nl(a)},0))};nl=function(a){var b=Vk[a];if(b&&b.ready){var c=b.tK;for(b.tK=[];c.length;)_.cl.send(a,_.Rf(c.shift()),b.origin)}};fl=function(a){return a.indexOf("legacy__")===0?{name:a.substring(8),Fv:!0}:{name:a,Fv:!1}};
gl=function(){for(var a=_.ai("rpc/residenceSec")||60,b=(new Date).getTime()/1E3,c,d=0;c=Uk[d];++d){var e=c.kp;if(!e||a>0&&b-c.timestamp>a)Uk.splice(d,1),--d;else{var f=e.s,h=Wk[f]||Wk["*"];if(h)if(Uk.splice(d,1),--d,e.origin=c.origin,c=ml(e),e.callback=c,h.Qs(e)){if(f!=="__cb"&&!!h.Fv!=!!e.g)break;e=h.Qh.apply(e,e.a);e!==void 0&&c(e)}else _.Vf.debug("gapix.rpc.rejected("+Mk+"): "+f)}}};pl=function(a,b,c){Uk.push({kp:a,origin:b,timestamp:(new Date).getTime()/1E3});c||gl()};
Kk=function(a,b){a=_.Qf(a);pl(a,b,!1)};ql=function(a){for(;a.length;)pl(a.shift(),this.origin,!0);gl()};rl=function(a){var b=!1;a=a.split("|");var c=a[0];c.indexOf("/")>=0&&(b=!0);return{id:c,origin:a[1]||"*",BI:b}};
_.sl=function(a,b,c,d){var e=rl(a);d&&(_.ye.frames[e.id]=_.ye.frames[e.id]||d);a=e.id;if(!Vk.hasOwnProperty(a)){c=c||null;d=e.origin;if(a==="..")d=_.Hg(Zk),c=c||Yk;else if(!e.BI){var f=_.Be.getElementById(a);f&&(f=f.src,d=_.Hg(f),c=c||_.Je(f,"rpctoken"))}e.origin==="*"&&d||(d=e.origin);Vk[a]={token:c,tK:[],origin:d,Qfa:b,HZ:function(){var h=a;Vk[h].ready=1;nl(h)}};_.cl.Hb(a,Vk[a].HZ)}return Vk[a].HZ};
_.ll=function(a,b,c,d){a=a||"..";_.sl(a);a=a.split("|",1)[0];var e=b,f=a,h=[].slice.call(arguments,3),k=c,l=Mk,m=Yk,n=Vk[f],p=l,q=rl(f);if(n&&f!==".."){if(q.BI){if(!(m=Vk[f].Qfa)){m=$k?$k.substring(1).split("/"):[Mk];p=m.length-1;for(f=_.ye.parent;f!==_.ye.top;){var t=f.parent;if(!p--){for(var v=null,u=t.frames.length,x=0;x<u;++x)t.frames[x]==f&&(v=x);m.unshift("{"+v+"}")}f=t}m="/"+m.join("/")}p=m}else p=l="..";m=n.token}k&&q?(n=dl,q.BI&&(n=el(q)),jl["_"+ ++Tk]=[k,n],k=Tk):k=null;h={s:e,f:l,r:p,t:m,
c:k,a:h};e=fl(e);h.s=e.name;h.g=e.Fv;Vk[a].tK.push(h);ol(a)};if(typeof _.ye.postMessage==="function"||typeof _.ye.postMessage==="object")_.cl=new Qk,_.hl("__cb",kl,function(){return!0}),_.hl("_processBatch",ql,function(){return!0}),_.sl("..");
var km;
km=function(){function a(k,l){k=window.getComputedStyle(k,"").getPropertyValue(l).match(/^([0-9]+)/);return parseInt(k[0],10)}for(var b=0,c=[document.body];c.length>0;){var d=c.shift(),e=d.childNodes;if(typeof d.style!=="undefined"){var f=d.style.overflowY;f||(f=(f=document.defaultView.getComputedStyle(d,null))?f.overflowY:null);if(f!="visible"&&f!="inherit"&&(f=d.style.height,f||(f=(f=document.defaultView.getComputedStyle(d,null))?f.height:""),f.length>0&&f!="auto"))continue}for(d=0;d<e.length;d++){f=e[d];
if(typeof f.offsetTop!=="undefined"&&typeof f.offsetHeight!=="undefined"){var h=f.offsetTop+f.offsetHeight+a(f,"margin-bottom");b=Math.max(b,h)}c.push(f)}}return b+a(document.body,"border-bottom")+a(document.body,"margin-bottom")+a(document.body,"padding-bottom")};
_.lm=function(){var a=0;self.innerHeight?a=self.innerHeight:document.documentElement&&document.documentElement.clientHeight?a=document.documentElement.clientHeight:document.body&&(a=document.body.clientHeight);var b=document.body,c=document.documentElement;if(document.compatMode==="CSS1Compat"&&c.scrollHeight)return c.scrollHeight!==a?c.scrollHeight:c.offsetHeight;if(navigator.userAgent.indexOf("AppleWebKit")>=0)return km();if(b&&c){var d=c.scrollHeight,e=c.offsetHeight;c.clientHeight!==e&&(d=b.scrollHeight,
e=b.offsetHeight);return d>a?d>e?d:e:d<e?d:e}};
var mm=function(a,b){return _.bi(a,b,!0)},nm=function(a){this.T=a||{}},om=function(a){var b=function(c){return new (a().Context)(c)};b.prototype.addOnConnectHandler=function(c,d,e,f){return a().Context.prototype.addOnConnectHandler.apply(this,[c,d,e,f])};b.prototype.addOnOpenerHandler=function(c,d,e){return a().Context.prototype.addOnOpenerHandler.apply(this,[c,d,e])};b.prototype.closeSelf=function(c,d,e){return a().Context.prototype.closeSelf.apply(this,[c,d,e])};b.prototype.connectIframes=function(c,
d){a().Context.prototype.connectIframes.apply(this,[c,d])};b.prototype.getFrameName=function(){return a().Context.prototype.getFrameName.apply(this)};b.prototype.getGlobalParam=function(c){a().Context.prototype.getGlobalParam.apply(this,[c])};b.prototype.getParentIframe=function(){return a().Context.prototype.getParentIframe.apply(this)};b.prototype.getWindow=function(){return a().Context.prototype.getWindow.apply(this)};b.prototype.isDisposed=function(){return a().Context.prototype.isDisposed.apply(this)};
b.prototype.open=function(c,d){return a().Context.prototype.open.apply(this,[c,d])};b.prototype.openChild=function(c){return a().Context.prototype.openChild.apply(this,[c])};b.prototype.ready=function(c,d,e,f){a().Context.prototype.ready.apply(this,[c,d,e,f])};b.prototype.removeOnConnectHandler=function(c){a().Context.prototype.removeOnConnectHandler.apply(this,[c])};b.prototype.restyleSelf=function(c,d,e){return a().Context.prototype.restyleSelf.apply(this,[c,d,e])};b.prototype.setCloseSelfFilter=
function(c){a().Context.prototype.setCloseSelfFilter.apply(this,[c])};b.prototype.setGlobalParam=function(c,d){a().Context.prototype.setGlobalParam.apply(this,[c,d])};b.prototype.setRestyleSelfFilter=function(c){a().Context.prototype.setRestyleSelfFilter.apply(this,[c])};return b},pm=function(a){var b=function(c,d,e,f){return new (a().Iframe)(c,d,e,f)};b.prototype.applyIframesApi=function(c){a().Iframe.prototype.applyIframesApi(c)};b.prototype.close=function(c,d){return a().Iframe.prototype.close.apply(this,
[c,d])};b.prototype.getContext=function(){return a().Iframe.prototype.getContext.apply(this,[])};b.prototype.getFrameName=function(){return a().Iframe.prototype.getFrameName.apply(this,[])};b.prototype.getId=function(){return a().Iframe.prototype.getId.apply(this,[])};b.prototype.getIframeEl=function(){return a().Iframe.prototype.getIframeEl.apply(this,[])};b.prototype.getOrigin=function(){return a().Iframe.prototype.getOrigin.apply(this,[])};b.prototype.getParam=function(c){a().Iframe.prototype.getParam.apply(this,
[c])};b.prototype.getSiteEl=function(){return a().Iframe.prototype.getSiteEl.apply(this,[])};b.prototype.getWindow=function(){return a().Iframe.prototype.getWindow.apply(this,[])};b.prototype.isDisposed=function(){return a().Iframe.prototype.isDisposed.apply(this,[])};b.prototype.ping=function(c,d){return a().Iframe.prototype.ping.apply(this,[c,d])};b.prototype.register=function(c,d,e){a().Iframe.prototype.register.apply(this,[c,d,e])};b.prototype.registerWasClosed=function(c,d){a().Iframe.prototype.registerWasClosed.apply(this,
[c,d])};b.prototype.registerWasRestyled=function(c,d){a().Iframe.prototype.registerWasRestyled.apply(this,[c,d])};b.prototype.restyle=function(c,d){return a().Iframe.prototype.restyle.apply(this,[c,d])};b.prototype.send=function(c,d,e,f){return a().Iframe.prototype.send.apply(this,[c,d,e,f])};b.prototype.setParam=function(c,d){a().Iframe.prototype.setParam.apply(this,[c,d])};b.prototype.setSiteEl=function(c){a().Iframe.prototype.setSiteEl.apply(this,[c])};b.prototype.unregister=function(c,d){a().Iframe.prototype.unregister.apply(this,
[c,d])};return b},qm,rm,vm,xm,Cm,Km,Lm,Nm,Rm,Sm,Vm,Xm,Ym,$m,Zm,an;_.Qj.prototype.Sz=_.lb(5,function(){return this.T.controller});_.Qj.prototype.cs=_.lb(4,function(a){this.T.apis=a;return this});qm=function(a,b){a.T.onload=b};rm=function(a){return a.T.rpctoken};_.sm=function(a,b){a.T.queryParams=b;return a};_.tm=function(a,b){a.T.relayOpen=b;return a};_.um=function(a,b){a.T.onClose=b;return a};vm=function(a,b){a.T.controllerData=b};_.wm=function(a){a.T.waitForOnload=!0};
xm=function(a){return(a=a.T.timeout)?a:null};_.ym=function(a){return!!a&&typeof a==="object"&&_.De.test(a.push)};_.zm=function(a){for(var b=0;b<this.length;b++)if(this[b]===a)return b;return-1};_.Am=function(a,b,c){if(a){_.tl(_.ym(a),"arrayForEach was called with a non array value");for(var d=0;d<a.length;d++)b.call(c,a[d],d)}};
_.Bm=function(a,b,c){if(a)if(_.ym(a))_.Am(a,b,c);else{_.tl(typeof a==="object","objectForEach was called with a non object value");c=c||a;for(var d in a)_.Ge(a,d)&&a[d]!==void 0&&b.call(c,a[d],d)}};Cm=function(a){this.T=a||{}};Cm.prototype.value=function(){return this.T};Cm.prototype.getIframe=function(){return this.T.iframe};var Dm=function(a,b){a.T.role=b;return a},Em=function(a,b){a.T.data=b;return a};Cm.prototype.Tk=function(a){this.T.setRpcReady=a;return this};var Fm=function(a){return a.T.setRpcReady};
Cm.prototype.fn=function(a){this.T.rpctoken=a;return this};var Gm=function(a){a.T.selfConnect=!0;return a};nm.prototype.value=function(){return this.T};var Im=function(a){var b=new Hm;b.T.role=a;return b};nm.prototype.uU=function(){return this.T.role};nm.prototype.Dc=function(a){this.T.handler=a;return this};nm.prototype.wb=function(){return this.T.handler};var Jm=function(a,b){a.T.filter=b;return a};nm.prototype.cs=function(a){this.T.apis=a;return this};Nm=/^[\w\.\-]*$/;
_.Om=function(a){return a.getOrigin()===a.getContext().getOrigin()};_.Pm=function(){return!0};_.Qm=function(a){for(var b=_.Fe(),c=0;c<a.length;c++)b[a[c]]=!0;return function(d){return!!b[d.Bd]}};Rm=function(a,b,c){a=Km[a];if(!a)return[];for(var d=[],e=0;e<a.length;e++)d.push(_.pk(a[e].call(c,b,c)));return d};Sm=function(a,b,c){return function(d){if(!b.isDisposed()){var e=this.origin,f=b.getOrigin();_.tl(e===f,"Wrong origin "+e+" != "+f);e=this.callback;d=Rm(a,d,b);!c&&d.length>0&&_.tk(d).then(e)}}};
_.Tm=function(a,b,c){_.tl(a!="_default","Cannot update default api");Lm[a]={map:b,filter:c}};_.Um=function(a,b,c){_.tl(a!="_default","Cannot update default api");_.Ee(Lm,a,{map:{},filter:_.Om}).map[b]=c};Vm=function(a,b){_.Ee(Lm,"_default",{map:{},filter:_.Pm}).map[a]=b;_.Bm(_.Mm.Xf,function(c){c.register(a,b,_.Pm)})};_.Wm=function(){return _.Mm};Xm=/^https?:\/\/[^\/%\\?#\s]+$/i;
Ym={longdesc:!0,name:!0,src:!0,frameborder:!0,marginwidth:!0,marginheight:!0,scrolling:!0,align:!0,height:!0,width:!0,id:!0,"class":!0,title:!0,tabindex:!0,hspace:!0,vspace:!0,allowtransparency:!0};$m=function(a){this.resolve=this.reject=null;this.promise=_.Ik((0,_.Ab)(function(b,c){this.resolve=b;this.reject=c},this));a&&(this.promise=Zm(this.promise,a))};Zm=function(a,b){return a.then(function(c){try{b(c)}catch(d){}return c})};an=function(a){this.dg=a;this.Context=om(a);this.Iframe=pm(a)};_.g=an.prototype;
_.g.CROSS_ORIGIN_IFRAMES_FILTER=function(a){return this.dg().CROSS_ORIGIN_IFRAMES_FILTER(a)};_.g.SAME_ORIGIN_IFRAMES_FILTER=function(a){return this.dg().SAME_ORIGIN_IFRAMES_FILTER(a)};_.g.create=function(a,b,c){return this.dg().create(a,b,c)};_.g.getBeforeOpenStyle=function(a){return this.dg().getBeforeOpenStyle(a)};_.g.getContext=function(){return this.dg().getContext()};_.g.getStyle=function(a){return this.dg().getStyle(a)};_.g.makeWhiteListIframesFilter=function(a){return this.dg().makeWhiteListIframesFilter(a)};
_.g.registerBeforeOpenStyle=function(a,b){return this.dg().registerBeforeOpenStyle(a,b)};_.g.registerIframesApi=function(a,b,c){return this.dg().registerIframesApi(a,b,c)};_.g.registerIframesApiHandler=function(a,b,c){return this.dg().registerIframesApiHandler(a,b,c)};_.g.registerStyle=function(a,b){return this.dg().registerStyle(a,b)};var bn=function(){this.zi=[]};bn.prototype.dg=function(a){return this.zi.length?cn(this.zi[0],a):void 0};var cn=function(a,b){b=b===void 0?function(c){return new c}:b;return a.ctor?b(a.ctor):a.instance},dn=function(){bn.apply(this,arguments)};_.y(dn,bn);var fn=function(a){var b=en.sR,c=a.priority,d=~mm(b.zi,function(e){return e.priority<c?-1:1});b.zi.splice(d,0,a)};var en=new function(){var a=this;this.sR=new dn;this.instance=new an(function(){return a.sR.dg()()})};fn({instance:function(){return window.gapi.iframes},priority:1});_.gn=en.instance;var hn,jn;hn={height:!0,width:!0};jn=/^(?!-*(?:expression|(?:moz-)?binding))(?:[.#]?-?(?:[_a-z0-9-]+)(?:-[_a-z0-9-]+)*-?|-?(?:[0-9]+(?:\.[0-9]*)?|\.[0-9]+)(?:[a-z]{1,2}|%)?|!important|)$/i;_.kn=function(a){typeof a==="number"&&(a=String(a)+"px");return a};var ln=function(){Cm.apply(this,arguments)};_.y(ln,Cm);var Hm=function(){nm.apply(this,arguments)};_.y(Hm,nm);var mn=function(){_.Qj.apply(this,arguments)};_.y(mn,_.Qj);var nn=function(a){mn.call(this,a)};_.y(nn,mn);var on=function(a,b){a.T.frameName=b;return a};nn.prototype.getFrameName=function(){return this.T.frameName};var pn=function(a,b){a.T.rpcAddr=b;return a};nn.prototype.kg=function(){return this.T.rpcAddr};var qn=function(a,b){a.T.retAddr=b;return a};_.g=nn.prototype;_.g.Yh=function(){return this.T.retAddr};_.g.Nj=function(a){this.T.origin=a;return this};_.g.getOrigin=function(){return this.T.origin};_.g.Tk=function(a){this.T.setRpcReady=a;return this};
_.g.wp=function(a){this.T.context=a};var rn=function(a,b){a.T._rpcReadyFn=b};nn.prototype.getIframeEl=function(){return this.T.iframeEl};var sn=function(a,b,c){var d=a.kg(),e=b.Yh();qn(pn(c,a.Yh()+"/"+b.kg()),e+"/"+d);on(c,b.getFrameName()).Nj(b.getOrigin())};var un=function(a,b,c){a.setTimeout(function(){b.closed||c==5?tn(b):(b.close(),c++,un(a,b,c))},1E3)},tn=function(a){a.closed||a.document&&a.document.body&&_.we(a.document.body,"Please close this window.")};_.vn=function(a,b,c,d){this.Jg=!1;this.qb=a;this.PK=b;this.uq=c;this.Ja=d;this.i_=this.Ja.Yh();this.Bd=this.Ja.getOrigin();this.hca=this.Ja.getIframeEl();this.Y0=this.Ja.T.where;this.zi=[];this.applyIframesApi("_default");a=this.Ja.T.apis||[];for(b=0;b<a.length;b++)this.applyIframesApi(a[b]);this.qb.Xf[c]=this};_.g=_.vn.prototype;_.g.isDisposed=function(){return this.Jg};
_.g.dispose=function(){if(!this.isDisposed()){for(var a=0;a<this.zi.length;a++)this.unregister(this.zi[a]);delete _.Mm.Xf[this.getFrameName()];this.Jg=!0}};_.g.getContext=function(){return this.qb};_.g.getOptions=function(){return this.Ja};_.g.kg=function(){return this.PK};_.g.Yh=function(){return this.i_};_.g.getFrameName=function(){return this.uq};_.g.getIframeEl=function(){return this.hca};_.g.getSiteEl=function(){return this.Y0};_.g.setSiteEl=function(a){this.Y0=a};_.g.Tk=function(){(0,this.Ja.T._rpcReadyFn)()};
_.g.setParam=function(a,b){this.Ja.value()[a]=b};_.g.getParam=function(a){return this.Ja.value()[a]};_.g.vc=function(){return this.Ja.value()};_.g.getId=function(){return this.Ja.getId()};_.g.getOrigin=function(){return this.Bd};var wn=function(a,b){var c=a.uq;a=a.qb.getFrameName();return c+":"+a+":"+b};_.g=_.vn.prototype;
_.g.register=function(a,b,c){_.tl(!this.isDisposed(),"Cannot register handler on disposed iframe "+a);_.tl((c||_.Om)(this),"Rejecting untrusted message "+a);c=wn(this,a);_.Ee(Km,c,[]).push(b)==1&&(this.zi.push(a),_.hl(c,Sm(c,this,a==="_g_wasClosed")))};_.g.unregister=function(a,b){var c=wn(this,a),d=Km[c];d&&(b?(b=_.zm.call(d,b),b>=0&&d.splice(b,1)):d.splice(0,d.length),d.length==0&&(b=_.zm.call(this.zi,a),b>=0&&this.zi.splice(b,1),_.il(c)))};_.g.jaa=function(){return this.zi};
_.g.applyIframesApi=function(a){this.VE=this.VE||[];if(!(_.zm.call(this.VE,a)>=0)){this.VE.push(a);a=Lm[a]||{map:{}};for(var b in a.map)_.Ge(a.map,b)&&this.register(b,a.map[b],a.filter)}};_.g.getWindow=function(){if(!_.Om(this))return null;var a=this.Ja.T._popupWindow;if(a)return a;var b=this.PK.split("/");a=this.getContext().getWindow();for(var c=0;c<b.length&&a;c++){var d=b[c];a=".."===d?a==a.parent?a.opener:a.parent:a.frames[d]}return a};
var xn=function(a){var b={};if(a)for(var c in a)_.Ge(a,c)&&_.Ge(hn,c)&&jn.test(a[c])&&(b[c]=a[c]);return b};_.g=_.vn.prototype;_.g.close=function(a,b){return yn(this,"_g_close",a,b)};_.g.restyle=function(a,b){return yn(this,"_g_restyle",a,b)};_.g.Vr=function(a,b){return yn(this,"_g_restyleDone",a,b)};_.g.I8=function(a){return this.getContext().closeSelf(a,void 0,this)};_.g.Mfa=function(a){if(a&&typeof a==="object")return this.getContext().restyleSelf(a,void 0,this)};
_.g.Nfa=function(a){var b=this.Ja.T.onRestyle;b&&b.call(this,a,this);a=a&&typeof a==="object"?xn(a):{};(b=this.getIframeEl())&&a&&typeof a==="object"&&(_.Ge(a,"height")&&(a.height=_.kn(a.height)),_.Ge(a,"width")&&(a.width=_.kn(a.width)),_.He(a,b.style))};
_.g.J8=function(a){var b=this.Ja.T.onClose;b&&b.call(this,a,this);if(b=this.getOptions().T._popupWindow){var c=this.getContext().getWindow().document.getElementById(this.getId());c&&c.parentNode&&c.parentNode.removeChild(c);c=this.getContext().getWindow();_.Gd&&_.zh&&c?(c.focus(),un(c,b,0)):(b.close(),tn(b))}b||(b=this.getIframeEl())&&b.parentNode&&b.parentNode.removeChild(b);if(b=this.Ja.Sz())c={},c.frameName=this.getFrameName(),yn(b,"_g_disposeControl",c);b=wn(this,"_g_wasClosed");Rm(b,a,this)};
_.g.registerWasRestyled=function(a,b){this.register("_g_wasRestyled",a,b)};_.g.registerWasClosed=function(a,b){this.register("_g_wasClosed",a,b)};_.g.sia=function(){delete this.getContext().Xf[this.getFrameName()];this.getContext().getWindow().setTimeout((0,_.Ab)(function(){this.dispose()},this),0)};
_.g.send=function(a,b,c,d){_.tl(!this.isDisposed(),"Cannot send message to disposed iframe - "+a);_.tl((d||_.Om)(this),"Wrong target for message "+a);c=new $m(c);a=this.qb.getFrameName()+":"+this.uq+":"+a;_.ll(this.PK,a,c.resolve,b);return c.promise};var yn=function(a,b,c,d){return a.send(b,c,d,_.Pm)};_.g=_.vn.prototype;_.g.Mea=function(a){return a};_.g.ping=function(a,b){return yn(this,"_g_ping",b,a)};
_.g.T8=function(a){a=a&&typeof a==="object"?a:{};for(var b=a.rpcAddr,c=(this.kg()+"/"+b).split("/"),d=this.getContext().getWindow(),e;(e=c.shift())&&d;)d=e==".."?d.parent:d.frames[e];_.tl(!!d,"Bad rpc address "+b);a._window=d;a._parentRpcAddr=this.kg();a._parentRetAddr=this.Yh();this.getContext();b=new _.zn(a);this.bea&&this.bea(b,a.controllerData);this.HF=this.HF||[];this.HF.push(b,a.controllerData)};
_.g.j9=function(a){a=(a||{}).frameName;for(var b=this.HF||[],c=0;c<b.length;c++)if(b[c].getFrameName()===a){a=b.splice(c,1)[0];a.dispose();this.fea&&this.fea(a);return}_.tl(!1,"Unknown contolled iframe to dispose - "+a)};
_.g.P8=function(a){var b=new nn(a);a=new ln(b.value());if(a.T.selfConnect)var c=this;else(_.tl(Xm.test(b.getOrigin()),"Illegal origin for connected iframe - "+b.getOrigin()),c=this.getContext().Xf[b.getFrameName()],c)?Fm(b)&&(c.Tk(),yn(c,"_g_rpcReady")):(b=on(qn(pn(new nn,b.kg()),b.Yh()).Nj(b.getOrigin()),b.getFrameName()).Tk(Fm(b)).fn(rm(b)),c=this.getContext().attach(b.value()));b=this.getContext();var d=a.T.role;a=a.T.data;An(b);d=d||"";_.Ee(b.FF,d,[]).push({iframe:c,data:a});Bn(c,a,b.HJ[d])};
_.g.tM=function(a,b){(new nn(b)).T._relayedDepth||(b={},Gm(Dm(new ln(b),"_opener")),yn(a,"_g_connect",b))};
_.g.zY=function(a){var b=this,c=a.T.messageHandlers,d=a.T.messageHandlersFilter,e=a.T.onClose;_.um(_.Sj(_.Rj(a,null),null),null);return yn(this,"_g_open",a.value()).then(function(f){var h=new nn(f[0]),k=h.getFrameName();f=new nn;var l=b.Yh(),m=h.Yh();qn(pn(f,b.kg()+"/"+h.kg()),m+"/"+l);on(f,k);f.Nj(h.getOrigin());f.cs(h.T.apis);f.fn(rm(a));_.Rj(f,c);_.Sj(f,d);_.um(f,e);(h=b.getContext().Xf[k])||(h=b.getContext().attach(f.value()));return h})};
_.g.SK=function(a){var b=a.getUrl();_.tl(!b||_.Nl.test(b),"Illegal url for new iframe - "+b);var c=a.Wn().value();b={};for(var d in c)_.Ge(c,d)&&_.Ge(Ym,d)&&(b[d]=c[d]);_.Ge(c,"style")&&(d=c.style,typeof d==="object"&&(b.style=xn(d)));a.value().attributes=b};
_.g.xea=function(a){a=new nn(a);this.SK(a);var b=a.T._relayedDepth||0;a.T._relayedDepth=b+1;a.T.openerIframe=this;var c=rm(a);a.fn(null);var d=this;return this.getContext().open(a.value()).then(function(e){var f=(new nn(e.vc())).T.apis,h=new nn;sn(e,d,h);b==0&&Dm(new ln(h.value()),"_opener");h.Tk(!0);h.fn(c);yn(e,"_g_connect",h.value());h=new nn;on(qn(pn(h,e.kg()),e.i_),e.getFrameName()).Nj(e.getOrigin()).cs(f);return h.value()})};
_.g.Lfa=function(a){this.getContext().addOnOpenerHandler(function(b){b.send("_g_wasRestyled",a,void 0,_.Pm)},null,_.Pm)};var Gn;_.Cn=_.Fe();_.Dn=_.Fe();_.En=function(a,b){_.Cn[a]=b};_.Fn=function(a){return _.Cn[a]};Gn=function(a,b){_.Ie.load("gapi.iframes.style."+a,b)};_.Hn=function(a,b){_.Dn[a]=b};_.In=function(a){return _.Dn[a]};_.zn=function(a){a=a||{};this.Jg=!1;this.xi=_.Fe();this.Xf=_.Fe();this.Sf=a._window||_.ye;this.Hd=this.Sf.location.href;this.QY=(this.bK=Jn(this.Hd,"parent"))?Jn(this.Hd,"pfname"):"";this.Da=this.bK?Jn(this.Hd,"_gfid")||Jn(this.Hd,"id"):"";this.uq=_.$l(this.Da,this.QY);this.Bd=_.Hg(this.Hd);if(this.Da){var b=new nn;pn(b,a._parentRpcAddr||"..");qn(b,a._parentRetAddr||this.Da);b.Nj(_.Hg(this.bK||this.Hd));on(b,this.QY);this.Fb=this.attach(b.value())}else this.Fb=null};_.g=_.zn.prototype;
_.g.isDisposed=function(){return this.Jg};_.g.dispose=function(){if(!this.isDisposed()){for(var a=_.za(Object.values(this.Xf)),b=a.next();!b.done;b=a.next())b.value.dispose();this.Jg=!0}};_.g.getFrameName=function(){return this.uq};_.g.getOrigin=function(){return this.Bd};_.g.getWindow=function(){return this.Sf};_.g.ub=function(){return this.Sf.document};_.g.setGlobalParam=function(a,b){this.xi[a]=b};_.g.getGlobalParam=function(a){return this.xi[a]};
_.g.attach=function(a){_.tl(!this.isDisposed(),"Cannot attach iframe in disposed context");a=new nn(a);a.kg()||pn(a,a.getId());a.Yh()||qn(a,"..");a.getOrigin()||a.Nj(_.Hg(a.getUrl()));a.getFrameName()||on(a,_.$l(a.getId(),this.uq));var b=a.getFrameName();if(this.Xf[b])return this.Xf[b];var c=a.kg(),d=c;a.getOrigin()&&(d=c+"|"+a.getOrigin());var e=a.Yh(),f=rm(a);f||(f=(f=a.getIframeEl())&&(f.getAttribute("data-postorigin")||f.src)||a.getUrl(),f=_.Je(f,"rpctoken"));rn(a,_.sl(d,e,f,a.T._popupWindow));
d=((window.gadgets||{}).rpc||{}).setAuthToken;f&&d&&d(c,f);var h=new _.vn(this,c,b,a),k=a.T.messageHandlersFilter;_.Bm(a.T.messageHandlers,function(l,m){h.register(m,l,k)});Fm(a)&&h.Tk();yn(h,"_g_rpcReady");return h};_.g.SK=function(a){on(a,null);var b=a.getId();!b||Nm.test(b)&&!this.getWindow().document.getElementById(b)||(_.Vf.log("Ignoring requested iframe ID - "+b),a.Me(null))};var Jn=function(a,b){var c=_.Je(a,b);c||(c=_.Qf(_.Je(a,"jcp",""))[b]);return c||""};
_.zn.prototype.openChild=function(a){_.tl(!this.isDisposed(),"Cannot open iframe in disposed context");var b=new nn(a);Kn(this,b);var c=b.getFrameName();if(c&&this.Xf[c])return this.Xf[c];this.SK(b);c=b.getUrl();_.tl(c,"No url for new iframe");var d=b.T.queryParams||{};d.usegapi="1";_.sm(b,d);d=this.jV&&this.jV(c,b);d||(d=b.T.where,_.tl(!!d,"No location for new iframe"),c=_.jm(c,d,a),b.T.iframeEl=c,d=c.getAttribute("id"));pn(b,d).Me(d);b.Nj(_.Hg(b.T.eurl||""));this.vX&&this.vX(b,b.getIframeEl());
c=this.attach(a);c.tM&&c.tM(c,a);(a=b.T.onCreate)&&a(c);b.T.disableRelayOpen||c.applyIframesApi("_open");return c};
var Ln=function(a,b,c){var d=b.T.canvasUrl;if(!d)return c;_.tl(!b.T.allowPost&&!b.T.forcePost,"Post is not supported when using canvas url");var e=b.getUrl();_.tl(e&&_.Hg(e)===a.Bd&&_.Hg(d)===a.Bd,"Wrong origin for canvas or hidden url "+d);b.setUrl(d);_.wm(b);b.T.canvasUrl=null;return function(f){var h=f.getWindow(),k=h.location.hash;k=_.im(e)+(/#/.test(e)?k.replace(/^#/,"&"):k);h.location.replace(k);c&&c(f)}},Mn=function(a,b,c){var d=b.T.relayOpen;if(d){var e=a.getParentIframe();d instanceof _.vn?
(e=d,_.tm(b,0)):Number(d)>0&&_.tm(b,Number(d)-1);if(e){_.tl(!!e.zY,"Relaying iframe open is disabled");if(d=b.getStyle())if(d=_.Dn[d])b.wp(a),d(b.value()),b.wp(null);b.T.openerIframe=null;c.resolve(e.zY(b));return!0}}return!1},Nn=function(a,b,c){var d=b.getStyle();if(d)if(_.tl(!!_.Fn,"Defer style is disabled, when requesting style "+d),_.Cn[d])Kn(a,b);else return Gn(d,function(){_.tl(!!_.Cn[d],"Fail to load style - "+d);c.resolve(a.open(b.value()))}),!0;return!1};
_.zn.prototype.open=function(a,b){_.tl(!this.isDisposed(),"Cannot open iframe in disposed context");var c=new nn(a);b=Ln(this,c,b);var d=new $m(b);(b=c.getUrl())&&c.setUrl(_.im(b));if(Mn(this,c,d)||Nn(this,c,d)||Mn(this,c,d))return d.promise;if(xm(c)!=null){var e=setTimeout(function(){h.getIframeEl().src="about:blank";d.reject({timeout:"Exceeded time limit of :"+xm(c)+"milliseconds"})},xm(c)),f=d.resolve;d.resolve=function(k){clearTimeout(e);f(k)}}c.T.waitForOnload&&qm(c.Wn(),function(){d.resolve(h)});
var h=this.openChild(a);c.T.waitForOnload||d.resolve(h);return d.promise};_.zn.prototype.getParentIframe=function(){return this.Fb};var On=function(a,b){var c=a.getParentIframe(),d=!0;b.filter&&(d=b.filter.call(b.iframe,b.params));return _.pk(d).then(function(e){return e&&c?(b.OY&&b.OY.call(a,b.params),e=b.sender?b.sender(b.params):yn(c,b.message,b.params),b.qia?e.then(function(){return!0}):!0):!1})};_.g=_.zn.prototype;
_.g.closeSelf=function(a,b,c){a=On(this,{sender:function(d){var e=_.Mm.getParentIframe();_.Bm(_.Mm.Xf,function(f){f!==e&&yn(f,"_g_wasClosed",d)});return yn(e,"_g_closeMe",d)},message:"_g_closeMe",params:a,iframe:c,filter:this.getGlobalParam("onCloseSelfFilter")});b=new $m(b);b.resolve(a);return b.promise};_.g.restyleSelf=function(a,b,c){a=a||{};b=new $m(b);b.resolve(On(this,{message:"_g_restyleMe",params:a,iframe:c,filter:this.getGlobalParam("onRestyleSelfFilter"),qia:!0,OY:this.n2}));return b.promise};
_.g.n2=function(a){a.height==="auto"&&(a.height=_.lm())};_.g.setCloseSelfFilter=function(a){this.setGlobalParam("onCloseSelfFilter",a)};_.g.setRestyleSelfFilter=function(a){this.setGlobalParam("onRestyleSelfFilter",a)};var Kn=function(a,b){var c=b.getStyle();if(c){b.Fi(null);var d=_.Cn[c];_.tl(d,"No such style: "+c);b.wp(a);d(b.value());b.wp(null)}};
_.zn.prototype.ready=function(a,b,c,d){var e=b||{},f=this.getParentIframe();this.addOnOpenerHandler(function(k){_.Bm(e,function(l,m){k.register(m,l,d)},this);k!==f&&k.send("_ready",h,void 0,d)},void 0,d);var h=a||{};h.height=h.height||"auto";this.n2(h);f&&f.send("_ready",h,c,_.Pm)};
_.zn.prototype.connectIframes=function(a,b){a=new ln(a);var c=new ln(b),d=Fm(a);b=a.getIframe();var e=c.getIframe();if(e){var f=rm(a),h=new nn;sn(b,e,h);Em(Dm((new ln(h.value())).fn(f),a.T.role),a.T.data).Tk(d);var k=new nn;sn(e,b,k);Em(Dm((new ln(k.value())).fn(f),c.T.role),c.T.data).Tk(!0);yn(b,"_g_connect",h.value(),function(){d||yn(e,"_g_connect",k.value())});d&&yn(e,"_g_connect",k.value())}else c={},Em(Dm(Gm(new ln(c)),a.T.role),a.T.data),yn(b,"_g_connect",c)};
var An=function(a){a.FF||(a.FF=_.Fe(),a.HJ=_.Fe())};_.zn.prototype.addOnConnectHandler=function(a,b,c,d){An(this);typeof a==="object"?(b=new Hm(a),c=b.uU()||""):(b=Jm(Im(a).Dc(b).cs(c),d),c=a);d=this.FF[c]||[];a=!1;for(var e=0;e<d.length&&!a;e++)Bn(this.Xf[d[e].iframe.getFrameName()],d[e].data,[b]),a=b.T.runOnce;c=_.Ee(this.HJ,c,[]);a||b.T.dontWait||c.push(b)};
_.zn.prototype.removeOnConnectHandler=function(a,b){a=_.Ee(this.HJ,a,[]);if(b)for(var c=!1,d=0;!c&&d<a.length;d++)a[d].wb()===b&&(c=!0,a.splice(d,1));else a.splice(0,a.length)};var Bn=function(a,b,c){c=c||[];for(var d=0;d<c.length;d++){var e=c[d];if(e&&a){var f=e.T.filter||_.Om;if(a&&f(a)){f=e.T.apis||[];for(var h=0;h<f.length;h++)a.applyIframesApi(f[h]);e.wb()&&e.wb()(a,b);e.T.runOnce&&(c.splice(d,1),--d)}}}};
_.zn.prototype.addOnOpenerHandler=function(a,b,c){var d=this.addOnConnectHandler;a=Jm(Im("_opener").Dc(a).cs(b),c);a.T.runOnce=!0;d.call(this,a.value())};_.zn.prototype.vX=function(a,b){var c=a.Sz();if(c){_.tl(c.Bd===a.getOrigin(),"Wrong controller origin "+this.Bd+" !== "+a.getOrigin());var d=a.kg();pn(a,c.kg());qn(a,c.Yh());var e=new nn;vm(pn(e,d),a.T.controllerData);_.Oe(b,"load",function(){c.send("_g_control",e.value())})}};
var Pn=function(a,b,c){a=a.getWindow();var d=a.document,e=c.T.reuseWindow;if(e){var f=c.getId();if(!f)throw Error("G");}else f=_.Zl(d,c);var h=f,k=c.T.rpcRelayUrl;if(k){k=_.hm(k);h=c.T.fragmentParams||{};h.rly=f;c.T.fragmentParams=h;h=c.T.where||d.body;_.tl(!!h,"Cannot open window in a page with no body");var l={};l.src=k;l.style="display:none;";l.id=f;l.name=f;_.cm(d,h,l,f);h=f+"_relay"}b=_.im(b);var m=_.am(d,b,f,c.value());c.T.eurl=m;b=c.T.openAsWindow;typeof b!=="string"&&(b=void 0);c=window.navigator.userAgent||
"";/Trident|MSIE/i.test(c)&&/#/.test(c)&&(m="javascript:window.location.replace("+_.ye.JSON.stringify(m).replace(/#/g,"\\x23")+")");if(e){var n=e;setTimeout(function(){n.location.replace(m)})}else n=_.Ic(a,m,h,b);return{id:f,S2:n}};_.zn.prototype.jV=function(a,b){if(b.T.openAsWindow){a=Pn(this,a,b);var c=a.id;_.tl(!!a.S2,"Open popup window failed");b.T._popupWindow=a.S2}return c};Km=_.Fe();Lm=_.Fe();_.Mm=new _.zn;Vm("_g_rpcReady",_.vn.prototype.Tk);Vm("_g_discover",_.vn.prototype.jaa);Vm("_g_ping",_.vn.prototype.Mea);Vm("_g_close",_.vn.prototype.I8);Vm("_g_closeMe",_.vn.prototype.J8);Vm("_g_restyle",_.vn.prototype.Mfa);Vm("_g_restyleMe",_.vn.prototype.Nfa);Vm("_g_wasClosed",_.vn.prototype.sia);_.Um("control","_g_control",_.vn.prototype.T8);_.Um("control","_g_disposeControl",_.vn.prototype.j9);var Qn=_.Mm.getParentIframe();
Qn&&Qn.register("_g_restyleDone",_.vn.prototype.Lfa,_.Pm);Vm("_g_connect",_.vn.prototype.P8);var Rn={};Rn._g_open=_.vn.prototype.xea;_.Tm("_open",Rn,_.Pm);var Sn={Context:_.zn,Iframe:_.vn,SAME_ORIGIN_IFRAMES_FILTER:_.Om,CROSS_ORIGIN_IFRAMES_FILTER:_.Pm,makeWhiteListIframesFilter:_.Qm,getContext:_.Wm,registerIframesApi:_.Tm,registerIframesApiHandler:_.Um,registerStyle:_.En,registerBeforeOpenStyle:_.Hn,getStyle:_.Fn,getBeforeOpenStyle:_.In,create:_.jm};fn({instance:function(){return Sn},priority:2});_.Um("gapi.load","_g_gapi.load",function(a){return new _.lk(function(b){_.Ie.load(a&&typeof a==="object"&&a.features||"",b)})});
_.Tn=function(a){this.T=a};_.g=_.Tn.prototype;_.g.gL=function(a){this.T.anchor=a;return this};_.g.cj=function(){return this.T.anchor};_.g.hL=function(a){this.T.anchorPosition=a};_.g.Td=function(a){this.T.height=a;return this};_.g.Mc=function(){return this.T.height};_.g.Ne=function(a){this.T.width=a;return this};_.g.Rb=function(){return this.T.width};_.g.setZIndex=function(a){this.T.zIndex=a;return this};_.g.getZIndex=function(){return this.T.zIndex};
_.Un=function(a){a.T.connectWithQueryParams=!0;return a};
_.r("gapi.iframes.create",_.jm);
_.r("gapi.iframes.registerStyle",_.En);_.r("gapi.iframes.registerBeforeOpenStyle",_.Hn);_.r("gapi.iframes.getStyle",_.Fn);_.r("gapi.iframes.getBeforeOpenStyle",_.In);_.r("gapi.iframes.registerIframesApi",_.Tm);_.r("gapi.iframes.registerIframesApiHandler",_.Um);_.r("gapi.iframes.getContext",_.Wm);_.r("gapi.iframes.SAME_ORIGIN_IFRAMES_FILTER",_.Om);_.r("gapi.iframes.CROSS_ORIGIN_IFRAMES_FILTER",_.Pm);_.r("gapi.iframes.makeWhiteListIframesFilter",_.Qm);_.r("gapi.iframes.Context",_.zn);
_.r("gapi.iframes.Context.prototype.isDisposed",_.zn.prototype.isDisposed);_.r("gapi.iframes.Context.prototype.getWindow",_.zn.prototype.getWindow);_.r("gapi.iframes.Context.prototype.getFrameName",_.zn.prototype.getFrameName);_.r("gapi.iframes.Context.prototype.getGlobalParam",_.zn.prototype.getGlobalParam);_.r("gapi.iframes.Context.prototype.setGlobalParam",_.zn.prototype.setGlobalParam);_.r("gapi.iframes.Context.prototype.open",_.zn.prototype.open);
_.r("gapi.iframes.Context.prototype.openChild",_.zn.prototype.openChild);_.r("gapi.iframes.Context.prototype.getParentIframe",_.zn.prototype.getParentIframe);_.r("gapi.iframes.Context.prototype.closeSelf",_.zn.prototype.closeSelf);_.r("gapi.iframes.Context.prototype.restyleSelf",_.zn.prototype.restyleSelf);_.r("gapi.iframes.Context.prototype.setCloseSelfFilter",_.zn.prototype.setCloseSelfFilter);_.r("gapi.iframes.Context.prototype.setRestyleSelfFilter",_.zn.prototype.setRestyleSelfFilter);
_.r("gapi.iframes.Context.prototype.addOnConnectHandler",_.zn.prototype.addOnConnectHandler);_.r("gapi.iframes.Context.prototype.removeOnConnectHandler",_.zn.prototype.removeOnConnectHandler);_.r("gapi.iframes.Context.prototype.addOnOpenerHandler",_.zn.prototype.addOnOpenerHandler);_.r("gapi.iframes.Context.prototype.connectIframes",_.zn.prototype.connectIframes);_.r("gapi.iframes.Iframe",_.vn);_.r("gapi.iframes.Iframe.prototype.isDisposed",_.vn.prototype.isDisposed);
_.r("gapi.iframes.Iframe.prototype.getContext",_.vn.prototype.getContext);_.r("gapi.iframes.Iframe.prototype.getFrameName",_.vn.prototype.getFrameName);_.r("gapi.iframes.Iframe.prototype.getId",_.vn.prototype.getId);_.r("gapi.iframes.Iframe.prototype.register",_.vn.prototype.register);_.r("gapi.iframes.Iframe.prototype.unregister",_.vn.prototype.unregister);_.r("gapi.iframes.Iframe.prototype.send",_.vn.prototype.send);_.r("gapi.iframes.Iframe.prototype.applyIframesApi",_.vn.prototype.applyIframesApi);
_.r("gapi.iframes.Iframe.prototype.getIframeEl",_.vn.prototype.getIframeEl);_.r("gapi.iframes.Iframe.prototype.getSiteEl",_.vn.prototype.getSiteEl);_.r("gapi.iframes.Iframe.prototype.setSiteEl",_.vn.prototype.setSiteEl);_.r("gapi.iframes.Iframe.prototype.getWindow",_.vn.prototype.getWindow);_.r("gapi.iframes.Iframe.prototype.getOrigin",_.vn.prototype.getOrigin);_.r("gapi.iframes.Iframe.prototype.close",_.vn.prototype.close);_.r("gapi.iframes.Iframe.prototype.restyle",_.vn.prototype.restyle);
_.r("gapi.iframes.Iframe.prototype.restyleDone",_.vn.prototype.Vr);_.r("gapi.iframes.Iframe.prototype.registerWasRestyled",_.vn.prototype.registerWasRestyled);_.r("gapi.iframes.Iframe.prototype.registerWasClosed",_.vn.prototype.registerWasClosed);_.r("gapi.iframes.Iframe.prototype.getParam",_.vn.prototype.getParam);_.r("gapi.iframes.Iframe.prototype.setParam",_.vn.prototype.setParam);_.r("gapi.iframes.Iframe.prototype.ping",_.vn.prototype.ping);_.r("gapi.iframes.Iframe.prototype.getOpenParams",_.vn.prototype.vc);
});
// Google Inc.
